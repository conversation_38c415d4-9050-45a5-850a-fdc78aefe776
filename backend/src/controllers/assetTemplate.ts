import { Request, Response } from "express";
import { PAGINATED_QUERY_PAGE_SIZE } from "~/constants/global";
import { IMachineTemplate } from "~/datamodels/MachineTemplate/interface";
import { getAllAssetTemplates, getAssetTemplate } from "~/services/assetTemplate/fetch";
import { controllerErrorHandler } from "~/utils/controllerErrorHandler";
import { serviceFnInputGenerator } from "~/utils/inputGenerator";
import APP_FEATURES from "$/settings/app-features.json";
import { getEnums } from "~/utils";

const appFeaturesEnum = getEnums(APP_FEATURES, "reference");

const project = (assetTemplates: IMachineTemplate[], isProductCatalogEnabled: boolean) =>
  assetTemplates.map((assetTemplate: IMachineTemplate) => ({
    title: assetTemplate.title,
    description: assetTemplate.description,
    ...(isProductCatalogEnabled
      ? { productId: assetTemplate.templateId }
      : { templateId: assetTemplate.templateId }),
    // @ts-ignore
    createdAt: assetTemplate.created_at,
    // @ts-ignore
    updatedAt: assetTemplate.updated_at,
  }));

export const getAllAssetTemplatesController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.params.limit = PAGINATED_QUERY_PAGE_SIZE;
    params.args.params.skip = (Number(params.args.query.page || 1) - 1) * PAGINATED_QUERY_PAGE_SIZE;
    const appConfig = await params.dataSources.AppConfig.getOne();
    const isProductCatalogEnabled = appConfig.features.includes(appFeaturesEnum.productCatalog);

    const assetTemplates: { templates: IMachineTemplate[]; totalCount: number } =
      (await getAllAssetTemplates(params)) as {
        templates: IMachineTemplate[];
        totalCount: number;
      };

    return res.status(200).json({
      data: project(assetTemplates.templates, isProductCatalogEnabled),
      totalPages: Math.ceil(assetTemplates.totalCount / PAGINATED_QUERY_PAGE_SIZE),
      page: Number(params.args.query.page || 1),
    });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const getAssetTemplateController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.templateId = params.args.params.id;
    const appConfig = await params.dataSources.AppConfig.getOne();
    const isProductCatalogEnabled = appConfig.features.includes(appFeaturesEnum.productCatalog);

    const assetTemplate = await getAssetTemplate(params);
    if (assetTemplate) {
      return res.status(200).json({
        data: project([assetTemplate], isProductCatalogEnabled)[0],
      });
    }

    return res
      .status(404)
      .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};
