import InvalidInputError from "~/errors/InvalidInputError";
import NotFoundError from "~/errors/NotFoundError";
import type { ServiceParamsType } from "~/types/common";

export const updateCustomerPortal = async ({ user, dataSources, args }: ServiceParamsType) => {
  const { input } = args || {};
  if (!input || !input._id) throw new InvalidInputError("Invalid Input");
  const { _id, assetAccess, productAccess } = input;

  const customerPortal = await dataSources.CustomerPortal.getOne({
    _id,
    oem: user.organization,
  });

  if (!customerPortal) throw new NotFoundError("Customer Portal not found");

  await dataSources.CustomerPortal.CustomerPortal.findOneAndUpdate(
    {
      _id,
    },
    {
      $set: {
        assetAccess: {
          documentation: assetAccess.documentation,
          parts: assetAccess.parts,
          preventiveMaintenance: assetAccess.preventiveMaintenance,
          qrCodes: assetAccess.qrCodes,
          _3dModel: assetAccess._3dModel,
          history: assetAccess.history,
        },
        productAccess: {
          allowCopying: productAccess.allowCopying,
          documentation: productAccess.documentation,
          parts: productAccess.parts,
          procedures: productAccess.procedures,
          _3dModel: productAccess._3dModel,
        },
      },
    },
  );

  const updatedCustomerPortal = await dataSources.CustomerPortal.getOne({
    _id,
    oem: user.organization,
  });
  return updatedCustomerPortal;
};
