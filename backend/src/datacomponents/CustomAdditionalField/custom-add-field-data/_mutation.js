import to from "await-to-js";
import { roles } from "~/directives";
import { ERROR } from "~/environment";
import { getEnums, reorderCustomFields, slugify, throwIfError } from "~/utils";
import { getCustomFieldDataSource } from "../helpers";
import { VisibilityScopeEnum } from "~/datamodels/CustomAdditionalField/schema";
import CUSTOM_FIELD_TYPE from "$/settings/enums/customAdditionalField/_custom-field-type.json";
import runJob from "~/agenda/run";
import { GENERATE_DOCUMENT_CUSTOM_FIELDS_JOB } from "~/agenda/constants";
import CustomFieldTypes from "$/settings/enums/customAdditionalField/_type.json";

const customFieldTypes = getEnums(CustomFieldTypes, "reference");

const CUSTOM_FIELD_TYPES = getEnums(CUSTOM_FIELD_TYPE, "reference");

export const mutationTypes = `
  type Mutation {
    createOwnOemCustomField(input: InputInsertCustomAdditionalField): CustomAdditionalField @${roles.is.oem}
    updateOwnOemCustomField(input: InputUpdateCustomAdditionalField): CustomAdditionalField @${roles.is.oem}
    deleteOwnOemCustomAdditionalField(input: InputDeleteOwnOemCustomAdditionalField): String @${roles.is.oem}
    activeInactiveOwnOemAdditionalField(_id: ID! enabled: Boolean!): CustomAdditionalField @${roles.is.oem}
    updateOwnOemCustomFieldsOrder(input: InputUpdateCustomFieldsOrder): String @${roles.is.oem}
  }
`;

export const mutationResolvers = {
  Mutation: {
    createOwnOemCustomField: async (_, args, { dataSources, user }) => {
      const { input } = args || {};
      if (!input) return null;
      const { label, type, fieldType, customFieldsWithOrder } = input;
      if (!label || !type || !fieldType) return null;
      const slug = slugify(label);
      const key = "value";
      const options = [...new Map(input?.options?.map(item => [item[key], item]) || []).values()];

      const order = customFieldsWithOrder?.length;

      // Set visibilityScope based on showOnCustomerPortal and remove the showOnCustomerPortal property
      const visibilityScope = input.showOnCustomerPortal
        ? VisibilityScopeEnum.EXTERNAL
        : VisibilityScopeEnum.INTERNAL;

      delete input.showOnCustomerPortal;
      input.visibilityScope = visibilityScope;

      const [err, newCustomAdditionalField] = await to(
        dataSources.CustomAdditionalField.save({
          ...input,
          options,
          slug,
          enabled: true,
          oem: user?.organization,
          createdBy: user?.id,
          order,
        }),
      );

      throwIfError(err?.code === 11000, "Custom field name already exists");
      throwIfError(err, ERROR.USER.BAD_USER_INPUT);

      if (newCustomAdditionalField.type === customFieldTypes.knowledgeBase) {
        const allDocuments = await dataSources.Document.getManyByQuery({
          oem: user.organization,
        });
        await Promise.all(
          allDocuments.map(doc =>
            runJob({
              jobName: GENERATE_DOCUMENT_CUSTOM_FIELDS_JOB,
              oemId: user.organization,
              documentId: doc._id,
              customFieldIdsToGenerate: [newCustomAdditionalField._id?.toString()],
            }),
          ),
        );
      }

      await reorderCustomFields({ customFieldsWithOrder, dataSources });

      return newCustomAdditionalField;
    },

    updateOwnOemCustomField: async (_, args, { dataSources, user }) => {
      const { input } = args || {};
      if (!input) return null;
      const customField = await dataSources.CustomAdditionalField.getById(input._id);

      if (customField?.oem?.toString() === user?.organization?.toString()) {
        if (input.label) input.slug = slugify(input.label);
        const key = "value";

        const options = [...new Map(input?.options?.map(item => [item[key], item]) || []).values()];

        // Set visibilityScope based on showOnCustomerPortal and remove the showOnCustomerPortal property
        const visibilityScope = input.showOnCustomerPortal
          ? VisibilityScopeEnum.EXTERNAL
          : VisibilityScopeEnum.INTERNAL;

        delete input.showOnCustomerPortal;
        input.visibilityScope = visibilityScope;

        const [err, updatedCustomField] = await to(
          dataSources.CustomAdditionalField.save({
            ...input,
            options,
            oem: customField.oem,
          }),
        );

        throwIfError(err?.code === 11000, "Custom field name already exists");
        throwIfError(err, ERROR.USER.BAD_USER_INPUT);

        if (options.length) {
          const updatedOptions = customField.options.filter(prevOption =>
            Boolean(
              options.find(
                option =>
                  prevOption._id.toString().includes(option._id?.toString()) &&
                  option.value !== prevOption.value,
              ),
            ),
          );

          const deletedOptions = customField.options.filter(
            prevOption =>
              !options.some(option => prevOption._id.toString().includes(option._id?.toString())),
          );

          const entity = getCustomFieldDataSource(input.type);
          if (updatedOptions.length) {
            for (const updatedOption of updatedOptions) {
              if (customField.fieldType === CUSTOM_FIELD_TYPES.multiSelect) {
                const allDocs = await dataSources[entity].getManyByQuery({
                  oem: user.organization,
                  "customFields.fieldId": customField._id,
                  "customFields.values": updatedOption.value,
                });
                const updatedValue = options.find(option =>
                  updatedOption._id.toString().includes(option._id?.toString()),
                ).value;

                Promise.all(
                  allDocs.map(doc => {
                    let updatedCustomFields = [...doc.customFields];
                    updatedCustomFields = updatedCustomFields.map(cf => {
                      if (cf.fieldId?.toString() === customField._id?.toString()) {
                        const updatedValues = (cf.values || []).filter(
                          value => value !== updatedOption.value,
                        );
                        updatedValues.push(updatedValue);
                        return {
                          ...cf,
                          values: updatedValues,
                        };
                      }
                      return cf;
                    });
                    return dataSources[entity].updateOne(
                      {
                        oem: user.organization,
                        _id: doc._id,
                      },
                      {
                        $set: {
                          customFields: updatedCustomFields,
                        },
                      },
                    );
                  }),
                );
              }
              const newValue = options.find(option =>
                updatedOption._id.toString().includes(option._id?.toString()),
              ).value;
              dataSources[entity].updateManyByQuery(
                {
                  oem: user.organization,
                  "customFields.fieldId": customField._id,
                  "customFields.value": updatedOption.value,
                },
                {
                  $set: { "customFields.$.value": newValue },
                },
              );
            }
          }

          if (deletedOptions.length) {
            for (const deletedOption of deletedOptions) {
              dataSources[entity].updateManyByQuery(
                {
                  oem: user.organization,
                  "customFields.fieldId": customField._id,
                  "customFields.value": deletedOption.value,
                },
                {
                  $pull: {
                    customFields: {
                      fieldId: customField._id,
                      value: deletedOption.value,
                    },
                  },
                },
              );
            }
          }
        }
        if (customField.fieldType !== input.fieldType) {
          if (
            customField.type === customFieldTypes.knowledgeBase &&
            input.fieldType === CUSTOM_FIELD_TYPES.multiSelect &&
            customField.fieldType === CUSTOM_FIELD_TYPES.singleSelect
          ) {
            const allDocuments = await dataSources.Document.getManyByQuery({
              oem: user.organization,
              "customFields.fieldId": customField._id,
            });
            const updatedDocuments = allDocuments.map(doc => ({
              ...doc,
              customFields: doc.customFields.map(cf => ({
                ...cf,
                ...(cf.fieldId?.toString() === customField._id?.toString()
                  ? {
                      values: cf.value ? [cf.value] : [],
                      value: null,
                    }
                  : {}),
              })),
            }));
            updatedDocuments.forEach(doc =>
              dataSources.Document.updateOne(
                {
                  oem: user.organization,
                  _id: doc._id,
                },
                {
                  $set: {
                    customFields: doc.customFields,
                  },
                },
              ),
            );
          }
        }
        if (customField.type === customFieldTypes.knowledgeBase) {
          const allDocuments = await dataSources.Document.getManyByQuery({
            oem: user.organization,
          });
          await Promise.all(
            allDocuments.map(doc =>
              runJob({
                jobName: GENERATE_DOCUMENT_CUSTOM_FIELDS_JOB,
                oemId: user.organization,
                documentId: doc._id,
                customFieldIdsToGenerate: [customField._id?.toString()],
              }),
            ),
          );
        }

        return updatedCustomField;
      }
      return null;
    },

    deleteOwnOemCustomAdditionalField: async (_, args, { dataSources, user }) => {
      const { input } = args || {};
      const { _id, customFieldsWithOrder } = input || {};
      if (!_id) return null;

      const customField = await dataSources.CustomAdditionalField.getById(_id);
      if (customField?.oem?.toString() !== user?.organization?.toString()) {
        return throwIfError(ERROR.USER.DO_NOT_HAVE_PERMISSION);
      }

      const [err] = await to(dataSources.CustomAdditionalField.softDeleteById(_id, user.id));
      throwIfError(err);

      if (customField.type === "tickets") {
        const oem = await dataSources.Oem.loadOne(user.organization);
        if (oem && oem.ticketTypes) {
          oem.ticketTypes.forEach(ticketType => {
            ticketType.customFields = ticketType.customFields.filter(
              cf => cf.customAdditionalField.toString() !== _id,
            );
          });

          // Update OEM with modified ticketTypes
          await dataSources.Oem.updateOne(
            { _id: user.organization },
            { $set: { ticketTypes: oem.ticketTypes } },
          );
        }
      }

      const entity = getCustomFieldDataSource(customField.type);

      dataSources[entity].updateManyByQuery(
        {
          oem: user.organization,
          "customFields.fieldId": customField._id,
        },
        {
          $pull: {
            customFields: {
              fieldId: customField._id,
            },
          },
        },
      );
      dataSources.User.updateManyByQuery(
        {
          oem: user.organization,
          "boards.fieldId": customField._id,
        },
        {
          $pull: {
            boards: {
              fieldIds: customField._id,
            },
          },
        },
      );

      await reorderCustomFields({ customFieldsWithOrder, dataSources });
      return "ok";
    },

    activeInactiveOwnOemAdditionalField: async (_, args, { dataSources, user }) => {
      const { _id, enabled } = args || {};
      if (!_id) return null;
      const customField = await dataSources.CustomAdditionalField.getById(_id);
      if (customField) {
        const isExists = await dataSources.CustomAdditionalField.getOne({
          slug: customField?.slug,
          type: customField?.type,
          oem: user.organization,
          deleted: false,
        });
        if (!isExists) {
          const newData = customField;
          delete newData?._id;
          // eslint-disable-next-line no-shadow
          const [err, updatedCustomField] = await to(
            dataSources.CustomAdditionalField.save({
              ...newData,
              enabled,
              oem: user.organization,
              createdBy: user.id,
            }),
          );
          throwIfError(err, ERROR.USER.BAD_USER_INPUT);
          return updatedCustomField;
        }
        const [err, updatedCustomField] = await to(
          dataSources.CustomAdditionalField.save({
            ...isExists,
            enabled: !isExists?.enabled,
          }),
        );
        throwIfError(err, ERROR.USER.BAD_USER_INPUT);
        return updatedCustomField;
      }
      return null;
    },

    updateOwnOemCustomFieldsOrder: async (_, args, { dataSources }) => {
      const { input } = args || {};
      const { customFieldsWithOrder } = input || {};

      await reorderCustomFields({ customFieldsWithOrder, dataSources });

      return "ok";
    },
  },
};
