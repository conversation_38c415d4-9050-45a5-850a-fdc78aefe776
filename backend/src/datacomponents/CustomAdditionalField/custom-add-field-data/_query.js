import { roles } from "~/directives";
import { getAllCustomFields } from "~/services/customFields";

export const queryTypes = `
  type Query {
    listAdditionalFields(type: Types): [AdditionalField] @${roles.is.technician}
    listOwnOemCustomFields(type: Types): [CustomAdditionalField] @${roles.is.technician}
  }
`;

export const queryResolvers = {
  Query: {
    listAdditionalFields: async (_, args, { dataSources }) => {
      const { type } = args || {};
      return await dataSources.CustomAdditionalField.getMany({
        $and: [
          {
            isAdditionalField: true,
            oem: { $exists: false },
            ...(type ? { type } : {}),
          },
        ],
        sort: ["created_at:asc"],
      });
    },

    listOwnOemCustomFields: async (_, args, { dataSources, user }) =>
      getAllCustomFields({
        args: { params: {}, query: { type: args.type }, files: null, headers: {} },
        user,
        dataSources,
      }),
  },
};
