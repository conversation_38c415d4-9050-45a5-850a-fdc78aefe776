import { describe, it, expect, vi, beforeEach } from "vitest";
import { getConnectionHistory, listOemCustomersInArea } from "./fetch";

describe("listOemCustomersInArea", () => {
  const mockDataSources = {
    Customer: {
      getMany: vi.fn(),
    },
  };

  const mockUser = {
    organization: "OEM_001",
  };

  const validCoordinates = [
    [73.0479, 33.6844],
    [73.0489, 33.6844],
    [73.0489, 33.6854],
    [73.0479, 33.6854],
    [73.0479, 33.6844],
  ];

  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("should fetch facilities within valid coordinates", async () => {
    const mockFacilities = [
      { _id: "1", name: "Facility A", customerAddress: { coordinates: [73.048, 33.684] } },
      { _id: "2", name: "Facility B", customerAddress: { coordinates: [73.049, 33.685] } },
    ];

    mockDataSources.Customer.getMany.mockResolvedValue(mockFacilities);

    const args = {
      params: {
        where: {
          coordinates: validCoordinates,
        },
      },
    };

    const result = await listOemCustomersInArea({
      args,
      dataSources: mockDataSources,
      user: mockUser,
    });

    expect(mockDataSources.Customer.getMany).toHaveBeenCalledWith(
      {
        oem: mockUser.organization,
        customerAddress: {
          $geoWithin: {
            $polygon: validCoordinates,
          },
        },
      },
      {
        select: { customerAddress: 1, _id: 1 },
      },
    );

    expect(result).toEqual(mockFacilities);
  });

  it("should return an no facilities [] if valid coordinates are provided but no facilities are found", async () => {
    mockDataSources.Customer.getMany.mockResolvedValue([]);

    const args = {
      params: {
        where: {
          coordinates: validCoordinates,
        },
      },
    };

    const result = await listOemCustomersInArea({
      args,
      dataSources: mockDataSources,
      user: mockUser,
    });

    expect(mockDataSources.Customer.getMany).toHaveBeenCalledWith(
      {
        oem: mockUser.organization,
        customerAddress: {
          $geoWithin: {
            $polygon: validCoordinates,
          },
        },
      },
      {
        select: { customerAddress: 1, _id: 1 },
      },
    );
    expect(result).toEqual([]);
  });

  it("should return an error if coordinates are missing", async () => {
    const args = {
      params: {
        where: {},
      },
    };

    await expect(
      listOemCustomersInArea({ args, dataSources: mockDataSources, user: mockUser }),
    ).rejects.toThrow("Missing valid set of coordinates");

    expect(mockDataSources.Customer.getMany).not.toHaveBeenCalled();
  });

  it("should return all facilities that match the provided coordinates", async () => {
    const mockFacilities = Array.from({ length: 50 }, (_, index) => ({
      _id: `${index}`,
      name: `Facility ${index + 1}`,
      customerAddress: { coordinates: [73.04 + index * 0.01, 33.68] },
    }));

    mockDataSources.Customer.getMany.mockResolvedValue(mockFacilities);

    const args = {
      params: {
        where: {
          coordinates: validCoordinates,
        },
      },
    };

    const result = await listOemCustomersInArea({
      args,
      dataSources: mockDataSources,
      user: mockUser,
    });

    expect(mockDataSources.Customer.getMany).toHaveBeenCalledWith(
      {
        oem: mockUser.organization,
        customerAddress: {
          $geoWithin: {
            $polygon: validCoordinates,
          },
        },
      },
      {
        select: { customerAddress: 1, _id: 1 },
      },
    );

    expect(result).toHaveLength(50);
    expect(result[0].name).toBe("Facility 1");
    expect(result[49].name).toBe("Facility 50");
  });

  it("should return an error if coordinates do not form a closed polygon", async () => {
    // 4 coords
    const invalidCoordinates = [
      [73.0479, 33.6844],
      [73.0489, 33.6844],
      [73.0489, 33.6854],
      [73.0479, 33.6854],
    ];

    const args = {
      params: {
        where: {
          coordinates: invalidCoordinates,
        },
      },
    };

    await expect(
      listOemCustomersInArea({ args, dataSources: mockDataSources, user: mockUser }),
    ).rejects.toThrow("Missing valid set of coordinates");

    expect(mockDataSources.Customer.getMany).not.toHaveBeenCalled();
  });

  it("should return an empty array if no facilities are found", async () => {
    mockDataSources.Customer.getMany.mockResolvedValue([]);

    const args = {
      params: {
        where: {
          coordinates: validCoordinates,
        },
      },
    };

    const result = await listOemCustomersInArea({
      args,
      dataSources: mockDataSources,
      user: mockUser,
    });

    expect(mockDataSources.Customer.getMany).toHaveBeenCalled();
    expect(result).toEqual([]);
  });

  it("should return an empty array if facilities is null", async () => {
    mockDataSources.Customer.getMany.mockResolvedValue(null);

    const args = {
      params: {
        where: {
          coordinates: validCoordinates,
        },
      },
    };

    const result = await listOemCustomersInArea({
      args,
      dataSources: mockDataSources,
      user: mockUser,
    });

    expect(mockDataSources.Customer.getMany).toHaveBeenCalled();
    expect(result).toEqual([]);
  });
});

describe("getConnectionHistory", () => {
  const mockDataSources = {
    Machine: {
      getMany: vi.fn(),
      Machine: {
        find: vi.fn(),
      },
    },
    MachineHistory: {
      countDocuments: vi.fn(),
      getMany: vi.fn(),
    },
  };

  const mockUser = {
    organization: "OEM_001",
  };

  const mockMachines = [{ _id: "machine1" }, { _id: "machine2" }];

  const mockHistory = [
    { _id: "h1", machine: "machine1", type: "Ticket", createdAt: new Date() },
    { _id: "h2", machine: "machine2", type: "Ticket", createdAt: new Date() },
  ];

  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("should return null if no customer is provided in the filter", async () => {
    const args = { params: { where: {} } };

    const result = await getConnectionHistory({
      args,
      dataSources: mockDataSources,
      user: mockUser,
    });

    expect(result).toBeNull();
    expect(mockDataSources.Machine.Machine.find).not.toHaveBeenCalled();
  });

  it("should fetch machine history for a valid customer", async () => {
    const args = {
      params: {
        where: { customer: "customer1" },
        skip: 0,
        limit: 10,
      },
    };

    mockDataSources.Machine.getMany.mockResolvedValue(mockMachines);
    mockDataSources.MachineHistory.countDocuments.mockResolvedValue(2);
    mockDataSources.MachineHistory.getMany.mockResolvedValue(mockHistory);

    const result = await getConnectionHistory({
      args,
      dataSources: mockDataSources,
      user: mockUser,
    });

    expect(mockDataSources.Machine.getMany).toHaveBeenCalledWith(
      {
        customer: "customer1",
        oem: "OEM_001",
        deleted: false,
      },
      {
        _id: 1,
      },
    );

    expect(mockDataSources.MachineHistory.countDocuments).toHaveBeenCalledWith({
      customer: "customer1",
      machine: { $in: ["machine1", "machine2"] },
      type: "Ticket",
      oem: "OEM_001",
      deleted: false,
    });

    expect(mockDataSources.MachineHistory.getMany).toHaveBeenCalledWith({
      customer: "customer1",
      machine: { $in: ["machine1", "machine2"] },
      type: "Ticket",
      oem: "OEM_001",
      deleted: false,
      limit: 10,
      skip: 0,
      sort: ["createdAt:desc", "_id:asc"],
    });

    expect(result).toEqual({
      history: mockHistory,
      limit: 10,
      skip: 0,
      currentPage: 1,
      machineCount: 2,
      totalCount: 2,
    });
  });

  it("should handle empty machine list gracefully", async () => {
    const args = {
      params: {
        where: { customer: "customer1" },
        skip: 0,
        limit: 10,
      },
    };

    mockDataSources.Machine.getMany.mockResolvedValue([]);
    mockDataSources.MachineHistory.countDocuments.mockResolvedValue(0);
    mockDataSources.MachineHistory.getMany.mockResolvedValue([]);

    const result = await getConnectionHistory({
      args,
      dataSources: mockDataSources,
      user: mockUser,
    });

    expect(result).toEqual({
      history: [],
      limit: 10,
      skip: 0,
      currentPage: 1,
      machineCount: 0,
      totalCount: 0,
    });
  });

  it("should calculate currentPage correctly with skip and limit", async () => {
    const args = {
      params: {
        where: { customer: "customer1" },
        skip: 20,
        limit: 10,
      },
    };

    mockDataSources.Machine.getMany.mockResolvedValue(mockMachines);
    mockDataSources.MachineHistory.countDocuments.mockResolvedValue(2);
    mockDataSources.MachineHistory.getMany.mockResolvedValue(mockHistory);

    const result = await getConnectionHistory({
      args,
      dataSources: mockDataSources,
      user: mockUser,
    });

    expect(result.currentPage).toBe(3); // (20 / 10) + 1
  });
});
