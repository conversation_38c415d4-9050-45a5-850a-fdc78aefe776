/* eslint-disable */
// @ts-nocheck

import { expect, test, vi } from "vitest";
import startAgenda from "~/agenda";

const {
  agendaCancel,
  agendaDefine,
  agendaEvery,
  agendaJobs,
  agendaOn,
  agendaSchedule,
  agendaStart,
  defineJob,
  jobFunc,
  jobName,
  pendingJobName,
  runJob,
} = vi.hoisted(() => {
  const _pendingJobName = "random-pending-job";

  return {
    agendaCancel: vi.fn(),
    agendaDefine: vi.fn(),
    agendaEvery: vi.fn(),
    agendaJobs: vi.fn(() => [
      {
        attrs: {
          name: _pendingJobName,
        },
      },
    ]),
    agendaOn: vi.fn(),
    agendaSchedule: vi.fn(),
    agendaStart: vi.fn(),
    defineJob: vi.fn(),
    jobFunc: vi.fn(),
    jobName: "random-job",
    pendingJobName: _pendingJobName,
    runJob: vi.fn(),
  };
});

vi.mock("agenda", () => {
  class Agenda {
    cancel = agendaCancel;

    define = agendaDefine;

    every = agendaEvery;

    jobs = agendaJobs;

    on = agendaOn;

    schedule = agendaSchedule;

    start = agendaStart;
  }
  return { default: Agenda };
});

vi.mock("~/agenda/definitions", () => ({
  definitions: {
    [pendingJobName]: {
      config: {},
      func: jobFunc,
    },
    [jobName]: {
      config: {},
      func: jobFunc,
    },
  },
  SCHEDULE_TICKET_CREATION_JOB: "SCHEDULE_TICKET_CREATION",
}));

vi.mock("~/agenda/helpers", () => ({
  defineJob,
}));

vi.mock("~/agenda/run", () => ({
  default: runJob,
}));

test("Agenda start", async () => {
  const dataSources = {};

  startAgenda(dataSources);

  expect(agendaOn).toHaveBeenCalledOnce();
  expect(agendaOn.mock.calls[0][0]).toEqual("ready");

  await agendaOn.mock.calls[0][1]();

  expect(agendaStart).toHaveBeenCalledOnce();
  expect(agendaCancel).toHaveBeenCalledWith({ nextRunAt: null });

  // should define pending jobs
  expect(defineJob).toHaveBeenCalledWith({
    dataSources,
    name: pendingJobName,
    processedDefinitions: new Set<string>(),
  });

  // should define non pending jobs
  expect(defineJob).toHaveBeenCalledWith({
    dataSources,
    name: jobName,
    processedDefinitions: new Set<string>(),
  });

  // should query for legacy jobs
  expect(agendaJobs.mock.calls[1][0]["data.eventId"]).toEqual({ $exists: true });
  expect(agendaJobs.mock.calls[1][0].name).toEqual({ $ne: "SCHEDULE_TICKET_CREATION" });

  // should cancel legacy jobs and run them on new flow
  expect(agendaCancel).toHaveBeenCalledWith({ name: pendingJobName });
  expect(runJob).toHaveBeenCalledWith({
    contextUser: undefined,
    eventId: undefined,
    jobName: "SCHEDULE_TICKET_CREATION",
    maintenanceOn: undefined,
  });
});
