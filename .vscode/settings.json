{
  "files.associations": {
    "*.jsx": "javascriptreact"
  },
  "files.insertFinalNewline": true,
  "eslint.format.enable": true,
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "eslint.validate": [
    "javascript",
    "typescript",
    "javascriptreact",
    "typescriptreact"
  ],
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "git.ignoreLimitWarning": true,
  "eslint.codeActionsOnSave.mode": "all",
  "editor.formatOnSaveMode": "file",
  "eslint.alwaysShowStatus": true,
  // "eslint.workingDirectories": ["backend"],
  "editor.formatOnPaste": true,
  "editor.tabSize": 2,
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit"
  },
  "typescript.tsdk": "backend/node_modules/typescript/lib"
}
