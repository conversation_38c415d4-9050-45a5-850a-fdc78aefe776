export default `#graphql
    input InputCreateDocument {
        title: String!
        size: Int!
        documentId: String!
    }

    input InputCreateDocuments {
        documents: [InputCreateDocument!]!
        runOCR: Boolean
    }

    input InputUpdateDocument {
        _id: ID!
        customFields: [InputCustomField]
    }

    input InputResultDocument {
        documentId: String
        documentTitle: String
        documentChunks: [String]
        answer: String
    }

    input InputUploadSearchFeedback {
        queryText: String
        resultDocuments: [InputResultDocument]
        description: String
    }
`;
