import { Request, Response } from "express";
import {
  nylasWebHookChallenge,
  processEmailFolderEvent,
  processEmailMessageEvent,
  processGrantEvent,
} from "~/services/email/webhooks";
import { getEmailAttachment } from "~/services/email/fetch";
import { logger, verifyWebhookSignature } from "~/utils";
import { serviceFnInputGenerator } from "~/utils/inputGenerator";

export const nylasWebHookChallengeController = (req: Request, res: Response) =>
  res.send(nylasWebHookChallenge(serviceFnInputGenerator(req, res)));

export const grantEventController = async (req: Request, res: Response) => {
  const parsedBody = JSON.parse(req.body.toString());

  try {
    const isRequestVerified = verifyWebhookSignature(
      req.body,
      process.env.NYLAS_EMAIL_WEBHOOK_SECRET!,
      req.headers["x-nylas-signature"] as string,
    );
    if (isRequestVerified) {
      if (parsedBody.data) {
        await processGrantEvent(serviceFnInputGenerator(req, res, true));
      }
      return res.send("ok");
    }
    return res.status(403).json({ success: false, error: "Invalid Authentication Header" });
  } catch (err) {
    logger.log("error", `nylas webhook error: ${err}`);
    return res.status(500).json({ success: false, error: err!.toString() });
  }
};

export const emailMessageEventController = async (req: Request, res: Response) => {
  try {
    const parsedBody = JSON.parse(req.body.toString());
    const isRequestVerified = verifyWebhookSignature(
      req.body,
      process.env.NYLAS_EMAIL_MESSAGE_WEBHOOK_SECRET!,
      req.headers["x-nylas-signature"] as string,
    );
    if (isRequestVerified) {
      if (parsedBody.data) {
        await processEmailMessageEvent(serviceFnInputGenerator(req, res, true));
      }
      return res.send("ok");
    }
    return res.status(403).json({ success: false, error: "Invalid Authentication Header" });
  } catch (err) {
    logger.log("error", `nylas webhook error: ${err}`);
    return res.status(500).json({ success: false, error: err!.toString() });
  }
};

export const emailFolderEventController = async (req: Request, res: Response) => {
  try {
    const parsedBody = JSON.parse(req.body.toString());
    const isRequestVerified = verifyWebhookSignature(
      req.body,
      process.env.NYLAS_EMAIL_FOLDER_WEBHOOK_SECRET!,
      req.headers["x-nylas-signature"] as string,
    );
    if (isRequestVerified) {
      if (parsedBody.data) {
        await processEmailFolderEvent(serviceFnInputGenerator(req, res, true));
      }
      return res.send("ok");
    }
    return res.status(403).json({ success: false, error: "Invalid Authentication Header" });
  } catch (err) {
    logger.log("error", `nylas webhook error: ${err}`);
    return res.status(500).json({ success: false, error: err });
  }
};

export const getEmailAttachmentController = async (req: Request, res: Response) => {
  try {
    const { emailAttachment, attachmentResponse } = await getEmailAttachment(
      serviceFnInputGenerator(req, res),
    );

    res.writeHead(200, {
      "Content-Disposition": `attachment; filename=${emailAttachment.filename}`,
      "Content-Type": emailAttachment.contentType,
    });

    attachmentResponse
      .pipe(res)
      .on("finish", () => {
        res.end();
      })
      .on("error", (error: string) => {
        logger.log("error", `nylas download file pipe error: ${error}`);
        res.status(500).send("Error streaming the attachment");
      });
  } catch (error) {
    logger.log("error", `nylas download file error: ${error}`);
    res.status(500).send("Error processing the request");
  }
};
