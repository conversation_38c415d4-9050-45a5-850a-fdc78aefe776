import { roles } from "~/directives";
import { IContext } from "~/types/common";
import {
  generateAnalyticsQuery,
  buildQueryParams,
  checkForTeamsEnable,
  entityTypes,
  generateTableValues,
} from "~/utils";
import { InputQueryParams } from "~/graphql/types";
import { IReport } from "~/datamodels/Report/interface";
import { IOem } from "~/datamodels/Oem/interface";
import { ITeam } from "~/datamodels/Team/interface";
import { ANALYTICS_AXIS_OPTIONS } from "~/config";
import { ICustomAdditionalField } from "~/datamodels/CustomAdditionalField/interface";

const { oem }: { oem?: string } = roles.is;

const collectionToTypeMap: any = {
  workOrder: "Ticket",
  facility: "Customer",
  machine: "Machine",
};

export const queryTypes = `#graphql
  type Query {
    getOwnOemReportByID(id: ID!): Report @${oem}
    getOwnOemAnalytics(input: InputGetAnalytics): Analytics @${oem}
    listAllOwnOemReports(params: InputQueryParams): PaginatedReports @${oem}
  }
`;

const generateAnalyticsForReports = async (
  dataSources: IContext["dataSources"],
  reports: Array<IReport>,
  userOem: IOem,
  customFields: Array<ICustomAdditionalField>,
  teams: Array<ITeam>,
) => {
  const analyticsPromises = reports.map(async report => {
    const _inputs = {
      entity: report.entity,
      chartType: report.chartType,
      filters: report.filters,
      xAxis: report.xAxis,
      segment: report.segment,
    };

    report.data = [];

    if (!_inputs.xAxis) {
      return report;
    }

    const entityCustomFields = customFields.filter(
      customField => entityTypes[_inputs.entity] === customField.type,
    );

    if (
      _inputs.xAxis &&
      (_inputs.xAxis.value === ANALYTICS_AXIS_OPTIONS.Team ||
        // @ts-ignore
        !ANALYTICS_AXIS_OPTIONS[_inputs.xAxis.value])
    ) {
      const isAxisTeam = _inputs.xAxis.value === ANALYTICS_AXIS_OPTIONS.Team;
      if (isAxisTeam && !(await checkForTeamsEnable(userOem, dataSources))) {
        return report;
      }

      if (!isAxisTeam) {
        const found = entityCustomFields.find(
          customField => customField._id.toString() === _inputs.xAxis!.value,
        );

        if (!found) return report;
      }
    }

    const collection = collectionToTypeMap[_inputs.entity];

    if (
      _inputs.segment &&
      (_inputs.segment.value === ANALYTICS_AXIS_OPTIONS.Team ||
        // @ts-ignore
        !ANALYTICS_AXIS_OPTIONS[_inputs.segment.value])
    ) {
      if (
        _inputs.segment.value === ANALYTICS_AXIS_OPTIONS.Team &&
        (!(await checkForTeamsEnable(userOem, dataSources)) || (teams || []).length === 0)
      ) {
        _inputs.segment = { value: "none", label: "None" };
      } else {
        const found = customFields.find(
          // @ts-ignore
          customField => customField._id.toString() === _inputs.segment.value,
        );

        if (!found) _inputs.segment = { value: "none", label: "None" };
      }
    }

    if (_inputs.filters) {
      const fieldIndex = _inputs.filters?.findIndex(
        (filter: any) => filter.field === ANALYTICS_AXIS_OPTIONS.Team,
      );

      const isTeamEnabled = await checkForTeamsEnable(userOem, dataSources);

      if ((fieldIndex !== -1 && isTeamEnabled && (teams || []).length === 0) || !isTeamEnabled) {
        // @ts-ignore
        _inputs.filters = _inputs.filters?.filter(
          (filter: any) => filter.field !== ANALYTICS_AXIS_OPTIONS.Team,
        );
      }

      const doesCustomFieldExist = _inputs.filters?.find(
        (filter: any) => filter.filterType === null,
      );
      if (doesCustomFieldExist) {
        const customFieldsMap = customFields.reduce((acc, item) => {
          const label = item.label!;
          const value = item;
          // @ts-ignore
          acc[label] = value;
          return acc;
        }, {});

        // @ts-ignore
        _inputs.filters = _inputs.filters?.filter(
          // @ts-ignore
          (filter: any) => filter.filterType !== null || customFieldsMap[filter.field],
        );
      }
    }

    report.data = JSON.parse(
      JSON.stringify(
        await dataSources[collection].aggregate(
          generateAnalyticsQuery(
            _inputs.chartType,
            _inputs.xAxis,
            _inputs.segment!,
            userOem._id,
            _inputs.filters!,
            entityCustomFields,
            teams,
          ),
        ),
      ),
    );

    return report;
  });

  const updatedReports = await Promise.all(analyticsPromises);

  return updatedReports;
};

const prepareFilterQuery = ({ args }: { args: { params: InputQueryParams } }) => {
  const query = { ...args };
  const searchQuery = query?.params?.where?.searchQuery;

  const filterQuery: {
    [x: string]: any;
  }[] = [];

  if (searchQuery) {
    const regexQuery = { $regex: searchQuery || "", $options: "i" };
    filterQuery.push({
      $or: [
        {
          title: regexQuery,
        },
      ],
    });
  }

  delete query?.params?.where?.searchQuery;

  return { filterQuery, query };
};

export const queryResolvers = {
  Query: {
    listAllOwnOemReports: async (
      _: any,
      args: { params: InputQueryParams },
      { dataSources, user }: IContext,
    ) => {
      const { skip, limit } = args.params;
      const { filterQuery, query } = prepareFilterQuery({ args });
      const queryPayload = {
        ...(filterQuery?.length ? { $and: filterQuery } : {}),
        ...buildQueryParams(query),
        oem: user.organization,
        sort: ["created_at:desc"],
      };

      const [reports, totalCount, userOem, customFields, teams] = await Promise.all([
        dataSources.Report.getMany(queryPayload),
        dataSources.Report.totalCount({
          ...queryPayload,
          limit: -1,
          skip: 0,
        }),
        dataSources.Oem.getOne({
          _id: user.organization,
        }),
        dataSources.CustomAdditionalField.getMany({
          oem: user.organization,
          type: { $in: ["facilities", "machines", "tickets"] },
          fieldType: "singleSelect",
          enabled: true,
          deleted: false,
          sort: ["order:asc", "created_at:asc"],
        }),
        dataSources.Team.getMany({
          oem: user.organization,
        }),
      ]);

      const updatedReports = await generateAnalyticsForReports(
        dataSources,
        reports,
        userOem,
        customFields,
        teams,
      );

      return {
        reports: updatedReports,
        limit,
        skip,
        totalCount,
        currentPage: skip && limit ? Math.floor(skip / limit + 1) : 1,
      };
    },

    getOwnOemAnalytics: async (_: any, args: any, { dataSources, user }: IContext) => {
      const { input } = args || {};

      if (!args) return null;
      const response: { data: any[]; inputTableOptions: { value: string; label: any }[] } = {
        data: [],
        inputTableOptions: [],
      };
      const { withInputTableOptions, ...restInput } = input;
      let _inputs = { ...restInput };

      if (input._id) {
        const report = await dataSources.Report.getOne({
          oem: user.organization,
          _id: input._id,
        });
        _inputs = { ...report };
      }

      const [customFields, teams, userOem]: [Array<ICustomAdditionalField>, Array<ITeam>, IOem] =
        await Promise.all([
          dataSources.CustomAdditionalField.getMany({
            oem: user.organization,
            type: entityTypes[_inputs.entity!],
            fieldType: "singleSelect",
            enabled: true,
            deleted: false,
            sort: ["order:asc", "created_at:asc"],
          }),
          dataSources.Team.getMany({
            oem: user.organization,
          }),
          dataSources.Oem.getOne({
            _id: user.organization,
          }),
        ]);

      const collection = collectionToTypeMap[_inputs.entity];
      if (_inputs.xAxis) {
        response.data = await dataSources[collection].aggregate(
          generateAnalyticsQuery(
            _inputs.chartType,
            _inputs.xAxis,
            _inputs.segment,
            user.organization,
            _inputs.filters,
            customFields,
            teams,
          ),
        );

        if (withInputTableOptions)
          response.inputTableOptions = await generateTableValues({
            userOem,
            customFields,
            entity: _inputs.entity,
            dataSources,
          });
      }

      return JSON.parse(JSON.stringify(response));
    },

    getOwnOemReportByID: async (_: any, args: any, { dataSources, user }: IContext) => {
      const { id } = args || {};
      if (!id || !user.organization) return null;

      return await dataSources.Report.getOne({
        _id: id,
        oem: user.organization,
      });
    },
  },
};
