import to from "await-to-js";
import mongoose from "mongoose";
import { SCOPES, mail } from "~/config";
import { ICustomFieldObject } from "~/datamodels/CustomAdditionalField/interface";
import { ERROR } from "~/environment";
import { IUser } from "~/datamodels/User/interface";
import { ICustomer } from "~/datamodels/Customer/interface";
import { ServiceParamsType } from "~/types/common";
import {
  throwIfError,
  generateRandom,
  hashString,
  getValidTeams,
  validateCustomFields,
} from "~/utils";
import { getFacilityInvitationEmailContent } from "~/utils/customers";
import { generatedPassword } from "~/utils/encryption/_hash";
import ROUTES_SETTINGS from "$/settings/routes-resolvers.json";
import InvalidInputError from "~/errors/InvalidInputError";
import ConflictError from "~/errors/ConflictError";
import { MONGODB_ERRORS } from "~/errors/constants";

export const createCustomer = async ({
  args,
  dataSources,
  user: contextUser,
  isRestAPI,
  translate,
}: ServiceParamsType) => {
  // STAGE 1
  const { input } = args || {};
  if (!input) return null;
  const { name, users, teams, facilityIdentifier, description, customFields, type } = input;
  if (!name) return null;
  const { teams: userTeams } = contextUser;

  const {
    CLIENT: {
      ROUTES: { FACILITY_LOGIN },
    },
  } = ROUTES_SETTINGS;

  const route = FACILITY_LOGIN.replace("/", "en/");

  if (userTeams?.length && !teams?.length) {
    throw new InvalidInputError("Need to assign at least one team to the created customer");
  }

  let newCustomFields: ICustomFieldObject[] = [];
  if (customFields?.length) {
    const {
      isValid,
      validatedCustomFields,
      errors: customFieldErrors,
    } = await validateCustomFields({
      dataSources,
      oem: contextUser.organization!.toString(),
      type: "facilities",
      customFields,
    });

    if (!isValid) throw new InvalidInputError(customFieldErrors?.join(", ") as string);
    newCustomFields = validatedCustomFields!;
  }

  const [errNewCustomer, newCustomer] = await to<ICustomer>(
    dataSources.Customer.save({
      name,
      oem: contextUser?.organization,
      facilityIdentifier:
        // @ts-ignore
        facilityIdentifier || generateRandom.stringWithSeed(new mongoose.Types.ObjectId()),
      createdBy: contextUser.id,
      type,
      ...(isRestAPI ? { createdFromAPI: true } : {}),
      ...(description ? { description } : {}),
      ...(newCustomFields ? { customFields: newCustomFields } : {}),
    }),
  );

  if ((errNewCustomer as any)?.code === MONGODB_ERRORS.DUPLICATE_KEY_ERROR_CODE) {
    throw new ConflictError(
      `Connection ${translate("error_messages.already_exists_id").replace(
        "{id}",
        facilityIdentifier,
      )}`,
    );
  } else {
    throwIfError(errNewCustomer, ERROR.USER.BAD_USER_INPUT);
  }

  const errors: { customer?: any; users?: any; machines?: any } = {};

  // STAGE 2
  // create users
  let newUsers: IUser[];
  const generatedPasswords: { [key: string]: string } = {};
  if (users?.length) {
    newUsers = await users.reduce(async (arr: IUser[], user: IUser, index: number) => {
      // eslint-disable-next-line no-shadow
      const { name, email, info, access } = user;
      await arr;
      if (Object.keys(errors).length) {
        const res = await arr;
        return res;
      }
      const password = generatedPassword();
      const hashedPassword = hashString(password).digest;
      const [err, newFacilityUser]: [any, IUser] = (await to(
        // @ts-ignore
        dataSources.User.addOne({
          data: {
            name,
            username: email,
            email,
            info,
            access,
            role: SCOPES.ROLES.USER.VALUE,
            password: hashedPassword,
            oem: contextUser?.organization,
            facility: newCustomer?._id,
          },
        }),
      )) as [any, IUser];
      if (err) {
        errors.users = [
          ...(errors?.users ?? []),
          {
            index,
            message: `${email} is already in use`,
          },
        ];
        const res = await arr;
        return res;
      }
      generatedPasswords[newFacilityUser._id] = password;
      return [...(await arr), newFacilityUser];
    }, []);

    if (errors.users?.length) {
      if (newUsers?.length) {
        const newUsersIds = newUsers.map(user => user._id);
        await dataSources.User.deleteMany({ _id: { $in: newUsersIds } });
      }
      await dataSources.Customer.deleteById(newCustomer?._id);
    }
  }

  // STAGE 3
  // Assign machines
  if (!errors.customer && !errors.users) {
    const { machines = [] } = input;
    await machines.reduce(async (arr: string[], machineId: string, index: number) => {
      await arr;
      if (Object.keys(errors).length) {
        const res = await arr;
        return res;
      }
      const [err, assignedMachine] = await to(
        // @ts-ignore
        dataSources.Machine.assignToCustomer({
          _id: machineId,
          customer: newCustomer?._id,
          customerQrCodeAccess: newCustomer?.qrCodeAccess,
          oem: contextUser.organization,
        }),
      );
      if (err) {
        errors.machines = [
          ...(errors?.machines ?? []),
          {
            index,
            message: "Cannot assign machine",
          },
        ];
        const res = await arr;
        return res;
      }
      return [...(await arr), assignedMachine];
    }, []);
    if (errors.machines?.length) {
      // @ts-ignore
      if (newUsers?.length) {
        const newUsersIds = newUsers.map(user => user._id);
        await dataSources.User.deleteMany({ _id: { $in: newUsersIds } });
      }
      await dataSources.Customer.deleteById(newCustomer?._id);
      // TODO: [rico][cleanup]
      // should reassing previous customers values back to affected machines
    }
  }

  throwIfError(Object.keys(errors).length, {
    custom: {
      validationErrors: {
        ...errors,
      },
    },
  });

  // send emails if everything is fine and new users with access have been added

  // @ts-ignore
  if (newUsers?.length) {
    // @ts-ignore
    await newUsers.reduce(async (arr, user) => {
      await arr;
      const { access } = user;
      if (mail && access) {
        // send email only if user has given access
        // console.log({ user });
        const oem = await dataSources.Oem.getById(user.oem);
        const loginUrl = {
          text: `${process.env.FACILITY_APP_URI}/${route}/${oem.urlOem}`,
          html: `<a href="${process.env.FACILITY_APP_URI}/${route}/${oem.urlOem}">${process.env.FACILITY_APP_URI}/${route}/${oem.urlOem}</a>`,
        };
        const data = getFacilityInvitationEmailContent({
          from: process.env.MAIL_FROM_EMAIL,
          to: user.email,
          company: oem,
          link: loginUrl,
          password: generatedPasswords[user._id],
        });

        mail.messages().send(data, async function (error) {
          if (error) {
            // console.log("[MAIL][ERROR]: ", error);
          } else {
            // credentials sent
            await dataSources.User.save({
              _id: user._id,
              userCredentialsSent: true,
            });
            // console.log("MAIL SENT", user.email);
          }
        });
      }
      const res = await arr;
      return res;
    }, []);
  }

  const validTeams = await getValidTeams({
    incomingTeams: teams,
    userTeams,
    user: contextUser,
    dataSources,
  });

  if (validTeams.length) {
    const updatedNewCustomerWithTeams = await dataSources.Customer.save({
      _id: newCustomer?._id,
      oem: contextUser.organization,
      teams: validTeams,
      createdBy: contextUser.id,
    });

    return updatedNewCustomerWithTeams;
  }
  return newCustomer;
};
