export const IMAGE_FILE_TYPES = [
  "jpg",
  "jpeg",
  "png",
  "gif",
  "webp",
  "avif",
  "heif",
  "heic",
  "tiff",
  "tif",
  "bmp",
  "tga",
  "targa",
  "psd",
  "psb",
  "xcf",
  "cr2",
  "cr3",
  "nef",
  "arw",
  "dng",
  "orf",
  "raf",
  "rw2",
  "3fr",
  "fff",
  "mef",
  "mrw",
  "nrw",
  "pef",
  "ptx",
  "r3d",
  "rw1",
  "rwl",
  "sr2",
  "srf",
  "x3f",
  "bay",
  "crw",
  "dcr",
  "erf",
  "kdc",
  "mos",
  "raw",
  "rwz",
  "srw",
  "pcx",
  "ico",
  "cur",
  "xbm",
  "xpm",
  "pbm",
  "pgm",
  "ppm",
  "pam",
  "wbmp",
  "jxr",
  "jp2",
  "jpx",
  "jng",
  "mng",
  "svg",
  "ai",
  "eps",
  "pdf",
  "emf",
  "wmf",
  "cgm",
  "fig",
  "odg",
  "vsd",
  "vsdx",
  "dwg",
  "dxf",
  "plt",
  "hpgl",
  "dcm",
  "dicom",
  "nii",
  "hdr",
  "img",
  "fits",
  "fit",
  "fts",
  "hdf",
  "h4",
  "h5",
  "hdf4",
  "hdf5",
  "geotiff",
  "ecw",
  "sid",
  "jpf",
  "j2k",
  "j2c",
  "apng",
  "icns",
  "djvu",
  "djv",
  "cbr",
  "cbz",
  "cb7",
  "cbt",
  "cba",
  "jxl",
  "webp2",
  "bpg",
  "flif",
  "wdp",
  "hdp",
  "dds",
  "ktx",
  "astc",
  "pkm",
  "pvr",
  "pic",
  "pict",
  "sgi",
  "rgb",
  "rgba",
  "bw",
  "int",
  "inta",
  "sun",
  "ras",
  "pnm",
  "jbig",
  "jb2",
  "wbm",
  "cut",
  "exr",
  "rgbe",
  "xyze",
  "pfm",
  "webm",
  "art",
  "jps",
  "mpo",
  "cals",
  "fax",
  "sfw",
  "pwp",
  "mac",
  "pntg",
  "qtif",
  "qti",
  "qif",
  "win",
  "bie",
  "jbg",
  "jbig2",
  "wmp",
  "scan",
  "g3",
  "g4",
  "mil",
  "cal",
  "cg4",
  "flc",
  "fli",
  "fpx",
  "mix",
  "ncd",
  "pcd",
  "pcds",
  "pct",
  "pix",
  "pxr",
  "qfx",
  "cin",
  "dpx",
  "xv",
  "thumb",
  "ithmb",
  "db",
  "jfi",
  "jfif",
  "jif",
  "jp4",
  "jpgm",
  "jpm",
  "mj2",
  "mjp2",
  "miff",
  "mvg",
  "otb",
  "pal",
  "palm",
  "pcl",
  "pdb",
  "pfb",
  "pgx",
  "picon",
  "ps",
  "ps2",
  "ps3",
  "ptif",
  "rad",
  "rla",
  "rle",
  "sct",
  "shtml",
  "mrsid",
  "tim",
  "ttf",
  "txt",
  "uil",
  "uyvy",
  "vicar",
  "viff",
  "wpg",
  "x",
  "xc",
  "xwd",
  "y",
  "yuv",
  "yvyu",
];

export const DOCUMENT_STATUS = {
  PENDING_INDEXING: 1000,
  PENDING_SCANNING: 1001,
  SCANNING: 1002,
  PENDING_APPROVAL: 1003,
  INDEXING: 1004,
  INDEXED: 1005,
  FAILED_INDEXING: 1006,
  FAILED_SCANNING: 1007,
};
