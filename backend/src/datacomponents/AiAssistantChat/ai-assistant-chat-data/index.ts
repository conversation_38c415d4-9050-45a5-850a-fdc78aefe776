import inputTypes from "~/datacomponents/AiAssistantChat/ai-assistant-chat-data/input";
import {
  mutationResolvers,
  mutationTypes,
} from "~/datacomponents/AiAssistantChat/ai-assistant-chat-data/mutation";
import {
  queryResolvers,
  queryTypes,
} from "~/datacomponents/AiAssistantChat/ai-assistant-chat-data/query";
import { typeResolvers, types } from "~/datacomponents/AiAssistantChat/ai-assistant-chat-data/type";

export default {
  resolvers: {
    ...mutationResolvers,
    ...queryResolvers,
    ...typeResolvers,
  },
  types: `
      ${inputTypes}
      ${mutationTypes}
      ${queryTypes}
      ${types}
    `,
};
