/* eslint-disable */
//prettier-ignore
module.exports = {
name: "@yarnpkg/plugin-engines",
factory: function (require) {
var plugin=(()=>{var m=Object.defineProperty;var C=Object.getOwnPropertyDescriptor;var N=Object.getOwnPropertyNames;var P=Object.prototype.hasOwnProperty;var s=(o=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(o,{get:(r,t)=>(typeof require<"u"?require:r)[t]}):o)(function(o){if(typeof require<"u")return require.apply(this,arguments);throw new Error('Dynamic require of "'+o+'" is not supported')});var k=(o,r)=>{for(var t in r)m(o,t,{get:r[t],enumerable:!0})},j=(o,r,t,e)=>{if(r&&typeof r=="object"||typeof r=="function")for(let n of N(r))!P.call(o,n)&&n!==t&&m(o,n,{get:()=>r[n],enumerable:!(e=C(r,n))||e.enumerable});return o};var V=o=>j(m({},"__esModule",{value:!0}),o);var T={};k(T,{default:()=>S});var i=s("@yarnpkg/core");var p=class{constructor(r){this.throwWrongEngineError=(r,t)=>{let e=this.formatErrorMessage(r,t);this.throwError(e)};this.throwError=r=>{switch(this.errorReporter){case"Yarn":this.reportYarnError(r);break;case"Console":default:this.reportConsoleError(r);break}};this.reportYarnError=r=>{throw new i.ReportError(i.MessageName.UNNAMED,r)};this.reportConsoleError=r=>{console.error(r),process.exit(1)};this.formatErrorMessage=(r,t)=>{let{configuration:e}=this.project,n=i.formatUtils.applyStyle(e,i.formatUtils.pretty(e,this.engine,"green"),2),c=i.formatUtils.pretty(e,r,"cyan"),g=i.formatUtils.pretty(e,t,"cyan"),l=`The current ${n} version ${c} does not satisfy the required version ${g}.`;return i.formatUtils.pretty(e,l,"red")};this.project=r.project,this.errorReporter=r.errorReporter}};var f=s("fs"),v=s("path"),h=s("semver"),y=s("@yarnpkg/fslib"),a=s("@yarnpkg/core");var Y=".nvmrc",b=".node-version",d=class extends p{constructor(){super(...arguments);this.resolveNodeFromFileRequiredVersion=t=>{let{configuration:e,cwd:n}=this.project,c=(0,v.resolve)(y.npath.fromPortablePath(n),t),g=a.formatUtils.applyStyle(e,a.formatUtils.pretty(e,this.engine,"green"),2);if(!(0,f.existsSync)(c)){this.throwError(a.formatUtils.pretty(e,`Unable to verify the ${g} version. The ${t} file does not exist.`,"red"));return}let l=(0,f.readFileSync)(c,"utf-8").trim();if((0,h.validRange)(l))return l;let w=a.formatUtils.pretty(e,t,"yellow");this.throwError(a.formatUtils.pretty(e,`Unable to verify the ${g} version. The ${w} file contains an invalid semver range.`,"red"))}}get engine(){return"Node"}verifyEngine(t){let e=t.node;e!=null&&([Y,b].forEach(n=>{e===n&&(e=this.resolveNodeFromFileRequiredVersion(n))}),(0,h.satisfies)(process.version,e,{includePrerelease:!0})||this.throwWrongEngineError(process.version.replace(/^v/i,""),e.replace(/^v/i,"")))}};var R=s("semver"),u=s("@yarnpkg/core");var E=class extends p{get engine(){return"Yarn"}verifyEngine(r){let t=r.yarn;t!=null&&((0,R.satisfies)(u.YarnVersion,t,{includePrerelease:!0})||this.throwWrongEngineError(u.YarnVersion,t))}};var x=o=>r=>{if(process.env.PLUGIN_YARN_ENGINES_DISABLE!=null)return;let{engines:t={}}=r.getWorkspaceByCwd(r.cwd).manifest.raw,e={project:r,errorReporter:o};[new d(e),new E(e)].forEach(c=>c.verifyEngine(t))},F={hooks:{validateProject:x("Yarn"),setupScriptEnvironment:x("Console")}},S=F;return V(T);})();
return plugin;
}
};
