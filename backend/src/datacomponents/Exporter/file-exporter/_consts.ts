import { PipelineStage } from "mongoose";
import {
  TypeCustomFieldType,
  IProject,
  TypeSort,
  AddFieldType,
  DetailExportValidator,
  DetailedExportType,
  OptionalDetailedExportType,
} from "~/datacomponents/Exporter/file-exporter/_types";
import { IProcedure } from "~/datamodels/Procedure/interface";
import { ITimeTrackerLog } from "~/datamodels/Ticket/interface";
import { IInventoryPart } from "~/datamodels/InventoryPart/interface";
import { IPreventiveMaintenance } from "~/datamodels/PreventiveMaintenance/interface";

const TICKET = "Ticket";
const MACHINE = "Machine";
const CUSTOMER = "Customer";
const PROCEDURE = "Procedure";
const INVENTORY_PART = "InventoryPart";
const USER = "User";
const TEAM = "Team";
const PREVENTIVE_MAINTENANCE = "PreventiveMaintenance";
const MACHINE_HISTORY = "MachineHistory";
const MACHINE_TEMPLATE = "MachineTemplate";
const OEM = "Oem";
const TIME_TRACKER = "TimeTracker";

export const COLLECTIONS = {
  TICKET,
  MACHINE,
  CUSTOMER,
  PROCEDURE,
  INVENTORY_PART,
  USER,
  TEAM,
  PREVENTIVE_MAINTENANCE,
  MACHINE_HISTORY,
};

export const DATE_FORMAT_CREATED_AT = "YYYY-MM-DD";
export const DATE_FORMAT_SCHEDULING = "YYYY-MM-DD hh:mm:ss A";

export const CUSTOM_FIELD_TYPE: TypeCustomFieldType = {
  [MACHINE]: "machines",
  [TICKET]: "tickets",
  [CUSTOMER]: "facilities",
  [INVENTORY_PART]: "parts",
};

export const CSV_FILE_NAME_PREFIX: { [x: string]: string } = {
  [MACHINE]: "assets",
  [TICKET]: "work-orders",
  [CUSTOMER]: "facilities",
  [INVENTORY_PART]: "inventory-parts",
  [MACHINE_HISTORY]: "machine-history",
  [MACHINE_TEMPLATE]: "machine-template",
};

export const DETAIL_EXPORT = {
  INVENTORY_PARTS: "inventoryParts",
  TIME_TRACKER_LOGS: "timeTrackerLogs",
  PROCEDURES: "procedures",
  PREVENTIVE_MAINTENANCE_EVENTS: "preventiveMaintenanceEvents",
  ASSET_HISTORY: "assetHistory",
} as const;

const isInventoryPart = (data: { quantity: string; details: IInventoryPart }) =>
  data && typeof data.details?.articleNumber === "string" && typeof data.quantity === "number";

const isPreventiveMaintenanceEvent = (data: IPreventiveMaintenance) =>
  data && !!data.startDate && !!data.eventDate;

const isTimeTrackerLog = (data: ITimeTrackerLog & { ticketTagLabel: string }) =>
  data && !!data.startDateTime && !!data.endDateTime;

const isProcedure = (data: IProcedure) => data && !!data.procedureId && !!data.name;

const isAssetHistory = (data: any) => data && !!data.eventType;

export const DETAIL_EXPORT_VALIDATORS: Record<DetailedExportType, DetailExportValidator> = {
  [DETAIL_EXPORT.INVENTORY_PARTS]: isInventoryPart,
  [DETAIL_EXPORT.TIME_TRACKER_LOGS]: isTimeTrackerLog,
  [DETAIL_EXPORT.PROCEDURES]: isProcedure,
  [DETAIL_EXPORT.PREVENTIVE_MAINTENANCE_EVENTS]: isPreventiveMaintenanceEvent,
  [DETAIL_EXPORT.ASSET_HISTORY]: isAssetHistory,
};

export const DETAIL_EXPORT_CONFIG = {
  [TICKET]: DETAIL_EXPORT,
  [MACHINE]: DETAIL_EXPORT,
  [MACHINE_TEMPLATE]: DETAIL_EXPORT.INVENTORY_PARTS,
} as const;

export const ROW_HEADERS: { [x: string]: string[] } = {
  [TICKET]: [
    "#",
    "ID",
    "Title",
    "Type",
    "Visibility",
    "Reporter",
    "Status",
    "Scheduled From (UTC)",
    "Scheduled To (UTC)",
    "Created Date",
    "Assignees",
    "Connection ID",
    "Connection Name",
    "Asset Serial Number",
    "Asset Name",
    "Created Time (UTC)",
    "Resolution Time (h:m)",
  ],
  [MACHINE]: [
    "#",
    "Asset ID",
    "Asset Name",
    "Asset Type",
    "Connection ID",
    "Connection Name",
    "Number of Parts",
    "Number of Preventive Maintenance Events",
    "QR Code Link",
    "Product Model",
  ],
  [CUSTOMER]: ["#", "ID", "Name", "Number of Assets"],
  [INVENTORY_PART]: ["#", "Article Number", "Name"],
  [MACHINE_HISTORY]: [
    "#",
    "Date",
    "Event Type",
    "Work Order Name",
    "Work Order ID",
    "Work Order Status",
    "Connection Name",
    "Procedure Name",
    "Procedure ID",
    "Procedure Status",
    "Note Description",
  ],
  [MACHINE_TEMPLATE]: ["#", "Product Model", "Product Name"],
};

export const DETAILED_ROW_HEADERS: {
  [key: string]: string[];
} = {
  [DETAIL_EXPORT.INVENTORY_PARTS]: ["Part Article Number", "Part Name", "Parts Quantity"],
  [DETAIL_EXPORT.TIME_TRACKER_LOGS]: [
    "Time Tracking Description",
    "From (Date & Time) (UTC)",
    "To (Date & Time) (UTC)",
    "Total Time Spent",
    "Time Tracking Tag",
    "Billable or Non-Billable",
    "Time Tracking Added By",
  ],
  [DETAIL_EXPORT.PROCEDURES]: ["Procedure Name", "Procedure Instance ID", "Procedure Status"],
  [DETAIL_EXPORT.PREVENTIVE_MAINTENANCE_EVENTS]: [
    "PME Name",
    "PME Start Date",
    "PME Frequency",
    "PME Repeat Every",
    "PME Work Order Creation",
  ],
  [DETAIL_EXPORT.ASSET_HISTORY]: [
    "Date",
    "Event Type",
    "Work Order Name",
    "Work Order ID",
    "Work Order Status",
    "Connection Name",
    "Procedure Name",
    "Procedure ID",
    "Procedure Status",
    "Note Description",
  ],
};

export const SORT: {
  [x: string]: TypeSort;
} = {
  [MACHINE]: ["name:asc"],
  [TICKET]: ["lastUpdatedAt:desc", "_id:asc"],
  [CUSTOMER]: ["name:asc"],
  [INVENTORY_PART]: ["name:asc"],
  [MACHINE_HISTORY]: ["createdAt:desc", "_id:asc"],
  [MACHINE_TEMPLATE]: ["title:asc"],
};

const PROJECT_COMMON: { [x: string]: 1 } = {
  name: 1,
  teams: 1,
  created_at: 1,
  updated_at: 1,
  closedOn: 1,
  customFields: 1,
  oem: 1,
};

const DETAILED_TICKET_PROJECTIONS: { [x: string]: IProject } = {
  [DETAIL_EXPORT.PROCEDURES]: { procedures: 1 },
  [DETAIL_EXPORT.INVENTORY_PARTS]: { inventoryParts: 1 },
  [DETAIL_EXPORT.TIME_TRACKER_LOGS]: { timeTrackerLogs: 1 },
  [DETAIL_EXPORT.PREVENTIVE_MAINTENANCE_EVENTS]: { preventiveMaintenanceEvents: 1 },
};

const DETAILED_ASSET_PROJECTIONS: { [x: string]: IProject } = {
  [DETAIL_EXPORT.INVENTORY_PARTS]: { inventoryParts: 1 },
  [DETAIL_EXPORT.PREVENTIVE_MAINTENANCE_EVENTS]: { preventiveMaintenanceEvents: 1 },
  [DETAIL_EXPORT.ASSET_HISTORY]: {
    // @ts-ignore
    assetHistory: {
      $concatArrays: ["$procedureType", "$ticketType", "$noteType"],
    },
  },
};

const DETAILED_ASSET_TEMPLATE_PROJECTIONS: { [x: string]: IProject } = {
  [DETAIL_EXPORT.INVENTORY_PARTS]: { inventoryParts: 1 },
};

export const PROJECT = (
  detailExport: OptionalDetailedExportType,
): {
  [x: string]: IProject;
} => ({
  [MACHINE]: {
    ...PROJECT_COMMON,
    assetType: 1,
    serialNumber: 1,
    countPreventiveMaintenanceEvents: 1,
    countPartsAttached: 1,
    facility: 1,
    facilityId: 1,
    isQRCodeEnabled: 1,
    uuid: 1,
    totalChildrenCount: 1,
    hierarchy: 1,
    templateId: 1,
    ...DETAILED_ASSET_PROJECTIONS[detailExport],
  },
  [TICKET]: {
    ...PROJECT_COMMON,
    id: 1,
    title: 1,
    status: 1,
    machine: 1,
    facility: 1,
    user: 1,
    assignees: 1,
    ticketType: 1,
    serialNumber: 1,
    facilityId: 1,
    "schedule.startTime": 1,
    "schedule.endTime": 1,
    countPartsAttached: 1,
    countProceduresAttached: 1,
    ...DETAILED_TICKET_PROJECTIONS[detailExport],
  },
  [CUSTOMER]: {
    ...PROJECT_COMMON,
    facilityIdentifier: 1,
    countMachinesAttached: 1,
  },
  [INVENTORY_PART]: {
    ...PROJECT_COMMON,
    articleNumber: 1,
    countPartsAttached: 1,
  },
  [MACHINE_HISTORY]: {
    history: { $concatArrays: ["$procedureType", "$ticketType", "$noteType"] },
  },
  [MACHINE_TEMPLATE]: {
    templateId: 1,
    title: 1,
    ...DETAILED_ASSET_TEMPLATE_PROJECTIONS[detailExport],
  },
});

const MATCH_DELETED = { $match: { deleted: false } };
const PROJECT_ID = { $project: { _id: 1 } };
const PROJECT_NAME = { $project: { name: 1 } };

export const LOOKUP_MACHINE = {
  $lookup: {
    from: MACHINE,
    localField: "machine",
    foreignField: "_id",
    pipeline: [MATCH_DELETED, { $project: { name: 1, serialNumber: 1 } }],
    as: "machine",
  },
};

export const LOOKUP_USERS = {
  $lookup: {
    from: USER,
    localField: "timeTrackerLogs.createdBy",
    foreignField: "_id",
    pipeline: [{ $project: { _id: 1, name: 1, deleted: 1 } }],
    as: "users",
  },
};

export const LOOKUP_TEAMS = {
  $lookup: {
    from: TEAM,
    localField: "teams",
    foreignField: "_id",
    pipeline: [MATCH_DELETED, PROJECT_NAME],
    as: "teams",
  },
};

export const LOOKUP_OEM = {
  $lookup: {
    from: OEM,
    localField: "oem",
    foreignField: "_id",
    pipeline: [{ $project: { _id: 1, statuses: 1 } }],
    as: "oem",
  },
};

export const ADD_FIRST_OEM = {
  $addFields: {
    oem: { $arrayElemAt: ["$oem", 0] },
  },
};

export const LOOKUP_ASSET_TEMPLATE_ID = {
  $lookup: {
    from: MACHINE_TEMPLATE,
    localField: "template",
    foreignField: "_id",
    pipeline: [{ $project: { _id: 1, templateId: 1 } }],
    as: "template",
  },
};

export const LOOKUP_PROCEDURE = {
  $lookup: {
    from: PROCEDURE,
    localField: "procedures.procedure",
    foreignField: "_id",
    pipeline: [MATCH_DELETED, { $project: { name: 1, procedureId: 1, state: 1 } }],
    as: "procedures",
  },
};

export const LOOKUP_TICKET_TAG = {
  $lookup: {
    from: TIME_TRACKER,
    localField: "timeTrackerLogs.ticketTag",
    foreignField: "_id",
    pipeline: [MATCH_DELETED, { $project: { _id: 1, label: 1 } }],
    as: "ticketTagLabels",
  },
};

export const LOOKUP_INVENTORY_PARTS = {
  $lookup: {
    from: INVENTORY_PART,
    localField: "inventoryParts.part",
    foreignField: "_id",
    pipeline: [MATCH_DELETED, { $project: { name: 1, articleNumber: 1 } }],
    as: "inventoryDetails",
  },
};

export const LOOKUP_PREVENTIVE_MAINTENANCE_DETAILED = {
  $lookup: {
    from: PREVENTIVE_MAINTENANCE,
    localField: "_id",
    foreignField: "machine",
    pipeline: [
      MATCH_DELETED,
      { $project: { title: 1, startDate: 1, frequency: 1, repeatInNumber: 1, eventDate: 1 } },
    ],
    as: "preventiveMaintenanceEvents",
  },
};

export const LOOKUP_CUSTOMER = {
  $lookup: {
    from: CUSTOMER,
    localField: "customer",
    foreignField: "_id",
    pipeline: [MATCH_DELETED, { $project: { name: 1, facilityIdentifier: 1 } }],
    as: "facility",
  },
};

export const LOOKUP_FACILITY = {
  ...LOOKUP_CUSTOMER,
  $lookup: { ...LOOKUP_CUSTOMER.$lookup, localField: "facility" },
};

export const LOOKUP_CUSTOMER_MACHINES = {
  $lookup: {
    from: MACHINE,
    localField: "_id",
    foreignField: "customer",
    pipeline: [MATCH_DELETED, PROJECT_ID],
    as: "machines",
  },
};

export const LOOKUP_ASSIGNEES = {
  $lookup: {
    from: USER,
    localField: "assignees",
    foreignField: "_id",
    pipeline: [MATCH_DELETED, PROJECT_NAME],
    as: "assignees",
  },
};

export const LOOKUP_REPORTER = {
  $lookup: {
    from: USER,
    localField: "user",
    foreignField: "_id",
    pipeline: [MATCH_DELETED, PROJECT_NAME],
    as: "user",
  },
};

export const LOOKUP_PREVENTIVE_MAINTENANCE = {
  $lookup: {
    from: PREVENTIVE_MAINTENANCE,
    localField: "_id",
    foreignField: "machine",
    pipeline: [MATCH_DELETED, PROJECT_ID],
    as: "preventiveMaintenanceEvent",
  },
};

export const ADD_FIELD_MACHINE: AddFieldType = {
  machine: "$machine.name",
  serialNumber: "$machine.serialNumber",
};

export const ADD_FIELD_FACILITY: AddFieldType = {
  facility: "$facility.name",
  facilityId: "$facility.facilityIdentifier",
};

export const ADD_FIELD_TEMPLATE_ID: AddFieldType = {
  templateId: "$template.templateId",
};

export const ADD_FIELD_TEAMS: AddFieldType = {
  teams: { $map: { input: "$teams", as: "team", in: "$$team.name" } },
};

export const ADD_FIELD_INVENTORY_PARTS: AddFieldType = {
  inventoryParts: {
    $map: {
      input: "$inventoryParts",
      as: "inventoryPart",
      in: {
        quantity: "$$inventoryPart.quantity",
        details: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$inventoryDetails",
                as: "inv",
                cond: { $eq: ["$$inv._id", "$$inventoryPart.part"] },
              },
            },
            0,
          ],
        },
      },
    },
  },
};

export const ADD_FIELD_INVENTORY_PARTS_DETAILED: AddFieldType = {
  inventoryParts: {
    $map: {
      input: "$inventoryParts",
      as: "inventoryPart",
      in: {
        quantity: { $size: "$inventoryParts" },
        details: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$inventoryDetails",
                as: "inv",
                cond: { $eq: ["$$inv._id", "$$inventoryPart.part"] },
              },
            },
            0,
          ],
        },
      },
    },
  },
};

export const ADD_FIELD_PREVENTIVE_MAINTENANCE_EVENTS_DETAILED: AddFieldType = {
  preventiveMaintenanceEvents: {
    $map: {
      input: "$preventiveMaintenanceEvents",
      as: "preventiveMaintenanceEvent",
      in: {
        $mergeObjects: [
          "$$preventiveMaintenanceEvent",
          {
            startDate: {
              $dateToString: {
                format: "%Y-%m-%d",
                date: {
                  $cond: [
                    { $eq: [{ $type: "$$preventiveMaintenanceEvent.startDate" }, "string"] },
                    {
                      $dateFromString: {
                        dateString: "$$preventiveMaintenanceEvent.startDate",
                        format: "%Y-%m-%d",
                      },
                    },
                    "$$preventiveMaintenanceEvent.startDate",
                  ],
                },
                timezone: "UTC",
              },
            },
            eventDate: {
              $dateToString: {
                format: "%Y-%m-%d",
                date: {
                  $cond: [
                    { $eq: [{ $type: "$$preventiveMaintenanceEvent.eventDate" }, "string"] },
                    {
                      $dateFromString: {
                        dateString: "$$preventiveMaintenanceEvent.eventDate",
                        format: "%Y-%m-%d",
                      },
                    },
                    "$$preventiveMaintenanceEvent.eventDate",
                  ],
                },
                timezone: "UTC",
              },
            },
          },
        ],
      },
    },
  },
};

export const ADD_TICKET_TAG = {
  timeTrackerLogs: {
    $map: {
      input: "$timeTrackerLogs",
      as: "log",
      in: {
        $mergeObjects: [
          "$$log",
          {
            ticketTagLabel: {
              $let: {
                vars: {
                  labelObj: {
                    $arrayElemAt: [
                      {
                        $filter: {
                          input: "$ticketTagLabels",
                          as: "label",
                          cond: { $eq: ["$$label._id", "$$log.ticketTag"] },
                        },
                      },
                      0,
                    ],
                  },
                },
                in: "$$labelObj.label",
              },
            },
          },
          {
            createdBy: {
              $let: {
                vars: {
                  user: {
                    $arrayElemAt: [
                      {
                        $filter: {
                          input: "$users",
                          as: "user",
                          cond: { $eq: ["$$user._id", "$$log.createdBy"] },
                        },
                      },
                      0,
                    ],
                  },
                },
                in: "$$user",
              },
            },
          },
        ],
      },
    },
  },
};

export const ADD_FIELD_COUNT_PARTS_ATTACHED: AddFieldType = {
  countPartsAttached: { $size: { $ifNull: ["$inventoryParts", []] } as any },
};

export const ADD_FIELD_COUNT_PREVENTIVE_MAINTENANCE_EVENTS: AddFieldType = {
  countPreventiveMaintenanceEvents: {
    $size: { $ifNull: ["$preventiveMaintenanceEvent", []] } as any,
  },
};

export const ADD_FIELD_COUNT_MACHINES_ATTACHED: AddFieldType = {
  countMachinesAttached: { $size: { $ifNull: ["$machines", []] } as any },
};

export const ADD_FIELD_COUNT_PRODCEDURES_ATTACHED: AddFieldType = {
  countProceduresAttached: { $size: { $ifNull: ["$procedures", []] } as any },
};

export const ADD_FIELD_ASSIGNEES: AddFieldType = {
  assignees: { $map: { input: "$assignees", as: "assignee", in: "$$assignee.name" } },
};

export const ADD_FIELD_REPORTER: AddFieldType = {
  user: "$user.name",
};

export const ADD_FIELD_PARTS_REQUESTED: AddFieldType = {
  countPartsRequested: {
    $reduce: {
      input: "$inventoryParts",
      initialValue: 0,
      in: { $add: ["$$value", "$$this.quantity"] },
    },
  },
};

export const ADD_FIRST_MACHINE: AddFieldType = {
  machine: { $arrayElemAt: ["$machine", 0] },
};

export const ADD_FIRST_FACILITY: AddFieldType = {
  facility: { $arrayElemAt: ["$facility", 0] },
};

export const ADD_FIRST_ASSET_TEMPLATE: AddFieldType = {
  template: { $arrayElemAt: ["$template", 0] },
};

export const ADD_FIRST_USER: AddFieldType = {
  user: { $arrayElemAt: ["$user", 0] },
};

const TICKET_TYPE_PIPELINE = [
  {
    $lookup: {
      from: CUSTOMER,
      localField: "facility",
      foreignField: "_id",
      as: "facility",
    },
  },
  { $unwind: "$facility" },
];

const MACHINE_HISTORY_FACET = {
  $facet: {
    procedureType: [
      { $match: { $expr: { $eq: ["$type", PROCEDURE] } } },
      {
        $lookup: {
          from: PROCEDURE,
          localField: "resourceId",
          foreignField: "_id",
          as: "resource",
        },
      },
      { $unwind: "$resource" },
      {
        $lookup: {
          from: TICKET,
          localField: "ticket",
          foreignField: "_id",
          as: "ticket",
        },
      },
      { $unwind: "$ticket" },
    ],
    ticketType: [
      { $match: { $expr: { $eq: ["$type", TICKET] } } },
      {
        $lookup: {
          from: TICKET,
          localField: "resourceId",
          foreignField: "_id",
          pipeline: TICKET_TYPE_PIPELINE,
          as: "resource",
        },
      },
      { $unwind: "$resource" },
    ],
    noteType: [{ $match: { $expr: { $eq: ["$type", "Note"] } } }],
  },
};

const ASSET_HISTORY_LOOKUP = {
  $lookup: {
    from: MACHINE_HISTORY,
    localField: "_id",
    foreignField: "machine",
    pipeline: [{ $sort: { createdAt: -1 } }],
    as: "history",
  },
};

export const UNWIND_HISTORY = {
  $unwind: {
    path: "$history",
    preserveNullAndEmptyArrays: true,
  },
};

export const LOOKUP_PROCEDURE_FROM_RESOURCE = {
  $lookup: {
    from: "Procedure",
    localField: "history.resourceId",
    foreignField: "_id",
    as: "history.resource",
  },
};

export const UNWIND_HISTORY_RESOURCE = {
  $unwind: {
    path: "$history.resource",
    preserveNullAndEmptyArrays: true,
  },
};

export const LOOKUP_HISTORY_BY_HISTORY_TICKET = {
  $lookup: {
    from: "Ticket",
    localField: "history.ticket",
    foreignField: "_id",
    as: "history_ticket_by_ticket",
  },
};

export const LOOKUP_HISTORY_BY_HISTORY_RESOURCE = {
  $lookup: {
    from: "Ticket",
    localField: "history.resourceId",
    foreignField: "_id",
    as: "history_ticket_by_id",
  },
};

export const ADD_FIELDS_HISTORY_TICKET = {
  $addFields: {
    "history.ticket": {
      $cond: {
        if: { $gt: [{ $size: "$history_ticket_by_ticket" }, 0] },
        then: { $arrayElemAt: ["$history_ticket_by_ticket", 0] },
        else: { $arrayElemAt: ["$history_ticket_by_id", 0] },
      },
    },
  },
};

export const HISTORY_CUSTOMER_LOOKUP = {
  $lookup: {
    from: "Customer",
    localField: "history.ticket.facility",
    foreignField: "_id",
    as: "history.ticket.facility",
  },
};

export const UNWIND_HISTORY_CUSTOMER = {
  $unwind: {
    path: "$history.ticket.facility",
    preserveNullAndEmptyArrays: true,
  },
};

export const UNWIND_HISTORY_TICKET = {
  $unwind: {
    path: "$history.ticket",
    preserveNullAndEmptyArrays: true,
  },
};

export const HISTORY_TICKET_GROUP = {
  $group: {
    _id: "$_id",
    machineData: { $first: "$$ROOT" },
    procedureType: {
      $push: {
        $cond: [
          { $eq: ["$history.type", "Procedure"] },
          {
            createdAt: "$history.createdAt",
            eventType: "$history.type",
            oemStatuses: "$oem.statuses",
            historyId: "$history._id",
            resourceId: "$history.resourceId",
            ticket: "$history.ticket",
            resource: "$history.resource",
          },
          "$$REMOVE",
        ],
      },
    },
    ticketType: {
      $push: {
        $cond: [
          { $eq: ["$history.type", "Ticket"] },
          {
            createdAt: "$history.createdAt",
            eventType: "$history.type",
            oemStatuses: "$oem.statuses",
            historyId: "$history._id",
            resourceId: "$history.resourceId",
            resource: "$history.resource",
            ticket: "$history.ticket",
            facility: "$history.ticket.facility",
          },
          "$$REMOVE",
        ],
      },
    },
    noteType: {
      $push: {
        $cond: [
          { $eq: ["$history.type", "Note"] },
          {
            createdAt: "$history.createdAt",
            eventType: "$history.type",
            oemStatuses: "$oem.statuses",
            historyId: "$history._id",
            note: "$history.note",
          },
          "$$REMOVE",
        ],
      },
    },
  },
};

export const HISTORY_TICKET_REPLACE_ROOT = {
  $replaceRoot: {
    newRoot: {
      $mergeObjects: [
        "$machineData",
        {
          procedureType: "$procedureType",
          ticketType: "$ticketType",
          noteType: "$noteType",
        },
      ],
    },
  },
};

export const HISTORY_REMERGE_ROOT = {
  $addFields: {
    mergedData: {
      $mergeObjects: [
        {
          procedureType: "$procedureType",
          ticketType: "$ticketType",
          noteType: "$noteType",
        },
      ],
    },
  },
};

export const NO_OP_STAGE: PipelineStage = { $match: {} };

const DETAILED_TICKET_LOOKUP_STAGES: { [x: string]: PipelineStage[] } = {
  [DETAIL_EXPORT.PROCEDURES]: [LOOKUP_PROCEDURE],
  [DETAIL_EXPORT.INVENTORY_PARTS]: [LOOKUP_INVENTORY_PARTS],
  [DETAIL_EXPORT.TIME_TRACKER_LOGS]: [LOOKUP_TICKET_TAG, LOOKUP_USERS],
};

const DETAILED_ASSET_LOOKUP_STAGES: { [x: string]: PipelineStage[] } = {
  [DETAIL_EXPORT.INVENTORY_PARTS]: [LOOKUP_INVENTORY_PARTS],
  [DETAIL_EXPORT.PREVENTIVE_MAINTENANCE_EVENTS]: [LOOKUP_PREVENTIVE_MAINTENANCE_DETAILED],
  [DETAIL_EXPORT.ASSET_HISTORY]: [
    // @ts-ignore
    ASSET_HISTORY_LOOKUP,
    UNWIND_HISTORY,
    LOOKUP_PROCEDURE_FROM_RESOURCE,
    UNWIND_HISTORY_RESOURCE,
    LOOKUP_HISTORY_BY_HISTORY_TICKET,
    LOOKUP_HISTORY_BY_HISTORY_RESOURCE,
    // @ts-ignore
    ADD_FIELDS_HISTORY_TICKET,
    UNWIND_HISTORY_TICKET,
    HISTORY_CUSTOMER_LOOKUP,
    UNWIND_HISTORY_CUSTOMER,
    LOOKUP_OEM,
    // @ts-ignore
    ADD_FIRST_OEM,
    HISTORY_TICKET_GROUP,
    HISTORY_TICKET_REPLACE_ROOT,
    HISTORY_REMERGE_ROOT,
  ],
};

const DETAILED_ASSET_TEMPLATE_LOOKUP_STAGES: { [x: string]: PipelineStage[] } = {
  [DETAIL_EXPORT.INVENTORY_PARTS]: [LOOKUP_INVENTORY_PARTS],
};

const DETAILED_TICKET_ADD_FIELD_STAGES: { [x: string]: AddFieldType } = {
  [DETAIL_EXPORT.INVENTORY_PARTS]: ADD_FIELD_INVENTORY_PARTS,
  [DETAIL_EXPORT.TIME_TRACKER_LOGS]: ADD_TICKET_TAG,
};

const DETAILED_ASSET_ADD_FIELD_STAGES: { [x: string]: AddFieldType } = {
  [DETAIL_EXPORT.INVENTORY_PARTS]: ADD_FIELD_INVENTORY_PARTS_DETAILED,
  [DETAIL_EXPORT.PREVENTIVE_MAINTENANCE_EVENTS]: ADD_FIELD_PREVENTIVE_MAINTENANCE_EVENTS_DETAILED,
};

const DETAILED_ASSET_TEMPLATE_ADD_FIELD_STAGES: { [x: string]: AddFieldType } = {
  [DETAIL_EXPORT.INVENTORY_PARTS]: ADD_FIELD_INVENTORY_PARTS_DETAILED,
};

export const LOOKUP_AND_PROCESSING = (
  detailExport: OptionalDetailedExportType,
): {
  [x: string]: PipelineStage[];
} => ({
  [TICKET]: [
    LOOKUP_MACHINE,
    LOOKUP_TEAMS,
    LOOKUP_FACILITY,
    LOOKUP_ASSIGNEES,
    LOOKUP_REPORTER,
    LOOKUP_OEM,
    ...(detailExport in DETAILED_TICKET_LOOKUP_STAGES
      ? DETAILED_TICKET_LOOKUP_STAGES[detailExport]
      : [NO_OP_STAGE]),
    {
      $addFields: {
        ...ADD_FIRST_MACHINE,
        ...ADD_FIRST_FACILITY,
        ...ADD_FIRST_USER,
      },
    },
    {
      $addFields: {
        ...ADD_FIELD_MACHINE,
        ...ADD_FIELD_FACILITY,
        ...ADD_FIELD_TEAMS,
        ...(detailExport in DETAILED_TICKET_ADD_FIELD_STAGES
          ? DETAILED_TICKET_ADD_FIELD_STAGES[detailExport]
          : {}),
        ...ADD_FIELD_REPORTER,
        ...ADD_FIELD_ASSIGNEES,
        ...ADD_FIELD_COUNT_PARTS_ATTACHED,
        ...ADD_FIELD_COUNT_PRODCEDURES_ATTACHED,
      },
    },
  ],
  [MACHINE]: [
    LOOKUP_CUSTOMER,
    LOOKUP_TEAMS,
    LOOKUP_PREVENTIVE_MAINTENANCE,
    LOOKUP_ASSET_TEMPLATE_ID,
    ...(detailExport in DETAILED_ASSET_LOOKUP_STAGES
      ? DETAILED_ASSET_LOOKUP_STAGES[detailExport]
      : [NO_OP_STAGE]),
    {
      $addFields: { ...ADD_FIRST_FACILITY, ...ADD_FIRST_ASSET_TEMPLATE },
    },
    {
      $addFields: {
        ...ADD_FIELD_FACILITY,
        ...ADD_FIELD_TEMPLATE_ID,
        ...ADD_FIELD_TEAMS,
        ...(detailExport in DETAILED_ASSET_ADD_FIELD_STAGES
          ? DETAILED_ASSET_ADD_FIELD_STAGES[detailExport]
          : {}),
        ...ADD_FIELD_COUNT_PARTS_ATTACHED,
        ...ADD_FIELD_COUNT_PREVENTIVE_MAINTENANCE_EVENTS,
      },
    },
  ],
  [CUSTOMER]: [
    LOOKUP_CUSTOMER_MACHINES,
    LOOKUP_TEAMS,
    {
      $addFields: {
        ...ADD_FIELD_COUNT_MACHINES_ATTACHED,
        ...ADD_FIELD_TEAMS,
      },
    },
  ],
  [MACHINE_HISTORY]: [MACHINE_HISTORY_FACET],
  [INVENTORY_PART]: [],
  [MACHINE_TEMPLATE]: [
    ...(detailExport in DETAILED_ASSET_TEMPLATE_LOOKUP_STAGES
      ? DETAILED_ASSET_TEMPLATE_LOOKUP_STAGES[detailExport]
      : [NO_OP_STAGE]),
    {
      $addFields: {
        ...(detailExport in DETAILED_ASSET_TEMPLATE_ADD_FIELD_STAGES
          ? DETAILED_ASSET_TEMPLATE_ADD_FIELD_STAGES[detailExport]
          : {}),
      },
    },
  ],
});

export const EXTENDED_PIPELINE: {
  [x: string]: PipelineStage[];
} = {
  [MACHINE_HISTORY]: [{ $unwind: "$history" }, { $replaceRoot: { newRoot: "$history" } }],
};

export const EXTENDED_PIPELINE_DETAIL_EXPORT: {
  [x: string]: PipelineStage[];
} = {
  [DETAIL_EXPORT.ASSET_HISTORY]: [
    {
      $set: {
        assetHistory: {
          $sortArray: {
            input: "$assetHistory",
            sortBy: { createdAt: -1 },
          },
        },
      },
    },
  ],
};
