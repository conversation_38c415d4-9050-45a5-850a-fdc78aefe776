import { describe, it, expect, vi, beforeEach } from "vitest";
import { Types } from "mongoose";
import { createContact } from "~/services/contact/create";
import InvalidInputError from "~/errors/InvalidInputError";
import ConflictError from "~/errors/ConflictError";
import { MONGODB_ERRORS } from "~/errors/constants";

describe("createContact", () => {
  const mockUser = {
    id: "67923bc76b5c8f61134e20a5",
    organization: "67923bc76b5c8f61134e20a8",
  };

  const mockInput = {
    name: "<PERSON>",
    email: "<EMAIL>",
    jobTitle: "Manager",
    landline: "1234567890",
    phoneNumber: "0987654321",
    user: "67923bc76b5c8f61134e20a5",
    connection: "66fbc581f9795b9fec77978c",
  };

  const mockOem = {
    _id: new Types.ObjectId("67923bc76b5c8f61134e20a8"),
    // add other required OEM fields
  };

  const mockCustomer = {
    _id: new Types.ObjectId("66fbc581f9795b9fec77978c"),
    oem: new Types.ObjectId("67923bc76b5c8f61134e20a8"),
    // add other required Customer fields
  };

  const mockContact = {
    _id: new Types.ObjectId(),
    ...mockInput,
    oem: mockUser.organization,
    createdBy: mockUser.id,
  };

  const mockDataSources = {
    Oem: {
      loadOne: vi.fn(),
    },
    Customer: {
      loadOne: vi.fn(),
    },
    Contact: {
      save: vi.fn(),
      getOne: vi.fn(),
    },
    User: {
      getOne: vi.fn(),
    },
  };

  const mockTranslate = vi.fn(key => key);

  beforeEach(() => {
    vi.clearAllMocks();
    mockDataSources.Oem.loadOne.mockResolvedValue(mockOem);
    mockDataSources.User.getOne.mockResolvedValue(null);
    mockDataSources.Customer.loadOne.mockResolvedValue(mockCustomer);
    mockDataSources.Contact.save.mockResolvedValue(mockContact);
  });

  it("should successfully create a contact", async () => {
    const result = await createContact({
      args: { input: mockInput },
      dataSources: mockDataSources,
      user: mockUser,
      translate: mockTranslate,
    });

    expect(result).toEqual(mockContact);
    expect(mockDataSources.Oem.loadOne).toHaveBeenCalledWith(
      new Types.ObjectId(mockUser.organization),
    );
    expect(mockDataSources.Customer.loadOne).toHaveBeenCalledWith(
      new Types.ObjectId(mockInput.connection),
    );
    expect(mockDataSources.Contact.save).toHaveBeenCalledWith({
      name: mockInput.name,
      email: mockInput.email,
      jobTitle: mockInput.jobTitle,
      landline: mockInput.landline,
      phoneNumber: mockInput.phoneNumber,
      user: mockInput.user,
      oem: mockUser.organization,
      connection: mockInput.connection,
      createdBy: mockUser.id,
    });
  });

  it("should throw InvalidInputError when input is missing", async () => {
    await expect(
      createContact({
        args: { input: null },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(InvalidInputError);
  });

  it("should throw InvalidInputError when OEM is not found", async () => {
    mockDataSources.Oem.loadOne.mockResolvedValue(null);

    await expect(
      createContact({
        args: { input: mockInput },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(InvalidInputError);
  });

  it("should throw InvalidInputError when Customer is not found", async () => {
    mockDataSources.Customer.loadOne.mockResolvedValue(null);

    await expect(
      createContact({
        args: { input: mockInput },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(InvalidInputError);
  });

  it("should throw InvalidInputError when Customer OEM does not match user organization", async () => {
    mockDataSources.Customer.loadOne.mockResolvedValue({
      ...mockCustomer,
      oem: new Types.ObjectId("66fbc581f97959bfec77978c"),
    });

    await expect(
      createContact({
        args: { input: mockInput },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(InvalidInputError);
  });

  it("should throw ConflictError when email already exists", async () => {
    const duplicateError = new Error("Duplicate key error");
    (duplicateError as any).code = MONGODB_ERRORS.DUPLICATE_KEY_ERROR_CODE;
    mockDataSources.Contact.save.mockRejectedValue(duplicateError);
    mockDataSources.Contact.getOne.mockResolvedValue(null);

    await expect(
      createContact({
        args: { input: mockInput },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(ConflictError);
  });

  it("should throw Error for other database errors", async () => {
    const dbError = new Error("Database error");
    mockDataSources.Contact.save.mockRejectedValue(dbError);

    await expect(
      createContact({
        args: { input: mockInput },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow();
  });
});
