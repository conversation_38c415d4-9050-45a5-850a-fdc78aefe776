export const types = `#graphql
  type ActivityLog {
    _id: ID
    resourceId: ID
    resource: String
    attribute: String
    action: String
    actor: String
    meta: MJSON
    createdAt: DateTime    
  }

  type PaginatedActivityLogs {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    logs: [ActivityLog]
  }
`;

export const typeResolvers = {
  ActivityLog: {
    // eslint-disable-next-line camelcase
    createdAt: ({ createdAt }: { createdAt: string }) =>
      createdAt ? new Date(createdAt)?.toISOString() : null,

    meta: ({ meta }: { meta: { [x: string]: any } }) =>
      meta ? JSON.parse(JSON.stringify(meta)) : null,
  },
};
