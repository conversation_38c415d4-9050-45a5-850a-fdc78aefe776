module.exports = {
  AWS_ACCESS_KEY_ID: {
    required: true,
  },
  AWS_SECRET_ACCESS_KEY: {
    required: true,
  },
  AWS_CLOUDFRONT_URL: {
    required: true,
  },
  AWS_S3_BUCKET: {
    required: true,
  },
  AWS_S3_BUCKET_REGION: {
    required: true,
  },
  AWS_S3_SIGNED_URL_EXPIRATION: {
    default: 60,
  },
  DB_CONNECTION_STRING: {
    required: true,
  },
  AUTH_SECRET_TOKEN: {
    required: true,
  },
  AUTH_SECRET_REFRESH_TOKEN: {
    required: true,
  },
  MACHINE_URL_MODIFIER: {
    required: true,
  },
  MAIL_API: {
    required: true,
  },
  MAIL_DOMAIN: {
    required: true,
  },
  MAIL_FROM_EMAIL: {
    required: true,
  },
  CHAT_SUBSCRIBE_KEY: {
    required: true,
  },
  CHAT_PUBLISH_KEY: {
    required: true,
  },
  CHAT_SECRET_KEY: {
    required: true,
  },
  <PERSON><PERSON>_CLIENT_ID: {
    required: true,
  },
  BOX_CLIENT_SECRET: {
    required: true,
  },
  BOX_PUBLIC_KEY_ID: {
    required: true,
  },
  BOX_PRIVATE_KEY: {
    required: true,
  },
  BOX_PASSPHRASE: {
    required: true,
  },
  BOX_ENTERPRISE_ID: {
    required: true,
  },
  SERVER_ENVIRONMENT: {
    required: true,
  },
  GOOGLE_PLACE_API_KEY: {
    required: true,
  },
  DROMO_FRONTEND_LICENCE_KEY: { required: true },
  DROMO_BACKEND_LICENCE_KEY: { required: true },
  NODE_ENV: { default: "production" },
  NEW_RELIC_API_KEY: {
    required: true,
  },
  NEW_RELIC_SERVICE_NAME: {
    required: true,
  },
  EMAIL_FILE_PASSWORD: {
    required: true,
  },
  SERVER_URI: {
    required: true,
  },
  ALLOWED_ORIGINS: {
    required: true,
  },
  OEM_APP_URI: {
    required: true,
  },
  FACILITY_APP_URI: {
    required: true,
  },
  QR_APP_URI: {
    required: true,
  },
  FACILITY_APP_VERSION: {
    required: true,
  },
  OEM_APP_VERSION: {
    required: true,
  },
  OEM_IOS_APP_VERSION: {
    required: true,
  },
  OEM_ANDROID_APP_VERSION: {
    required: true,
  },
  OEM_INTERCOM_SECRET: {
    required: false,
  },
  OEM_IOS_INTERCOM_SECRET: {
    required: false,
  },
  OEM_ANDROID_INTERCOM_SECRET: {
    required: false,
  },
  MACHINE_3D_AUTH_SECRET_TOKEN: {
    required: true,
  },
  VECTARA_API_KEY: {
    required: true,
  },
  NYLAS_API_URI: {
    required: true,
  },
  NYLAS_CLIENT_ID: {
    required: true,
  },
  NYLAS_API_KEY: {
    required: true,
  },
  BOX_SIGNATURE_PRIMARY: {
    required: true,
  },
  BOX_SIGNATURE_SECONDARY: {
    required: true,
  },
  REDIS_URL: {
    required: true,
  },
  NYLAS_CALENDAR_WEBHOOK_SECRET: {
    required: true,
  },
  NYLAS_EMAIL_WEBHOOK_SECRET: {
    required: true,
  },
  ASSEMBLY_AI_API_KEY: {
    required: true,
  },
  USER_SIGN_UP_TOKEN: {
    required: true,
  },
  MISTRAL_API_KEY: { required: true },
  POSTGRES_USER: { required: true },
  POSTGRES_PASSWORD: { required: true },
  POSTGRES_HOST: { required: true },
  HF_INFERENCE_TOKEN: { required: true },
  GOOGLE_SHEETS_SCRIPT_URL: { required: true },
  PUBLIC_APP_URI: { required: true },
};
