import { describe, expect, test, vi, afterEach } from "vitest";
import { generateCsvAndNotify } from "./_exporter";
import { prepareFilterQuery } from "~/utils/assets";
import { uploadToS3 } from "~/datasources/db/_utils/_s3";
import { getOemUserChannelId } from "~/utils/chat";
import { OptionalDetailedExportType } from "./_types";

const _id = "6499a52b5dfe76648ff67a97";

const detailExport: OptionalDetailedExportType = "";

const user = { _id, id: _id, organization: _id, username: "A" };

const dataSources = {
  Machine: {
    aggregate: vi.fn(),
    getAggergationOptions: vi.fn(),
  },
  CustomAdditionalField: {
    getMany: vi.fn(),
  },
  Oem: {
    getById: vi.fn(),
  },
  AppConfig: {
    getOne: vi.fn(),
  },
  PubnubApi: {
    publishMessage: vi.fn(),
  },
};

vi.mock("~/utils/assets", async () => ({
  prepareFilterQuery: vi.fn(),
}));

vi.mock("~/datasources/db/_utils/_s3", async () => ({
  uploadToS3: vi.fn(),
}));

vi.mock("~/utils/logger", async () => ({
  default: {
    error: vi.fn(),
  },
}));

vi.mock("./_consts", async () => {
  const actual = await vi.importActual("./_consts");
  return {
    // @ts-ignore
    ...actual,
    LOOKUP_AND_PROCESSING: () => ({
      Machine: [
        {
          $lookup: {
            from: "Customer",
          },
        },
      ],
    }),
    PROJECT: () => ({
      Machine: {
        name: 1,
        serialNumber: 1,
      },
    }),
  };
});

describe("Exporter _exporter.test.ts", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  test("success", async () => {
    const $match = { oem: _id, deleted: false };
    const $sort = ["name:asc"];
    // @ts-ignore
    prepareFilterQuery.mockResolvedValueOnce({ filterQuery: {}, query: {} });
    // @ts-ignore
    uploadToS3.mockResolvedValueOnce("someUrl");

    dataSources.Machine.getAggergationOptions.mockReturnValue({
      $match,
      $sort,
    });
    dataSources.AppConfig.getOne.mockResolvedValueOnce({
      features: [],
    });
    dataSources.Oem.getById.mockResolvedValueOnce({
      _id,
      paidFeatures: ["teams", "procedures"],
    });
    dataSources.CustomAdditionalField.getMany.mockResolvedValueOnce([{ _id }]);
    dataSources.Machine.aggregate.mockResolvedValueOnce([]);

    const resource = "Machine";
    const uuid = "19ed93db-5c20-4dc1-9296-4f6a6a3297a5";
    await generateCsvAndNotify({
      dataSources,
      // @ts-ignore
      user,
      args: {
        params: {},
      },
      resource,
      uuid,
      detailExport,
    });

    expect(prepareFilterQuery).toHaveBeenCalledOnce();
    expect(dataSources.Machine.getAggergationOptions).toHaveBeenCalledOnce();
    expect(dataSources.Oem.getById).toHaveBeenCalledOnce();
    expect(dataSources.Oem.getById).toHaveBeenCalledWith(_id, {
      select: {
        "statuses.label": 1,
        "statuses._id": 1,
        ticketTypes: 1,
        paidFeatures: 1,
        assetTypes: 1,
      },
    });
    expect(dataSources.CustomAdditionalField.getMany).toHaveBeenCalledOnce();
    expect(dataSources.CustomAdditionalField.getMany).toHaveBeenCalledWith(
      {
        enabled: true,
        oem: _id,
        type: "machines",
        sort: ["order:asc"],
      },
      { select: { label: 1 } },
    );
    expect(dataSources.Machine.aggregate).toHaveBeenCalledOnce();
    expect(dataSources.Machine.aggregate).toHaveBeenCalledWith([
      { $match },
      { $lookup: { from: "Customer" } },
      { $project: { name: 1, serialNumber: 1 } },
      { $sort },
    ]);
    expect(uploadToS3).toHaveBeenCalledOnce();
    expect(dataSources.PubnubApi.publishMessage).toHaveBeenCalledOnce();
    expect(dataSources.PubnubApi.publishMessage).toHaveBeenCalledWith({
      channel: getOemUserChannelId(_id),
      message: {
        error: "",
        payload: {
          resource,
          url: "someUrl",
          user: _id,
          uuid,
        },
        success: true,
        text: "csvExport",
      },
      meta: {
        isCsvExport: true,
      },
    });
  });

  test("error", async () => {
    // @ts-ignore

    dataSources.AppConfig.getOne.mockResolvedValueOnce({
      features: [],
    });
    dataSources.Oem.getById.mockResolvedValueOnce({
      _id,
      paidFeatures: ["teams", "procedures"],
    });

    // @ts-ignore
    prepareFilterQuery.mockImplementation(() => {
      throw new Error("Something went wrong");
    });
    const resource = "Machine";
    const uuid = "19ed93db-5c20-4dc1-9296-4f6a6a3297a5";
    await generateCsvAndNotify({
      dataSources,
      // @ts-ignore
      user,
      args: {
        params: {},
      },
      resource,
      uuid,
    });

    expect(dataSources.PubnubApi.publishMessage).toHaveBeenCalledOnce();
    expect(dataSources.PubnubApi.publishMessage).toHaveBeenCalledWith({
      channel: getOemUserChannelId(_id),
      message: {
        error: "Error: Something went wrong",
        payload: {
          resource,
          url: "",
          user: _id,
          uuid,
        },
        success: false,
        text: "csvExport",
      },
      meta: {
        isCsvExport: true,
      },
    });
  });
});
