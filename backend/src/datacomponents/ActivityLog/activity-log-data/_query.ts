import { roles } from "~/directives";
import { InputQueryParams } from "~/graphql/types";
import { IContext } from "~/types/common";
import { buildQueryParams } from "~/utils";

const { technician } = roles.is;

export const queryTypes = `#graphql
  type Query {
    listOwnOemActivityLogs(params: InputQueryParams): PaginatedActivityLogs @${technician}
  }
`;

export const queryResolvers = {
  Query: {
    listOwnOemActivityLogs: async (
      _: any,
      args: { params: InputQueryParams },
      { dataSources, user }: IContext,
    ) => {
      const { skip, limit } = args.params || {};

      const queryPayload = {
        oem: user.organization,
        ...buildQueryParams(args),
        sort: ["createdAt:desc", "_id:asc"],
      };

      const [logs, totalCount] = await Promise.all([
        dataSources.ActivityLog.getMany(queryPayload),
        dataSources.ActivityLog.totalCount({
          ...queryPayload,
          limit: -1,
          skip: 0,
        }),
      ]);

      return {
        logs,
        limit,
        skip,
        currentPage: skip && limit ? Math.floor(skip / limit + 1) : 1,
        totalCount,
      };
    },
  },
};
