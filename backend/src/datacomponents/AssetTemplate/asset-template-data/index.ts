import { typeResolvers, types } from "~/datacomponents/AssetTemplate/asset-template-data/type";
import {
  queryResolvers,
  queryTypes,
} from "~/datacomponents/AssetTemplate/asset-template-data/query";
import inputTypes from "~/datacomponents/AssetTemplate/asset-template-data/input";
import {
  mutationResolvers,
  mutationTypes,
} from "~/datacomponents/AssetTemplate/asset-template-data/mutation";

export default {
  types: `
    ${types}
    ${queryTypes}
    ${inputTypes}
    ${mutationTypes}
  `,
  resolvers: Object.assign(queryResolvers, mutationResolvers, typeResolvers),
};
