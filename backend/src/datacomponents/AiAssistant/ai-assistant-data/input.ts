export default `#graphql
    input InputAddAiAssistantDocuments {
        _id: ID!
        files: [String!]!
    }

    input InputCreateAiAssistantV2 {
        assetId: ID
        assetTemplateId: ID
        assistantType: String!
        description: String!
        documentIds: [String!]!
        name: String!
    }

    input InputQueryAiAssistant {
        chatID: String
        dbChatId: String
        id: String!
        query: String!
    }

    input InputRemoveAiAssistantDocuments {
        _id: ID!
        documentIds: [String!]!
    }

    input InputRenameAiAssistant {
        _id: ID!
        assistantName: String!
    }

    input InputUpdateAiAssistant {
        _id: ID!
        assistantName: String!
        description: String!
    }
`;
