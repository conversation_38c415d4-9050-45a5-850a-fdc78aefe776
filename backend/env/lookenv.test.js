module.exports = {
  AWS_ACCESS_KEY_ID: {
    default: "",
  },
  AWS_SECRET_ACCESS_KEY: {
    default: "",
  },
  AWS_CLOUDFRONT_URL: {
    default: "",
  },
  AWS_S3_BUCKET: {
    default: "",
  },
  AWS_S3_BUCKET_REGION: {
    default: "",
  },
  AWS_S3_SIGNED_URL_EXPIRATION: {
    default: 0,
  },
  DB_CONNECTION_STRING: {
    default: "mongodb://localhost:27017/makula-test-db",
  },
  AUTH_SECRET_TOKEN: {
    default: "test",
  },
  AUTH_SECRET_REFRESH_TOKEN: {
    default: "test",
  },
  MACHINE_URL_MODIFIER: {
    default: "m",
  },
  MAIL_API: {
    default: "",
  },
  MAIL_DOMAIN: {
    default: "",
  },
  MAIL_FROM_EMAIL: {
    default: "",
  },
  CHAT_SUBSCRIBE_KEY: {
    required: false,
  },
  CHAT_PUBLISH_KEY: {
    required: false,
  },
  CHAT_SECRET_KEY: {
    required: false,
  },
  BOX_CLIENT_ID: {
    required: false,
  },
  BOX_CLIENT_SECRET: {
    required: false,
  },
  BOX_PUBLIC_KEY_ID: {
    required: false,
  },
  BOX_PRIVATE_KEY: {
    required: false,
  },
  BOX_PASSPHRASE: {
    required: false,
  },
  BOX_ENTERPRISE_ID: {
    required: false,
  },
  SERVER_ENVIRONMENT: {
    required: false,
  },
  GOOGLE_PLACE_API_KEY: {
    required: false,
  },
  DROMO_FRONTEND_LICENCE_KEY: { required: false },
  DROMO_BACKEND_LICENCE_KEY: { required: false },
  NODE_ENV: { default: "test" },
  NEW_RELIC_API_KEY: {
    required: false,
  },
  NEW_RELIC_SERVICE_NAME: {
    required: false,
  },
  EMAIL_FILE_PASSWORD: {
    required: false,
  },
  SERVER_URI: {
    required: false,
  },
  ALLOWED_ORIGINS: {
    required: false,
  },
  OEM_APP_URI: {
    required: false,
  },
  FACILITY_APP_URI: {
    required: false,
  },
  QR_APP_URI: {
    required: false,
  },
  FACILITY_APP_VERSION: {
    required: false,
  },
  OEM_APP_VERSION: {
    required: false,
  },
  OEM_IOS_APP_VERSION: {
    required: false,
  },
  OEM_ANDROID_APP_VERSION: {
    required: false,
  },
  OEM_INTERCOM_SECRET: {
    required: false,
  },
  OEM_IOS_INTERCOM_SECRET: {
    required: false,
  },
  OEM_ANDROID_INTERCOM_SECRET: {
    required: false,
  },
  MACHINE_3D_AUTH_SECRET_TOKEN: {
    required: false,
  },
  VECTARA_API_KEY: {
    required: false,
  },
  NYLAS_API_URI: {
    required: false,
  },
  NYLAS_CLIENT_ID: {
    required: false,
  },
  NYLAS_API_KEY: {
    required: false,
  },
  BOX_SIGNATURE_PRIMARY: {
    default: "",
  },
  BOX_SIGNATURE_SECONDARY: {
    default: "",
  },
  REDIS_URL: {
    default: "",
  },
  NYLAS_CALENDAR_WEBHOOK_SECRET: {
    default: "",
  },
  NYLAS_EMAIL_WEBHOOK_SECRET: {
    default: "",
  },
  ASSEMBLY_AI_API_KEY: {
    default: "",
  },
  USER_SIGN_UP_TOKEN: {
    default: "",
  },
  MISTRAL_API_KEY: { default: "" },
  POSTGRES_USER: { default: "" },
  POSTGRES_PASSWORD: { default: "" },
  POSTGRES_HOST: { default: "" },
  HF_INFERENCE_TOKEN: { default: "" },
  GOOGLE_SHEETS_SCRIPT_URL: { default: "" },
  PUBLIC_APP_URI: { default: "" },
};
