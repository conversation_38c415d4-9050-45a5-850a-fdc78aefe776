import to from "await-to-js";
import { Types } from "mongoose";
import type { CreateContact } from "~/types/contact";
import { throwIfError } from "~/utils";
import InvalidInputError from "~/errors/InvalidInputError";
import type { IOem } from "~/datamodels/Oem/interface";
import type { ICustomer } from "~/datamodels/Customer/interface";
import type { IContact } from "~/datamodels/Contact/interface";
import { ERROR } from "~/environment";
import ConflictError from "~/errors/ConflictError";
import { MONGODB_ERRORS } from "~/errors/constants";

export const createContact = async ({ args, dataSources, user, translate }: CreateContact) => {
  const { input } = args;
  if (!input) throw new InvalidInputError("Invalid Input");
  const [oem, connection]: [IOem, ICustomer] = await Promise.all([
    dataSources.Oem.loadOne(new Types.ObjectId(user.organization)),
    dataSources.Customer.loadOne(new Types.ObjectId(input.connection)),
  ]);
  if (!oem || !connection || connection.oem?.toString() !== user.organization?.toString()) {
    throw new InvalidInputError(translate("error_messages.invalid_connection_id"));
  }

  const isSameOemMember = await dataSources.User.getOne({
    oem: user.organization,
    email: input.email,
  });

  if (isSameOemMember) {
    throw new ConflictError(translate("error_messages.email_already_in_oem"), { emailError: true });
  }

  const alreadyExists = await dataSources.Contact.getOne({
    email: input.email,
    oem: user.organization,
  });

  if (alreadyExists) {
    const existingConnection: ICustomer = await dataSources.Customer.getOne({
      _id: alreadyExists.connection,
    });
    throw new ConflictError(
      `${translate("error_messages.already_linked_email").replace("{}", existingConnection.name!)}`,
      { emailError: true },
    );
  }

  const [creationError, contact]: [any, IContact | undefined] = await to(
    dataSources.Contact.save({
      name: input.name,
      email: input.email,
      jobTitle: input.jobTitle,
      landline: input.landline,
      phoneNumber: input.phoneNumber,
      user: input.user,
      oem: user.organization,
      connection: input.connection,
      createdBy: user.id,
    }),
  );
  if ((creationError as any)?.code === MONGODB_ERRORS.DUPLICATE_KEY_ERROR_CODE) {
    const existingContact: IContact = await dataSources.Contact.getOne({
      email: input.email,
    });
    if (existingContact && existingContact.connection?.toString() !== input.connection) {
      const existingConnection: ICustomer = await dataSources.Customer.getOne({
        _id: existingContact.connection,
      });
      throw new ConflictError(
        `${translate("error_messages.already_linked_email").replace(
          "{}",
          existingConnection.name!,
        )}`,
        { emailError: true },
      );
    }
    throw new ConflictError(
      `Contact ${translate("error_messages.already_exists_id").replace("{id}", input.email)}`,
    );
  } else {
    throwIfError(creationError, ERROR.USER.BAD_USER_INPUT);
  }
  return contact;
};
