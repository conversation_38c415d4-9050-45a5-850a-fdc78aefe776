// eslint-disable-next-line import/no-extraneous-dependencies
import { afterAll, afterEach, beforeAll, describe, expect, test, vi } from "vitest";
import { Types } from "mongoose";
import axios from "axios";
import { encrypt, logger } from "~/utils";
import { getFileUrl, removeThreadsFromTickets, sendEmailWithAttachmentsAndNotify } from "./_helper";
import { IUser } from "~/datamodels/User/interface";

const OEM_ID = new Types.ObjectId("6499a52b5dfe76648ff67a81");

const originalEnv = process.env;
const EMAIL_FILE_PASSWORD = "*********";
const SERVER_URI = "https://www.example.com";

const user: IUser = {
  _id: "test-user-id",
  username: "admin",
  organization: OEM_ID,
};

const oem = {
  _id: OEM_ID,
  emailAccount: {
    grantId: "test-grant-id",
    accessToken: "abcdefghi",
    emailAddress: "<EMAIL>",
  },
};

const dataSources = {
  Oem: { getOne: vi.fn(), updateOne: vi.fn(), save: vi.fn() },
  Ticket: { updateManyByQuery: vi.fn(), save: vi.fn() },
  EmailThread: { getOne: vi.fn(), updateOne: vi.fn() },
  EmailMessage: { save: vi.fn() },
  NylasApi: { sendEmail: vi.fn(), getThread: vi.fn() },
  PubnubApi: { publishMessage: vi.fn() },
};

vi.mock("axios");
vi.mock("~/utils", async () => ({
  encrypt: vi.fn(),
  getFileUrl: vi.fn(),
  getOemUserChannelId: vi.fn().mockReturnValue("test-channel"),
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
  throwIfError: vi.fn(),
}));

describe("Helper", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("getFileUrl", async () => {
    beforeAll(() => {
      process.env = {
        ...originalEnv,
        EMAIL_FILE_PASSWORD,
        SERVER_URI,
      };
    });
    afterAll(() => {
      process.env = originalEnv;
    });

    test("get hash of encrypted file upload urls", async () => {
      const fileKey = "abcdgeidhwidvucbkjdsbjdhscvjdksc123p323i7rt";
      const fileId = "abcdgehur";
      const fileId2 = "hdiukjdbs";
      // @ts-ignore
      encrypt.mockImplementation(() => fileKey);
      const message = {
        attachments: [
          {
            isInline: true,
            id: fileId,
            contentId: fileId,
          },
          {
            isInline: true,
            id: fileId2,
            contentId: fileId2,
          },
        ],
      };
      // @ts-ignore
      const result = getFileUrl(message, user);
      expect(result).toStrictEqual({
        [fileId]: `${SERVER_URI}/email/file/${fileKey}`,
        [fileId2]: `${SERVER_URI}/email/file/${fileKey}`,
      });
    });
  });

  describe("removeThreadsFromTickets", async () => {
    test("remove all thread refs from all oem tickets", async () => {
      await removeThreadsFromTickets({
        oem: OEM_ID,
        dataSources,
      });
      expect(dataSources.Ticket.updateManyByQuery).toHaveBeenCalledOnce();
      expect(dataSources.Ticket.updateManyByQuery).toHaveBeenCalledWith(
        { oem: { $exists: true, $eq: OEM_ID } },
        { $set: { emailThreads: [] } },
      );
      expect(logger.info).toHaveBeenCalledOnce();
    });
  });

  describe("sendEmailWithAttachmentsAndNotify", () => {
    afterEach(() => {
      vi.restoreAllMocks();
    });

    test("should send email with attachments and notify successfully", async () => {
      const ticketId = "test-ticket-id";
      const to = ["<EMAIL>"];
      const cc = ["<EMAIL>"];
      const bcc = ["<EMAIL>"];
      const subject = "Test Subject";
      const body = "Test Body";
      const fileUrls = ["http://example.com/testfile1.txt", "http://example.com/testfile2.txt"];
      const replyToMessageId = "test-reply-id";
      const uuid = "test-uuid";

      const fileData = {
        data: Buffer.from("test file content"),
        headers: { "content-type": "text/plain" },
      };

      // @ts-ignore
      axios.get.mockResolvedValue(fileData);
      dataSources.NylasApi.sendEmail.mockResolvedValue({ threadId: "test-thread-id" });
      dataSources.NylasApi.getThread.mockResolvedValue({ threadId: "test-thread-id" });
      dataSources.EmailThread.getOne.mockResolvedValue({ threadId: "test-thread-id" });
      dataSources.EmailMessage.save.mockResolvedValue({ threadId: "test-thread-id" });

      await sendEmailWithAttachmentsAndNotify({
        dataSources,
        // @ts-ignore
        oem,
        ticket: { _id: ticketId },
        user,
        to,
        cc,
        bcc,
        subject,
        body,
        fileUrls,
        replyToMessageId,
        uuid,
      });

      expect(axios.get).toHaveBeenCalledTimes(fileUrls.length);
      expect(dataSources.NylasApi.sendEmail).toHaveBeenCalledOnce();
      expect(dataSources.Ticket.save).toHaveBeenCalledOnce();
      expect(dataSources.PubnubApi.publishMessage).toHaveBeenCalledOnce();
      expect(dataSources.EmailThread.getOne).toHaveBeenCalledOnce();

      expect(logger.error).not.toHaveBeenCalled();
    });

    test("should handle error when sending email fails", async () => {
      const ticketId = "test-ticket-id";
      const to = ["<EMAIL>"];
      const cc = ["<EMAIL>"];
      const bcc = ["<EMAIL>"];
      const subject = "Test Subject";
      const body = "Test Body";
      const fileUrls = ["http://example.com/testfile1.txt", "http://example.com/testfile2.txt"];
      const replyToMessageId = "test-reply-id";
      const uuid = "test-uuid";

      const error = new Error("Test Error");

      // @ts-ignore
      axios.get.mockRejectedValue(error);

      await sendEmailWithAttachmentsAndNotify({
        dataSources,
        // @ts-ignore
        oem,
        ticketId,
        user,
        to,
        cc,
        bcc,
        subject,
        body,
        fileUrls,
        replyToMessageId,
        uuid,
      });

      expect(axios.get).toHaveBeenCalledTimes(fileUrls.length);
      expect(dataSources.NylasApi.sendEmail).not.toHaveBeenCalled();
      expect(dataSources.Ticket.save).not.toHaveBeenCalled();
      expect(dataSources.PubnubApi.publishMessage).toHaveBeenCalledOnce();

      expect(logger.error).toHaveBeenCalledOnce();
    });
  });
});
