module.exports = {
  AWS_ACCESS_KEY_ID: {
    required: true,
  },
  AWS_SECRET_ACCESS_KEY: {
    required: true,
  },
  AWS_CLOUDFRONT_URL: {
    required: true,
  },
  AWS_S3_BUCKET: {
    required: true,
  },
  AWS_S3_BUCKET_REGION: {
    required: true,
  },
  AWS_S3_SIGNED_URL_EXPIRATION: {
    default: 60,
  },
  DB_CONNECTION_STRING: {
    default: "mongodb://localhost:27017/makula",
  },
  AUTH_SECRET_TOKEN: {
    default: "1234",
  },
  AUTH_SECRET_REFRESH_TOKEN: {
    default: "123456789",
  },
  MACHINE_URL_MODIFIER: {
    default: "m",
  },
  MAIL_API: {
    default: "",
  },
  MAIL_DOMAIN: {
    default: "",
  },
  MAIL_FROM_EMAIL: {
    default: "",
  },
  CHAT_SUBSCRIBE_KEY: {
    required: true,
  },
  CHAT_PUBLISH_KEY: {
    required: true,
  },
  CHAT_SECRET_KEY: {
    required: true,
  },
  BOX_CLIENT_ID: {
    required: true,
  },
  BOX_CLIENT_SECRET: {
    required: true,
  },
  BOX_PUBLIC_KEY_ID: {
    required: true,
  },
  BOX_PRIVATE_KEY: {
    required: true,
  },
  BOX_PASSPHRASE: {
    required: true,
  },
  BOX_ENTERPRISE_ID: {
    required: true,
  },
  SERVER_ENVIRONMENT: {
    required: false,
  },
  GOOGLE_PLACE_API_KEY: {
    required: true,
  },
  DROMO_FRONTEND_LICENCE_KEY: { required: true },
  DROMO_BACKEND_LICENCE_KEY: { required: true },
  NODE_ENV: { default: "development" },
  NEW_RELIC_API_KEY: {
    required: false,
  },
  NEW_RELIC_SERVICE_NAME: {
    required: false,
  },
  EMAIL_FILE_PASSWORD: {
    required: true,
  },
  SERVER_URI: {
    required: true,
  },
  ALLOWED_ORIGINS: {
    required: true,
  },
  OEM_APP_URI: {
    required: true,
  },
  FACILITY_APP_URI: {
    required: true,
  },
  QR_APP_URI: {
    required: true,
  },
  FACILITY_APP_VERSION: {
    required: true,
  },
  OEM_APP_VERSION: {
    required: true,
  },
  OEM_IOS_APP_VERSION: {
    required: false,
  },
  OEM_ANDROID_APP_VERSION: {
    required: false,
  },
  OEM_INTERCOM_SECRET: {
    required: false,
  },
  OEM_IOS_INTERCOM_SECRET: {
    required: false,
  },
  OEM_ANDROID_INTERCOM_SECRET: {
    required: false,
  },
  MACHINE_3D_AUTH_SECRET_TOKEN: {
    required: true,
  },
  VECTARA_API_KEY: {
    required: true,
  },
  NYLAS_API_URI: {
    required: true,
  },
  NYLAS_CLIENT_ID: {
    required: true,
  },
  NYLAS_API_KEY: {
    required: true,
  },
  BOX_SIGNATURE_PRIMARY: {
    default: "",
  },
  BOX_SIGNATURE_SECONDARY: {
    default: "",
  },
  REDIS_URL: {
    default: "",
  },
  NYLAS_CALENDAR_WEBHOOK_SECRET: {
    default: "",
  },
  NYLAS_EMAIL_WEBHOOK_SECRET: {
    default: "",
  },
  NYLAS_EMAIL_MESSAGE_WEBHOOK_SECRET: {
    default: "",
  },
  NYLAS_EMAIL_FOLDER_WEBHOOK_SECRET: {
    default: "",
  },
  ASSEMBLY_AI_API_KEY: {
    default: "",
  },
  USER_SIGN_UP_TOKEN: {
    default: "",
  },
  MISTRAL_API_KEY: { default: "" },
  POSTGRES_USER: { default: "" },
  POSTGRES_PASSWORD: { default: "" },
  POSTGRES_HOST: { default: "" },
  HF_INFERENCE_TOKEN: { default: "" },
  GOOGLE_SHEETS_SCRIPT_URL: { default: "" },
  PUBLIC_APP_URI: { default: "" },
};
