import { describe, it, expect, vi, beforeEach } from "vitest";
import { Types } from "mongoose";
import { deleteContact } from "~/services/contact/delete";
import InvalidInputError from "~/errors/InvalidInputError";
import { FORBIDDEN } from "~/environment";

describe("deleteContact", () => {
  // Using actual ObjectId instances
  const validObjectId = new Types.ObjectId();
  const organizationId = new Types.ObjectId();
  const userId = new Types.ObjectId();

  const mockUser = {
    id: userId.toString(),
    organization: organizationId.toString(),
  };

  const mockInput = {
    id: validObjectId.toString(),
  };

  const mockContact = {
    _id: validObjectId,
    oem: organizationId,
    createdBy: userId,
    // other contact fields would go here
  };

  const mockDataSources = {
    Contact: {
      loadOne: vi.fn(),
      softDeleteById: vi.fn(),
    },
    ConnectionRequest: {
      getOne: vi.fn(),
      softDeleteById: vi.fn(),
    },
  };

  const mockTranslate = vi.fn(key => key);

  beforeEach(() => {
    vi.clearAllMocks();
    mockDataSources.Contact.loadOne.mockResolvedValue(mockContact);
    mockDataSources.Contact.softDeleteById.mockResolvedValue(true);
  });

  it("should successfully delete a contact", async () => {
    const result = await deleteContact({
      args: { input: mockInput },
      dataSources: mockDataSources,
      user: mockUser,
      translate: mockTranslate,
    });

    expect(result).toBe(mockInput.id);
    expect(mockDataSources.Contact.loadOne).toHaveBeenCalledWith(new Types.ObjectId(mockInput.id));
    expect(mockDataSources.Contact.softDeleteById).toHaveBeenCalledWith(mockInput.id);
  });

  it("should throw InvalidInputError when input is missing", async () => {
    await expect(
      deleteContact({
        args: { input: null },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(InvalidInputError);
  });

  it("should throw InvalidInputError when id is missing", async () => {
    await expect(
      deleteContact({
        args: { input: { id: null } },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(InvalidInputError);
  });

  it("should throw InvalidInputError when contact is not found", async () => {
    mockDataSources.Contact.loadOne.mockResolvedValue(null);

    await expect(
      deleteContact({
        args: { input: mockInput },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(InvalidInputError);
  });

  it("should throw InvalidInputError when contact oem does not match user organization", async () => {
    const differentOrgId = new Types.ObjectId();
    mockDataSources.Contact.loadOne.mockResolvedValue({
      ...mockContact,
      oem: differentOrgId,
    });

    await expect(
      deleteContact({
        args: { input: mockInput },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(InvalidInputError);
  });

  it("should throw Forbidden error when user is not the creator", async () => {
    const differentUserId = new Types.ObjectId();
    mockDataSources.Contact.loadOne.mockResolvedValue({
      ...mockContact,
      createdBy: differentUserId,
    });

    await expect(
      deleteContact({
        args: { input: mockInput },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(FORBIDDEN);
  });

  it("should handle database errors during contact load", async () => {
    const dbError = new Error("Database error");
    mockDataSources.Contact.loadOne.mockRejectedValue(dbError);

    await expect(
      deleteContact({
        args: { input: mockInput },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow(dbError);
  });

  it("should handle database errors during contact deletion", async () => {
    const dbError = new Error("Delete error");
    mockDataSources.Contact.softDeleteById.mockRejectedValue(dbError);

    await expect(
      deleteContact({
        args: { input: mockInput },
        dataSources: mockDataSources,
        user: mockUser,
        translate: mockTranslate,
      }),
    ).rejects.toThrow();
  });
});
