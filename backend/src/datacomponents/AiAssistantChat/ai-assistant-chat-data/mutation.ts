import { GraphQLResolveInfo } from "graphql";
import { ExecutionContext } from "graphql/execution/execute";
import { roles, features } from "~/directives";
import { IContext } from "~/types/common";
import { deleteAiAssistantChat } from "~/services/aiAssistantChat/delete";
import { renameAiAssistantChat } from "~/services/aiAssistantChat/update";
import { createAiAssistantChat } from "~/services/aiAssistantChat/create";

const { aiAssistants } = features.oemOwns;
const { staff } = roles.is;

export const mutationTypes = `#graphql
  type Mutation {
    createAiAssistantChat(aiAssistantId: ID!): AiAssistantChat @${aiAssistants} @${staff}
    deleteAiAssistantChat(aiAssistantChatId: ID!): ID @${aiAssistants} @${staff}
    renameAiAssistantChat(input: InputRenameAiAssistantChat!): ID @${aiAssistants} @${staff}
  }
`;

export const mutationResolvers = {
  Mutation: {
    createAiAssistantChat: async (
      _: ExecutionContext["contextValue"],
      { aiAssistantId }: { aiAssistantId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      createAiAssistantChat({
        args: { input: { aiAssistantId }, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    deleteAiAssistantChat: async (
      _: ExecutionContext["contextValue"],
      { aiAssistantChatId }: { aiAssistantChatId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      deleteAiAssistantChat({
        args: { input: { aiAssistantChatId }, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    renameAiAssistantChat: async (
      _: ExecutionContext["contextValue"],
      {
        input: { aiAssistantChatId, aiAssistantChatName },
      }: {
        input: { aiAssistantChatId: string; aiAssistantChatName: string };
      },
      { dataSources, user, req: { t: translate } }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      renameAiAssistantChat({
        args: {
          input: { aiAssistantChatId, aiAssistantChatName },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),
  },
};
