import to from "await-to-js";
import { throwIfError } from "../../../utils";
import { ERROR } from "~/environment";

export const queryTypes = `
  type Query {
    getAppConfig: AppConfig
  }
`;

export const queryResolvers = {
  Query: {
    getAppConfig: async (_, __, { dataSources }) => {
      const [err, data] = await to(dataSources.AppConfig.getOne());

      throwIfError(err, ERROR.USER.BAD_USER_INPUT);

      return data;
    },
  },
};
