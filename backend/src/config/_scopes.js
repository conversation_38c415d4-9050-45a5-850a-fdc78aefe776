import ROLES_PERMISSIONS from "$/settings/roles-permissions.json";
const { USERS } = ROLES_PERMISSIONS;

const buildRoles = roles => {
  return roles.reduce(
    (obj, type, index) => ({
      ...obj,
      [Object.keys(type)[0]]: {
        RANK: index * 2,
        VALUE: Object.keys(type)[0],
      },
    }),
    {},
  );
};

const ROLES = buildRoles(USERS);

// uncomment if you need a more detailed view of the roles' hierarchy
// console.log('####################################################');
// console.log('ROLES HIERARCHY WITH RANK, LEVEL AND CONTAINERS');
// console.log('####################################################');
// console.log(ROLES);

export default {
  ROLES,
};
