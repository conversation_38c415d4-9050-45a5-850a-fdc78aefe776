import Types from "$/settings/enums/customAdditionalField/_type.json";
import FieldTypes from "$/settings/enums/customAdditionalField/_field-type.json";
import { getEnums } from "~/utils";

const type = getEnums(Types, "graphql");
const fieldTypes = getEnums(FieldTypes, "graphql");

export const types = `
  enum Types {
    ${type}
  }

  enum FieldTypes {
    ${fieldTypes}
  }
  
  enum VisibilityScope {
    internal
    external
  }

  type OptionResponse {
    _id: ID
    color: String
    description: String
    value: String
  }

  type AdditionalField {
    _id: ID!
    type: String
    slug: String
    label: String
    fieldType: String
    options: [OptionResponse]
    isAdditionalField: Boolean
    visibilityScope: VisibilityScope
    enabled: Boolean
    createdBy: ID
    created_at: String
    updated_at: String
  }

  type CustomAdditionalField {
    _id: ID!
    description: String
    type: String
    slug: String
    label: String
    fieldType: String
    options: [OptionResponse]
    visibilityScope: VisibilityScope
    isAdditionalField: Boolean
    enabled: Boolean
    oem: Oem
    createdBy: ID
    order: Int
    created_at: String
    updated_at: String
  }
`;

export const typeResolvers = {
  CustomAdditionalField: {
    oem: ({ oem }, _, { dataSources }) => dataSources.Oem.loadOne(oem),
  },
};
