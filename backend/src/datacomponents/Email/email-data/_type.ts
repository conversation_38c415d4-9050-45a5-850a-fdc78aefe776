export const types = `#graphql
  type PaginatedEmailContacts {    
    pageToken: String
    contacts: [<PERSON>SO<PERSON>]
  }
  
  type PaginatedEmailDrafts {
    pageToken: String
    drafts: [JSON]
  }

  type EmailDraft {
    draft: JSON
    fileUrl: JSO<PERSON>
  }

  type DraftJunkCount {
    drafts: Int
    junk: Int
  }
`;

export const typeResolvers = {
  //
};

export type AttachmentType = {
  content: string;
  contentType: string;
  filename: string;
  content_id: string;
  contentId: string;
  size: number;
};
