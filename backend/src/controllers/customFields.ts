import { Request, Response } from "express";
import { ICustomAdditionalField } from "~/datamodels/CustomAdditionalField/interface";
import { getAllCustomFields } from "~/services/customFields";
import { serviceFnInputGenerator } from "~/utils/inputGenerator";
import { getEnums } from "~/utils/_get-enums";
import CUSTOM_FIELD_TYPE from "$/settings/enums/customAdditionalField/_custom-field-type.json";
import { controllerErrorHandler } from "~/utils/controllerErrorHandler";

const CUSTOM_FIELD_TYPES = getEnums(CUSTOM_FIELD_TYPE, "reference");

const paramCustomFieldTypeMap = {
  asset: "machines",
  connection: "facilities",
  part: "parts",
  workOrder: "tickets",
  knowledgeBase: "knowledgeBase",
};

const CustomFieldTypeParamMap = {
  machines: "asset",
  facilities: "connection",
  parts: "part",
  tickets: "workOrder",
  knowledgeBase: "knowledgeBase",
};

const project = (customFields: ICustomAdditionalField[]) =>
  customFields.map((customField: ICustomAdditionalField) => ({
    id: customField._id,
    type: CustomFieldTypeParamMap[customField.type],
    label: customField.label,
    fieldType: customField.fieldType,
    ...(customField.fieldType === CUSTOM_FIELD_TYPES.singleSelect
      ? { options: customField.options?.map(option => ({ value: option.value })) }
      : {}),
  }));

export const getAllCustomFieldsController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    if (params.args.query.type) {
      // @ts-ignore
      params.args.query.type = paramCustomFieldTypeMap[params.args.query.type];
    }
    const customFields = await getAllCustomFields(params);
    return res.status(200).json({
      data: project(customFields),
    });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};
