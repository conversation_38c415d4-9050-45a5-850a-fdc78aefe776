/* eslint-disable */
// @ts-nocheck

import { expect, test, vi } from "vitest";
import { defineJob } from "~/agenda/helpers";
import runJob from "~/agenda/run";

const {
  agendaDefine,
  agendaEvery,
  agendaOn,
  agendaSchedule,
  firstJobName,
  interval,
  jobFunc,
  onJobFailure,
  secondJobName,
} = vi.hoisted(() => {
  const Random = require("random-seed-generator");

  return {
    agendaDefine: vi.fn(),
    agendaEvery: vi.fn(),
    agendaOn: vi.fn(),
    agendaSchedule: vi.fn(),
    firstJobName: Random.string({
      length: 24,
      pool: "ABCDEF0123456789",
    }),
    interval: "Every year",
    jobFunc: vi.fn(),
    onJobFailure: vi.fn(),
    secondJobName: Random.string({
      length: 24,
      pool: "ABCDEF0123456789",
    }),
  };
});

vi.mock("agenda", () => {
  class Agenda {
    cancel = vi.fn();

    define = agendaDefine;

    every = agendaEvery;

    jobs = vi.fn();

    on = agendaOn;

    schedule = agendaSchedule;
  }
  return { default: Agenda };
});

vi.mock("~/agenda/definitions", () => ({
  definitions: {
    [firstJobName]: {
      config: {},
      func: jobFunc,
      interval,
    },
    [secondJobName]: {
      config: {},
      func: jobFunc,
      interval,
      maxRetries: 2,
      onFailure: onJobFailure,
    },
  },
}));

const runAt = new Date();

test("Agenda define", async () => {
  const dataSources = {};
  const processedDefinitions = new Set();
  const save = vi.fn(() => new Promise(resolve => resolve()));

  defineJob({
    dataSources,
    name: firstJobName,
    processedDefinitions,
  });

  expect(agendaDefine).toHaveBeenCalledOnce();
  expect(agendaDefine.mock.calls[0][0]).toEqual(firstJobName);

  expect(agendaEvery).toHaveBeenCalledOnce();
  expect(agendaEvery).toHaveBeenCalledWith(interval, firstJobName);

  agendaDefine.mock.calls[0][2](null);

  expect(jobFunc).toHaveBeenCalledOnce();
  expect(jobFunc.mock.calls[0][0]).toEqual(dataSources);

  defineJob({
    dataSources,
    name: secondJobName,
    processedDefinitions,
  });

  expect(agendaOn).toHaveBeenCalledOnce();
  expect(agendaOn.mock.calls[0][0]).toEqual("fail");

  const job = {
    attrs: {
      failCount: 1,
      name: secondJobName,
    },
    save,
  };

  await agendaOn.mock.calls[0][1](null, job);

  expect(save).toHaveBeenCalledOnce();

  job.attrs.failCount = 2;
  await agendaOn.mock.calls[0][1](null, job);

  expect(onJobFailure).toHaveBeenCalledWith(dataSources, null, job);
});

test("Agenda run", async () => {
  const randomParameter = Random.string({
    length: 24,
    pool: "ABCDEF0123456789",
  });

  runJob({
    jobName: firstJobName,
    randomParameter,
    runAt,
  });

  expect(agendaSchedule).toHaveBeenCalledOnce();
  expect(agendaSchedule).toHaveBeenCalledWith(runAt, firstJobName, { randomParameter });
});
