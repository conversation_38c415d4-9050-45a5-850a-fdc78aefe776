/* eslint-disable */
// @ts-nocheck
import { Types } from "mongoose";
import Random from "random-seed-generator";
import { expect, test, vi } from "vitest";
import PAID_FEATURES from "$/settings/paid-features.json";
import { queryResolvers } from "~/datacomponents/AiAssistant/ai-assistant-data/query";
import { PRODUCT_TYPES } from "~/constants/plans";
import { getEnums } from "~/utils/_get-enums";
import { mockAppConfig } from "#/tests/mocks/AppConfig";
import { mockOemPaidFeatures } from "#/tests/mocks/Oem";

const paidFeaturesEnum = getEnums(PAID_FEATURES, "reference");

vi.mock("~/directives", () => ({
  roles: { is: { staff: "STAFF" } },
  features: { oemOwns: { aiAssistants: "aiAssistants" } },
}));

vi.mock("~/utils", () => ({
  getEnums: () =>
    Object.entries(PAID_FEATURES).reduce((acc, [key]) => ({
      ...acc,
      [key]: key,
    })),
  logger: {
    error: console.error,
    info: console.info,
    log: console.log,
    warn: console.warn,
  },
}));

const aiAssistantID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const aiAssistantName: string = Random.string({
  length: 24,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
});
const answer: string = Random.string({
  length: 24,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
});
const chatID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const machineID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const appConfigID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const oemID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const query: string = Random.string({
  length: 24,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
});
const turnID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const userID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});

test("Get machine assistant", async () => {
  const AiAssistantLoadOneQueryResponse = {
    _id: new Types.ObjectId(aiAssistantID),
    createdBy: new Types.ObjectId(userID),
    deleted: false,
    isSharedAssistant: false,
    machineID,
    oem: new Types.ObjectId(oemID),
  };
  const MachineLoadOnQueryResponse = {
    _id: new Types.ObjectId(machineID),
    oem: new Types.ObjectId(oemID),
    teams: [],
  };
  const AppConfigLoadOneQueryResponse = {
    _id: new Types.ObjectId(appConfigID),
    ...mockAppConfig,
  };
  const userContext = {
    id: new Types.ObjectId(userID),
    organization: new Types.ObjectId(oemID),
    teams: [],
  };

  const AiAssistantLoadOn = vi.fn(() => AiAssistantLoadOneQueryResponse);
  const MachineLoadOn = vi.fn(() => MachineLoadOnQueryResponse);
  const AppConfigLoadOne = vi.fn(() => AppConfigLoadOneQueryResponse);

  let response;

  const performQuery = async () =>
    (response = await queryResolvers.Query.getAiAssistant(
      null,
      { id: aiAssistantID },
      {
        dataSources: {
          AiAssistant: { loadOne: AiAssistantLoadOn },
          Machine: { loadOne: MachineLoadOn },
          AppConfig: { getOne: AppConfigLoadOne },
          Oem: {
            loadOne: () => ({
              paidFeatures: ["aiAssistants"],
              aiAssistantConfiguration: {
                allowedQueries: 10,
                allowedStorage: 128,
              },
              ...mockOemPaidFeatures,
            }),
          },
          User: {
            loadOne: () => ({ ...userContext, productAccess: [PRODUCT_TYPES.INDUSTRIAL_AI] }),
          },
        },
        user: userContext,
      },
      null,
    ));

  await performQuery();

  expect(AiAssistantLoadOn).toHaveBeenCalledWith(new Types.ObjectId(aiAssistantID));

  expect(response).toEqual(AiAssistantLoadOneQueryResponse);

  AiAssistantLoadOneQueryResponse.deleted = true;

  expect(performQuery).rejects.toThrowError();
});

test("Query all machine assistants", async () => {
  const AiAssistantGetManyQueryResponse = [
    {
      assistantName: aiAssistantName,
      machineID,
      oem: new Types.ObjectId(oemID),
    },
  ];
  const AppConfigLoadOneQueryResponse = {
    _id: new Types.ObjectId(appConfigID),
    ...mockAppConfig,
  };
  const AiAssistantCountDocumentsQueryResponse = {};
  const AiAssistantGetManyQuery = vi.fn(() => AiAssistantGetManyQueryResponse);
  const AiAssistantCountDocumentsQuery = vi.fn(() => AiAssistantCountDocumentsQueryResponse);
  const AppConfigLoadOne = vi.fn(() => AppConfigLoadOneQueryResponse);

  const response = await queryResolvers.Query.listAiAssistants(
    null,
    {},
    {
      dataSources: {
        AiAssistant: {
          getOne: vi.fn(() => null),
          getMany: AiAssistantGetManyQuery,
          countDocuments: AiAssistantCountDocumentsQuery,
        },
        AppConfig: { getOne: AppConfigLoadOne },
        Oem: {
          loadOne: () => ({
            paidFeatures: ["aiAssistants"],
            aiAssistantConfiguration: {
              allowedQueries: 10,
              allowedStorage: 128,
            },
            ...mockOemPaidFeatures,
          }),
        },
        User: { loadOne: () => ({ productAccess: [PRODUCT_TYPES.INDUSTRIAL_AI] }) },
      },
      user: {
        id: new Types.ObjectId(userID),
        organization: new Types.ObjectId(oemID),
      },
    },
    null,
  );

  expect(AiAssistantGetManyQuery).toHaveBeenCalledWith({
    deleted: false,
    oem: new Types.ObjectId(oemID),
  });
  response.aiAssistants.forEach((aiAssistant, index) =>
    Object.entries(aiAssistant).forEach(([key, value]) =>
      expect(AiAssistantGetManyQueryResponse[index][key]).toEqual(value),
    ),
  );
});

test("Query machine assistant", async () => {
  const AiAssistantLoadOneQueryResponse = {
    _id: new Types.ObjectId(aiAssistantID),
    createdBy: new Types.ObjectId(userID),
    deleted: false,
    isSharedAssistant: false,
    machineID,
    oem: new Types.ObjectId(oemID),
  };
  const MachineLoadOnQueryResponse = {
    _id: new Types.ObjectId(machineID),
    oem: new Types.ObjectId(oemID),
    teams: [],
  };
  const userContext = {
    id: new Types.ObjectId(userID),
    organization: new Types.ObjectId(oemID),
    teams: [],
  };
  const VectaraApiQueryResponse = {
    answer,
    chat_id: chatID,
    search_results: [],
    turn_id: turnID,
  };
  const AppConfigLoadOneQueryResponse = {
    _id: new Types.ObjectId(appConfigID),
    ...mockAppConfig,
  };
  const AiAssistantLoadOn = vi.fn(
    () => new Promise(resolve => resolve(AiAssistantLoadOneQueryResponse)),
  );
  const MachineLoadOn = vi.fn(() => new Promise(resolve => resolve(MachineLoadOnQueryResponse)));
  let OemFindOneAndUpdate = vi.fn(() => new Promise(resolve => resolve({})));
  let AiAssistantUsageFindOneAndUpdate = vi.fn(() => new Promise(resolve => resolve(null)));
  const OemSave = vi.fn(() => new Promise(resolve => resolve(null)));
  const VectaraApiQuery = vi.fn(() => new Promise(resolve => resolve(VectaraApiQueryResponse)));
  const AppConfigLoadOne = vi.fn(() => AppConfigLoadOneQueryResponse);

  let response;

  const performQuery = async () =>
    (response = await queryResolvers.Query.queryAiAssistant(
      null,
      { input: { id: aiAssistantID, query } },
      {
        dataSources: {
          AiAssistant: { loadOne: AiAssistantLoadOn },
          AiAssistantUsage: {
            AiAssistantUsage: {
              findOneAndUpdate: AiAssistantUsageFindOneAndUpdate,
            },
          },
          Machine: { loadOne: MachineLoadOn },
          AppConfig: { getOne: AppConfigLoadOne },
          Oem: {
            loadOne: () => ({
              paidFeatures: ["aiAssistants"],
              aiAssistantConfiguration: {
                allowedQueries: 10,
                allowedStorage: 128,
              },
              ...mockOemPaidFeatures,
            }),
            Oem: { findOneAndUpdate: OemFindOneAndUpdate },
            save: OemSave,
          },
          User: {
            loadOne: () =>
              new Promise(resolve =>
                resolve({ ...userContext, productAccess: [PRODUCT_TYPES.INDUSTRIAL_AI] }),
              ),
          },
          VectaraApi: {
            performQueryOnAssistant: VectaraApiQuery,
          },
        },
        user: userContext,
      },
      null,
    ));

  // should throw as it was not able to perform the update in DB
  expect(performQuery).rejects.toThrowError();

  AiAssistantUsageFindOneAndUpdate = vi.fn(() => new Promise(resolve => resolve({})));
  await performQuery();

  expect(VectaraApiQuery).toHaveBeenCalledWith(
    AiAssistantLoadOneQueryResponse._id,
    undefined,
    query,
  );
  expect(response).toEqual(VectaraApiQueryResponse);
});
