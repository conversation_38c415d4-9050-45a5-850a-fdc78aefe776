import { Types } from "mongoose";
import { IAiAssistant } from "~/datamodels/AiAssistant/interface";
import { IContext } from "~/types/common";
import { getAssetBoxFolderAccessToken } from "~/services/asset/fetch";
import { getAssetTemplateBoxFolderAccessToken } from "~/services/assetTemplate/fetch";

export const types = `#graphql
    type AiAssistant {
        _id: ID
        assistantName: String
        assistantType: String
        description: String
        documents: AiAssistantDocuments
        machine: Asset
        machineID: ID
        oem: ID
        oemDetails: Oem
        status: Int
        template: AssetTemplate
        templateId: ID
        createdBy: ID
        isSharedAssistant: Boolean
        boxAccessToken: String
    }

    type PaginatedAssistants {
        totalCount: Int
        limit: Int
        skip: Int
        currentPage: Int
        assistants: [AiAssistant]
    }

    type AiAssistantWithCount {
        aiAssistants: [AiAssistant]
        totalCount: Int
    }

    type AiAssistantDocument {
        _id: ID
        consumedStorage: Int
        externalStorageServiceDocumentID: String
        status: Int
    }

    type AiAssistantDocuments {
        externalDocuments: [AiAssistantDocument]
        internalDocuments: [AiAssistantDocument]
    }

    type AiAssistantQueryResponse {
        answer: String
        chat_id: String
        response_language: String
        search_results: [AiAssistantQuerySearchResult]
        turn_id: String
    }

    type AiAssistantQuerySearchResult {
        document_id: String
        part_metadata: AiAssistantQuerySearchResultPartMetadata
        score: Float
        text: String
    }

    type AiAssistantQuerySearchResultPartMetadata {
        page: Int
    }
`;

export const typeResolvers = {
  AiAssistant: {
    machine: async ({ machineID }: IAiAssistant, _: any, { dataSources }: IContext) =>
      await dataSources.Machine.loadOne(machineID),
    oemDetails: async ({ oem }: IAiAssistant, _: any, { dataSources }: IContext) =>
      await dataSources.Oem.loadOne(oem),
    template: async ({ templateId }: IAiAssistant, _: any, { dataSources }: IContext) =>
      await dataSources.MachineTemplate.loadOne(templateId),
    isSharedAssistant: async ({ oem }: IAiAssistant, _: any, { user }: IContext) =>
      !!oem && oem?.toString() !== user?.organization?.toString(),
    boxAccessToken: async (
      { oem, machineID, templateId }: IAiAssistant,
      _: any,
      { user: contextUser, dataSources }: IContext,
    ) => {
      if (oem?.toString() !== contextUser?.organization?.toString()) {
        if (machineID)
          return await getAssetBoxFolderAccessToken({
            dataSources,
            user: contextUser,
            // @ts-ignore
            args: {
              input: {
                assetId: machineID?.toString()!,
              },
            },
          });
        if (templateId)
          return await getAssetTemplateBoxFolderAccessToken({
            dataSources,
            user: contextUser,
            // @ts-ignore
            args: {
              input: {
                assetTemplateId: templateId?.toString()!,
              },
            },
          });
      }

      const user = await dataSources.User.loadOne(new Types.ObjectId(contextUser.id!));
      return user?.foldersAccessToken;
    },
  },
};
