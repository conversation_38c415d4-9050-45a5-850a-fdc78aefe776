export default `#graphql
type EmailParticipant {
    name: String
    email: String
}

type EmailThreadMessage {
    bcc: [EmailParticipant]
    cc: [EmailParticipant]
    date: Int
    from: [EmailParticipant]
    id: String
    replyTo: [EmailParticipant]
    subject: String
    to: [EmailParticipant]
    unread: Boolean
}

type EmailThread {
    _id: ID
    oem: ID
    id: String
    grantId: String
    earliestMessageDate: Int
    hasAttachments: Boolean
    latestMessageReceivedDate: Int
    latestMessageSentDate: Int
    messageIds: [String]
    messages: [EmailThreadMessage]
    folders: [String]
    participants: [EmailParticipant]
    snippet: String
    subject: String
    unread: Boolean
}

type EmailThreadWithTicket {
    thread: EmailThread
    ticket: Ticket
}

type PaginatedEmailThreads {    
    totalCount: Int
    currentPage: Int
    threads: [EmailThread]
    pageToken: String
}
`;
