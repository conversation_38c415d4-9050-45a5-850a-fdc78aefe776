import { Request, Response } from "express";
import createUuidFromSeed from "uuid-by-string";
import { IMachine } from "~/datamodels/Machine/interface";
import { createAsset } from "~/services/asset/create";
import { updateAsset } from "~/services/asset/update";
import { getAllAssets, getAsset } from "~/services/asset/fetch";
import { serviceFnInputGenerator } from "~/utils/inputGenerator";
import { controllerErrorHandler } from "~/utils/controllerErrorHandler";
import { PAGINATED_QUERY_PAGE_SIZE } from "~/constants/global";
import { buildQueryFilters, fetchFilterMappings } from "~/utils/fetchFilterMappings";
import { ERROR_TO_STATUS_CODE_MAP } from "~/errors/constants";
import { ERROR } from "~/environment";
import { IContext } from "~/types/common";

const qrAppUrl = process.env.QR_APP_URI;
const assetUrlModifier = process.env.MACHINE_URL_MODIFIER;

const getAssetUrl = async (asset: IMachine, dataSources: IContext["dataSources"]) => {
  let path = asset.uuid;

  if (!asset.uuid && asset._id) {
    const uuidValue = createUuidFromSeed(asset._id?.toString());
    await dataSources.Machine.save({
      _id: asset._id,
      uuid: uuidValue,
    });
    path = uuidValue;
  }

  return `${qrAppUrl}/${assetUrlModifier}/${path}`;
};

const project = async (assets: IMachine[], dataSources: IContext["dataSources"]) =>
  await Promise.all(
    assets.map(async (asset: IMachine) => ({
      name: asset.name,
      description: asset.description,
      serialNumber: asset.serialNumber,
      connectionId: asset.customer ?? null,
      // @ts-ignore
      parentSerialNumber: asset.parentSerialNumber ?? null,
      templateId: asset.template ?? null,
      assetType: asset.assetType ?? null,
      customFields: asset.customFields?.map(customField => ({
        fieldId: customField?.fieldId,
        value: customField?.value,
      })),
      url: await getAssetUrl(asset, dataSources),
      parts: asset.inventoryParts?.map(inventoryPart => ({
        articleNumber: inventoryPart,
      })),
      // @ts-ignore
      createdAt: asset.created_at,
      // @ts-ignore
      updatedAt: asset.updated_at,
    })),
  );

export const createAssetController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.facilityId = params.args.input.connectionId;
    const assetDetails = await createAsset(params);
    return res.status(200).json({
      message: params.translate("asset.created"),
      data: { serialNumber: assetDetails?.serialNumber },
    });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const getAllAssetsController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.params.limit = PAGINATED_QUERY_PAGE_SIZE;
    params.args.params.skip = (Number(params.args.query.page || 1) - 1) * PAGINATED_QUERY_PAGE_SIZE;
    params.args.query.facilityIdentifier = params.args.query.connectionId;
    const mappings = await fetchFilterMappings(params.args.query, params.dataSources, params.user);
    const filters = buildQueryFilters(params.args.query, mappings);
    params.args.params.where = filters;
    const assets = await getAllAssets(params);
    return res.status(200).json({
      data: await project(assets.assets, params.dataSources),
      totalPages: Math.ceil(assets.totalCount / PAGINATED_QUERY_PAGE_SIZE),
      page: Number(params.args.query.page || 1),
    });
  } catch (exception) {
    // @ts-ignore
    if (exception.code === ERROR_TO_STATUS_CODE_MAP[ERROR.DOCUMENT.NOT_FOUND]) {
      return res.status(200).json({
        data: [],
        totalPages: 0,
        page: 1,
      });
    }
    controllerErrorHandler(res, exception);
  }
};

export const getAssetController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.serialNumber = params.args.params.id;
    const asset = await getAsset(params);
    if (asset) {
      return res.status(200).json({
        data: (await project([asset], params.dataSources))[0],
      });
    }
    return res
      .status(404)
      .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const updateAssetController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.assetId = params.args.params.id;
    params.args.input.facilityId = params.args.input.connectionId;
    const assetDetails = await updateAsset(params);
    if (assetDetails) {
      return res.status(200).json({
        message: params.translate("asset.updated"),
        data: { serialNumber: assetDetails?.serialNumber },
      });
    }
    return res
      .status(404)
      .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};
