name: Test Templates
on:
  pull_request:
    branches:
      - develop
      - dev
    paths:
      - "templates/**"
jobs:
  test:
    defaults:
      run:
        working-directory: templates
    env:
      NODE_VERSION: 16.17.0

    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Execute Unit Tests
        run: |
          npm ci
          npm test
