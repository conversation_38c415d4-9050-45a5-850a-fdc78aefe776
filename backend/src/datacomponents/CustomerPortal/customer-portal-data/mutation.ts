import { GraphQLResolveInfo } from "graphql";
import { ExecutionContext } from "graphql/execution/execute";
import { createCustomerPortal as creationService } from "~/services/customerPortal/create";
import { updateCustomerPortal as modificationService } from "~/services/customerPortal/update";
import { deleteCustomerPortal as deletionService } from "~/services/customerPortal/delete";

import { features, roles } from "~/directives";
import { IContext } from "~/types/common";
import { UpdateContact } from "~/types/contact";

const { staff } = roles.is;
const { customerPortal } = features.oemOwns;

export const mutationTypes = `#graphql
  type Mutation {
    createCustomerPortal(connectionId: ID!): CustomerPortal @${staff} @${customerPortal}
    updateCustomerPortal(input: InputUpdateCustomerPortal!): CustomerPortal @${staff} @${customerPortal}
    deleteCustomerPortal(id: ID!): String @${staff} @${customerPortal}
  }
`;

export const createCustomerPortal = async (
  _: ExecutionContext["contextValue"],
  {
    connectionId,
  }: {
    connectionId: string;
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await creationService({
    // @ts-ignore
    args: { input: { connectionId }, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const updateCustomerPortal = async (
  _: ExecutionContext["contextValue"],
  {
    input,
  }: {
    input: UpdateContact["args"]["input"];
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await modificationService({
    args: { input, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const deleteCustomerPortal = async (
  _: ExecutionContext["contextValue"],
  {
    id,
  }: {
    id: string;
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await deletionService({
    args: { input: { id }, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });
