import types, { typeResolvers } from "~/datacomponents/Document/document-data/type";
import { mutationResolvers, mutationTypes } from "~/datacomponents/Document/document-data/mutation";
import { queryResolvers, queryTypes } from "~/datacomponents/Document/document-data/query";
import inputTypes from "~/datacomponents/Document/document-data/input";

export default {
  resolvers: {
    ...mutationResolvers,
    ...queryResolvers,
    ...typeResolvers,
  },
  types: `
      ${inputTypes}
      ${mutationTypes}
      ${queryTypes}
      ${types}
    `,
};
