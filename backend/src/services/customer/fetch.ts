import { ForbiddenError } from "apollo-server-errors";
import { CONTACT_ACCESS_STATUS } from "~/constants/contacts";
import { IContact } from "~/datamodels/Contact/interface";
import NotFoundError from "~/errors/NotFoundError";
import { ServiceParamsType } from "~/types/common";
import { buildQueryParams, throwIfError } from "~/utils";
import { prepareFilterQuery } from "~/utils/customers";

export const getAllCustomers = async ({ args, dataSources, user }: ServiceParamsType) => {
  const { skip, limit, sort } = args.params || {};
  const { filterQuery, query } = await prepareFilterQuery({
    args,
    user,
    dataSources,
  });
  const queryPayload = {
    ...(filterQuery?.length ? { $and: filterQuery } : {}),
    ...buildQueryParams(query),
    oem: user.organization,
    sort: sort?.length ? sort : ["name:asc"],
    delete: false,
  };

  const [customers, totalCount] = await Promise.all([
    dataSources.Customer.getMany(queryPayload),
    dataSources.Customer.totalCount({
      ...queryPayload,
      limit: -1,
      skip: 0,
    }),
  ]);

  return {
    customers,
    limit,
    skip,
    currentPage: skip && limit ? Math.floor(skip / limit + 1) : 1,
    totalCount,
  };
};

export const getCustomer = async ({ args: { input }, dataSources, user }: ServiceParamsType) => {
  const { id, facilityIdentifier } = input || {};
  if (!id && !facilityIdentifier) return null;
  const customer = await dataSources.Customer.getOne({
    ...(id ? { _id: id } : {}),
    ...(facilityIdentifier ? { facilityIdentifier } : {}),
    oem: user.organization,
  });

  if (!customer)
    throw new NotFoundError(`Facility with id ${id || facilityIdentifier} does not exist`);

  return customer;
};

export const listOemCustomersInArea = async ({ args, dataSources, user }: ServiceParamsType) => {
  const { where } = args?.params || {};
  const { coordinates, searchQuery } = where || {};

  const { filterQuery, query } = await prepareFilterQuery({
    args,
    user,
    dataSources,
  });

  if (!Array.isArray(coordinates) || coordinates.length !== 5) {
    return throwIfError("Missing valid set of coordinates");
  }

  // Ensure the coordinates form a closed polygon
  if (
    coordinates[0][0] !== coordinates[coordinates.length - 1][0] ||
    coordinates[0][1] !== coordinates[coordinates.length - 1][1]
  ) {
    return throwIfError("Coordinates must form a closed polygon");
  }

  const queryPayload = {
    ...(filterQuery?.length ? { $and: filterQuery } : {}),
    ...buildQueryParams(query),
  };

  delete queryPayload.where;
  delete queryPayload.skip;

  const facilities = await dataSources.Customer.getMany(
    {
      ...queryPayload,
      oem: user.organization,
      customerAddress: {
        $geoWithin: {
          $polygon: coordinates,
        },
      },
      ...(searchQuery ? { name: { $regex: searchQuery, $options: "i" } } : {}),
    },
    {
      select: { customerAddress: 1, _id: 1 },
    },
  );

  if (!facilities || facilities.length === 0) {
    return [];
  }

  return facilities;
};

export const getSharedOrganizationDetails = async ({
  args,
  dataSources,
  user,
}: ServiceParamsType) => {
  const correspondingContact: IContact = await dataSources.Contact.getOne({
    user: user.id,
    connection: args.input.connectionId,
    accessStatus: CONTACT_ACCESS_STATUS.ACTIVE,
  });

  if (!correspondingContact)
    throw new ForbiddenError("User does not have access to this organization's asset");

  const [connection, oem] = await Promise.all([
    dataSources.CustomerPortal.getOne({ connection: args.input.connectionId }),
    dataSources.Oem.loadOne(correspondingContact.oem),
  ]);

  const response = {
    assetAccess: connection.assetAccess,
    oem,
  };

  return response;
};

export const getConnectionHistory = async ({ args, dataSources, user }: ServiceParamsType) => {
  const { skip, limit, where } = args.params || {};
  const { customer } = where || {};

  if (!customer) return null;

  const machineIds = await dataSources.Machine.getMany(
    {
      customer,
      oem: user.organization,
      deleted: false,
    },
    {
      _id: 1,
    },
  );

  // @ts-ignore
  const allMachineIds = machineIds.map(machine => machine._id.toString());

  const queryPayload = {
    machine: { $in: allMachineIds },
    type: "Ticket",
    oem: user.organization,
    customer,
    deleted: false,
  };

  const [docCount, history] = await Promise.all([
    dataSources.MachineHistory.countDocuments(queryPayload),
    dataSources.MachineHistory.getMany({
      ...queryPayload,
      limit,
      skip,
      sort: ["createdAt:desc", "_id:asc"],
    }),
  ]);

  return {
    history,
    limit,
    skip,
    currentPage: skip && limit ? Math.floor(skip / limit + 1) : 1,
    machineCount: machineIds.length,
    totalCount: docCount,
  };
};
