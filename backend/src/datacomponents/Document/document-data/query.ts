import { GraphQLResolveInfo } from "graphql";
import type { ExecutionContext } from "graphql/execution/execute";
import { roles } from "~/directives";
import {
  getAllDocuments,
  hybridSearch,
  getDocumentById as _getDocumentById,
  getDocumentChunksById as _getDocumentChunksById,
  getDocumentAuthors,
} from "~/services/document/fetch";
import { IContext } from "~/types/common";

const { technician } = roles.is;

export const queryTypes = `#graphql
  type Query {
    getDocumentById(documentId: ID!): Document @${technician}
    getDocumentChunksByIds(documentChunkIds: [ID!]!): [DocumentChunk] @${technician}
    listDocuments(params: InputQueryParams): PaginatedDocuments @${technician}
    queryDocuments(query: String!, activeFilters: JSON): QueryResponse @${technician}
    getDocumentAuthors: <AUTHORS>
  }
`;

export const queryResolvers = {
  Query: {
    getDocumentById: async (
      _: ExecutionContext["contextValue"],
      args: { documentId: string },
      { dataSources, user, req: { t: translate } }: IContext,
      ___: GraphQLResolveInfo,
    ) =>
      await _getDocumentById({
        args: {
          input: { documentId: args.documentId },
          params: {},
          headers: {},
          files: null,
          query: {},
        },
        dataSources,
        user,
        translate,
      }),

    getDocumentChunksByIds: async (
      _: ExecutionContext["contextValue"],
      args: { documentChunkIds: string },
      { dataSources, user, req: { t: translate } }: IContext,
      ___: GraphQLResolveInfo,
    ) =>
      await _getDocumentChunksById({
        args: {
          input: { documentChunkIds: args.documentChunkIds },
          params: {},
          headers: {},
          files: null,
          query: {},
        },
        dataSources,
        user,
        translate,
      }),

    listDocuments: async (
      _: ExecutionContext["contextValue"],
      args: { params: { skip: number; limit: number } },
      { dataSources, user, req: { t: translate } }: IContext,
      ___: GraphQLResolveInfo,
    ) =>
      await getAllDocuments({
        args: { input: {}, params: args.params, headers: {}, files: null, query: {} },
        dataSources,
        user,
        translate,
      }),

    getDocumentAuthors: <AUTHORS>
      _: ExecutionContext["contextValue"],
      args: {},
      { dataSources, user, req: { t: translate } }: IContext,
      ___: GraphQLResolveInfo,
    ) =>
      await getDocumentAuthors({
        args: { input: {}, params: {}, headers: {}, files: null, query: {} },
        dataSources,
        user,
        translate,
      }),

    queryDocuments: async (
      _: ExecutionContext["contextValue"],
      args: {
        query: string;
        activeFilters: { customFields: { fieldId: string; value: string[] }[]; authors: string[] };
      },
      { dataSources, user, req: { t: translate } }: IContext,
      ___: GraphQLResolveInfo,
    ) =>
      await hybridSearch({
        args: {
          input: { query: args.query, activeFilters: args.activeFilters },
          params: {},
          headers: {},
          files: null,
          query: {},
        },
        dataSources,
        user,
        translate,
      }),
  },
};
