import { Types } from "mongoose";
import type { Attachment, Draft, Message } from "nylas";
import axios from "axios";
import path from "path";
import { promisify } from "util";
import { encrypt, logger, uploadAndGetEmailFileUrl, getOemUserChannelId } from "~/utils";
import type { IUser } from "~/datamodels/User/interface";
import { deleteS3Files } from "~/datasources/db/_utils/_s3";
import { S3 } from "~/config";
import { IContext } from "~/types/common";
import type { InputForwardedEmailAttachments, InputEmailInlineAttachments } from "~/graphql/types";
import type { AttachmentType } from "~/datasources/api/nylas/interface";
import type { ITicket } from "~/datamodels/Ticket/interface";
import { createEmailMessage } from "~/services/emailMessage/create";
import { createEmailThread } from "~/services/emailThread/create";

const BATCH_SIZE = 5;
const MAX_RETRIES = 30;
const BASE_DELAY = 500;
const sleep = promisify(setTimeout);

const getFileUrl = (message: Draft | Message, user: IUser, emailAddressId: string) => {
  const fileUrl: { [key: string]: string } = {};
  message?.attachments?.forEach(attachment => {
    if (attachment.isInline) {
      const fileKey = encrypt(
        `${user.organization}---${attachment.id}---${message.id}---${emailAddressId}`,
        process.env.EMAIL_FILE_PASSWORD,
      );
      fileUrl[attachment.contentId as string] = `${process.env.SERVER_URI}/email/file/${fileKey}`;
    }
  });
  return fileUrl;
};

const sendEmailFileUrl = async ({
  grantId,
  messageId,
  attachment,
  userId,
  dataSources,
  oemSlug,
  uuid,
}: {
  grantId: string;
  messageId: string;
  attachment: Attachment;
  userId: string;
  dataSources: any;
  oemSlug: string;
  uuid: string;
}) => {
  const channel = getOemUserChannelId(userId);
  const meta = { isEmail: true, uuid };

  try {
    const buffer: ReadableStream = await dataSources.NylasApi.downloadFile(
      grantId,
      messageId,
      attachment.id,
    );

    const url = await uploadAndGetEmailFileUrl({
      oemSlug,
      attachment,
      buffer,
    });

    const publishPayload = {
      message: {
        text: "downloadEmailFile",
        success: true,
        payload: { url },
      },
      meta,
      channel,
    };

    dataSources.PubnubApi.publishMessage(publishPayload);
  } catch (error) {
    const publishPayload = {
      message: {
        text: "downloadEmailFile",
        success: false,
        error,
      },
      meta,
      channel,
    };

    dataSources.PubnubApi.publishMessage(publishPayload);
    logger.error("sendEmailFileUrl", error);
  }
};

const removeThreadsFromTickets = async ({
  oem,
  dataSources,
}: {
  oem: Types.ObjectId;
  dataSources: any;
}) => {
  try {
    await dataSources.Ticket.updateManyByQuery(
      { oem: { $exists: true, $eq: oem } },
      { $set: { emailThreads: [] } },
    );
    logger.info(
      `removeThreadsFromTickets: All email thread references removed from the tickets of OEM ${oem}`,
    );
  } catch (error) {
    logger.error("removeThreadsFromTickets", error);
  }
};

const fetchFileData = async (url: string) => {
  const response = await axios.get(url, { responseType: "arraybuffer" });
  return {
    content: Buffer.from(response.data),
    contentType: response.headers["content-type"],
    filename: path.basename(url),
    size: response.data.length,
  };
};

const sendEmailWithAttachmentsAndNotify = async ({
  dataSources,
  ticket,
  user,
  to,
  cc,
  bcc,
  subject,
  body,
  fileUrls = [],
  replyToMessageId,
  uuid,
  forwardedAttachments,
  draftId,
  inlineAttachments,
  grantId,
  emailAddressId,
}: {
  dataSources: IContext["dataSources"];
  ticket?: ITicket;
  user: IUser;
  to: string[];
  cc: string[];
  bcc: string[];
  subject: string;
  body: string;
  fileUrls: string[];
  replyToMessageId: string;
  draftId: string;
  uuid: string;
  forwardedAttachments: InputForwardedEmailAttachments[];
  inlineAttachments: InputEmailInlineAttachments[];
  grantId: string;
  emailAddressId: string;
}) => {
  const text = "sendEmail";
  const meta = { isEmail: true, uuid };
  const channel = getOemUserChannelId(user.id);
  try {
    const attachmentPromises = fileUrls.map(async url => {
      const { content, contentType, filename, size } = await fetchFileData(url);

      return {
        contentDisposition: "attachment" as AttachmentType,
        contentType,
        filename,
        content: content.toString("base64") as string,
        id: path.basename(filename, path.extname(filename)),
        size,
      };
    });

    const forwardAttachmentPromises = (forwardedAttachments || []).map(async attachment => {
      const res = await dataSources.NylasApi.downloadAttachmentBase64({
        grantId: grantId || "",
        messageId: replyToMessageId || draftId,
        attachment,
      });
      return {
        contentDisposition: attachment.isInline ? "inlined" : "attachment",
        contentType: attachment.contentType,
        filename: attachment.filename,
        content: res as string,
        id: attachment.id,
        size: attachment.size,
        ...(attachment.isInline ? { contentId: attachment.contentId } : {}),
      };
    });

    const finalAttachments = await Promise.all([
      ...attachmentPromises,
      ...forwardAttachmentPromises,
    ]);

    const message = await dataSources.NylasApi.sendEmail({
      grantId: grantId || "",
      to,
      cc,
      bcc,
      subject,
      body,
      replyToMessageId,
      // @ts-ignore
      attachments: [...finalAttachments, ...(inlineAttachments || [])],
    });

    // Delete from the bucket immediately. From CDN, it will be removed once cache is cleared
    // These temp file urls are not available to anyone. So unauthoirzed access is not possible
    const filesToDelete = fileUrls.map(url =>
      url.replace(S3.CDN as string, `${S3.BUCKET as string}.s3.amazonaws.com`),
    );

    if (filesToDelete.length) deleteS3Files(fileUrls);

    if (ticket) {
      const alreadyExists = ticket.linkedEmailThreads?.find(
        emailThread => emailThread.threadId === message?.threadId,
      );
      if (!alreadyExists)
        await dataSources.Ticket.save({
          _id: ticket._id,
          $push: {
            linkedEmailThreads: { accountId: emailAddressId, threadId: message?.threadId },
          },
        });
    }

    const thread = await dataSources.EmailThread.getOne({ id: message!.threadId });
    const nylasThread = await dataSources.NylasApi.getThread(grantId, message?.threadId!);

    await Promise.all([
      createEmailMessage({
        // @ts-ignore
        args: {
          input: {
            // @ts-ignore
            message: { ...message, attachments: message!.attachments || [], unread: false },
          },
        },
        dataSources,
        user,
      }),
      thread
        ? dataSources.EmailThread.updateOne(
            {
              _id: thread._id,
            },
            {
              $set: nylasThread,
            },
          )
        : createEmailThread({
            // @ts-ignore
            args: {
              input: {
                thread: nylasThread,
                grantId,
              },
            },
            dataSources,
            user,
          }),
      draftId ? dataSources.NylasApi.destroyDraft(grantId, draftId) : Promise.resolve(null),
    ]);

    const publishPayload = {
      message: {
        text,
        success: true,
        payload: {
          threadId: message?.threadId,
          accountId: emailAddressId,
          replied: !!replyToMessageId,
          message: {
            bcc: [],
            cc: [],
            date: message?.date,
            from: message?.from,
            id: message?.id,
            replyTo: message?.replyTo,
            subject: message?.subject,
            to: message?.to?.length ? [message?.to[0]] : [],
            unread: message?.unread,
          },
        },
      },
      meta,
      channel,
    };

    dataSources.PubnubApi.publishMessage(publishPayload);
  } catch (error) {
    const publishPayload = {
      message: {
        text,
        success: false,
        error,
      },
      meta,
      channel,
    };

    dataSources.PubnubApi.publishMessage(publishPayload);
    logger.error(text, error);
  }
};

const processMessageBatch = async (
  batch: any[],
  grantId: string,
  dataSources: IContext["dataSources"],
  processedThreads: Set<string>,
) => {
  for (const messageId of batch) {
    let attempt = 0;
    let success = false;

    while (attempt < MAX_RETRIES && !success) {
      try {
        const nylasEmailMessage = await dataSources.NylasApi.getMessage(grantId, messageId);
        if (!nylasEmailMessage) return;
        const { threadId } = nylasEmailMessage;
        const nylasThread = threadId
          ? await dataSources.NylasApi.getThread(grantId, threadId)
          : null;

        const [emailMessage, emailThread] = await Promise.all([
          dataSources.EmailMessage.getOne({ id: messageId }),
          threadId ? dataSources.EmailThread.getOne({ id: threadId }) : null,
        ]);

        if (!emailMessage) {
          await dataSources.EmailMessage.save({
            ...nylasEmailMessage,
            attachments: nylasEmailMessage.attachments || [],
          });
        } else {
          await dataSources.EmailMessage.updateOne(
            { id: messageId },
            {
              $set: {
                ...nylasEmailMessage,
                attachments: nylasEmailMessage.attachments || [],
              },
            },
          );
        }

        if (threadId && nylasThread) {
          if (!emailThread) {
            await createEmailThread({
              // @ts-ignore
              args: { input: { thread: nylasThread, grantId } },
              dataSources,
            });
          } else if (!processedThreads.has(threadId)) {
            processedThreads.add(threadId);
            await dataSources.EmailThread.updateOne(
              { _id: emailThread._id },
              { $set: nylasThread },
            );
          }
        }

        success = true;
      } catch (error) {
        attempt += 1;
        const delay = BASE_DELAY * 2 ** attempt;
        logger.error(`Error processing message ${messageId} (Attempt ${attempt}): ${error}`);
        if (attempt < MAX_RETRIES) await sleep(delay);
      }
    }
  }
};

const processMessagesInBatches = async (
  messageIds: string[],
  grantId: string,
  dataSources: any,
) => {
  const processedThreads = new Set<string>();
  const batches = [];

  for (let i = 0; i < messageIds.length; i += BATCH_SIZE) {
    batches.push(messageIds.slice(i, i + BATCH_SIZE));
  }

  for (const batch of batches) {
    await Promise.all(
      batch.map(messageId =>
        processMessageBatch([messageId], grantId, dataSources, processedThreads),
      ),
    );
  }
};

export {
  getFileUrl,
  sendEmailFileUrl,
  removeThreadsFromTickets,
  sendEmailWithAttachmentsAndNotify,
  processMessagesInBatches,
};
