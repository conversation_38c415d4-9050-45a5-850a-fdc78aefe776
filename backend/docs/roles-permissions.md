[<< Back](../../README.md)

### roles-permissions.json

All application roles are set here

**HIERARCHY BUILDING BLOCKS**

- Top-down: higher users in the tree inherit roles from lower levels as long as they are in the same sub-tree

`{}` creates a tree leaf
`[]` creates a tree leaf of sibilings
`[[]]` creates a sub-tree

Example:

- `{}` parent (root)
- `{}` child (a) of [root]
- `[` grand-children of [root] and children of [a] with same rank, i.e sibilings
  `{}`, child (a1) of [a]
  `{}` child (a2) of [a]
  `]`,
- `[[` grand-children of [root] children of [a] in nested (isolated) level, i.e. sub-tree
  `{}` child (a3) of [a]
  `{}` child (a4) of [a]
  `]]`
- `{}` child (b) of [root]

Real-world example:

```
{

  "USERS": [
    {
      "ADMIN": {}
    },
    [[
        [
          {
            "HR": {}
          },
          {
            "STAFF": {}
          }
        ]
    ]],
    {
      "USER": {}
    }
  ]
}
```

###### Ouput of the above roles

```
####################################################
ROLES TREE
####################################################
OWNER
└── ADMIN
    ├── HR|STAFF
    └── USER

####################################################
ROLES' COMPUTED PERMISSIONS
####################################################

  ROLE: ADMIN
  INHERITED_ROLES: [HR, STAFF, USER]
  ALL PERMISSIONS: [
    update_userProfile
    update_badge
    delete_userProfile
    delete_badge
    create_badge
    create_userProfile
    create_employeeProfile
    read_userProfile
    read_employeeProfile
    update_employeeProfile
    read_badge
  ]

-----------------------------------------------------------------

  ROLE: HR
  INHERITED_ROLES: []
  ALL PERMISSIONS: [
    create_userProfile
    create_employeeProfile
    read_userProfile
    read_employeeProfile
    update_userProfile
    update_employeeProfile
  ]

-----------------------------------------------------------------

  ROLE: STAFF
  INHERITED_ROLES: []
  ALL PERMISSIONS: [
    read_employeeProfile
    read_badge
  ]

-----------------------------------------------------------------

  ROLE: USER
  INHERITED_ROLES: []
  ALL PERMISSIONS: [
    read_userProfile
    read_badge
    update_userProfile
  ]

-----------------------------------------------------------------

```

###### How to enforce roles in queries and mutations using custom directives

Once the `roles-permissions.json` is saved and the backend server started all roles and permissions custom directive get build programmatically.

You will able to enforce them following this pattern:
Roles: roles.is.{role}

> ###### At field level

```
import { permissions } from '../../../directives';

export const types = `
  type User {
    id: String!
    name: String
    username: String
    email: String @${permissions.can.read.user_profile}
  }`;
```

> ##### at query/mutation level

```
    testPermissionsHasRole: String @${roles.is.admin}
    testPermissionsIsAllowed: String @${permissions.can.read.badge}
```
