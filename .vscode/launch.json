{"version": "0.2.0", "configurations": [{"name": "Debug Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/backend/node_modules/.bin/babel-node", "args": ["${workspaceFolder}/backend/src/server.js", "--extensions", ".js,.ts"], "cwd": "${workspaceFolder}/backend", "env": {"NODE_ENV": "development"}, "envFile": "${workspaceFolder}/backend/env/.env", "console": "integratedTerminal", "restart": true, "runtimeExecutable": "${workspaceFolder}/backend/node_modules/.bin/lookenv", "runtimeArgs": ["--path=./env/lookenv.dev.js", "--dotenv=./env/.env", "--"], "skipFiles": ["<node_internals>/**"], "outputCapture": "std"}, {"name": "Debug Worker", "type": "node", "request": "launch", "program": "${workspaceFolder}/backend/node_modules/.bin/babel-node", "args": ["${workspaceFolder}/backend/src/worker.js", "--extensions", ".js,.ts"], "cwd": "${workspaceFolder}/backend", "env": {"NODE_ENV": "development"}, "envFile": "${workspaceFolder}/backend/env/.env", "console": "integratedTerminal", "restart": true, "runtimeExecutable": "${workspaceFolder}/backend/node_modules/.bin/lookenv", "runtimeArgs": ["--path=./env/lookenv.dev.js", "--dotenv=./env/.env", "--"], "skipFiles": ["<node_internals>/**"], "outputCapture": "std"}, {"name": "Debug Server (Inspect)", "type": "node", "request": "launch", "program": "${workspaceFolder}/backend/node_modules/.bin/babel-node", "args": ["--inspect", "--expose-gc", "--max-old-space-size=1024", "${workspaceFolder}/backend/src/server.js", "--extensions", ".js,.ts"], "cwd": "${workspaceFolder}/backend", "env": {"NODE_ENV": "development"}, "envFile": "${workspaceFolder}/backend/env/.env", "console": "integratedTerminal", "restart": true, "runtimeExecutable": "${workspaceFolder}/backend/node_modules/.bin/lookenv", "runtimeArgs": ["--path=./env/lookenv.dev.js", "--dotenv=./env/.env", "--"], "skipFiles": ["<node_internals>/**"], "outputCapture": "std"}]}