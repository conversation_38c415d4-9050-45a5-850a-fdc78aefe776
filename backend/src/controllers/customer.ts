import { Request, Response } from "express";
import { getAllCustomers, getCustomer } from "~/services/customer/fetch";
import { createCustomer } from "~/services/customer/create";
import { updateCustomer } from "~/services/customer/update";
import { serviceFnInputGenerator } from "~/utils/inputGenerator";
import { controllerErrorHandler } from "~/utils/controllerErrorHandler";
import { PAGINATED_QUERY_PAGE_SIZE } from "~/constants/global";
import { ICustomer } from "~/datamodels/Customer/interface";

const project = (customers: ICustomer[]) =>
  customers.map((customer: ICustomer) => ({
    name: customer.name,
    description: customer.description,
    connectionId: customer.facilityIdentifier,
    customFields: customer.customFields?.map(customField => ({
      fieldId: customField?.fieldId,
      value: customField?.value,
    })),
    // @ts-ignore
    createdAt: customer.created_at,
    // @ts-ignore
    updatedAt: customer.updated_at,
  }));

export const createCustomerController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.facilityIdentifier = params.args.input.connectionId;
    const customerDetails = await createCustomer(params);
    return res.status(200).json({
      message: "Connection created successfully!",
      data: { connectionId: customerDetails.facilityIdentifier },
    });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const getAllCustomersController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.params.limit = PAGINATED_QUERY_PAGE_SIZE;
    params.args.params.skip = (Number(params.args.query.page || 1) - 1) * PAGINATED_QUERY_PAGE_SIZE;
    const customers = await getAllCustomers(params);
    return res.status(200).json({
      data: project(customers.customers),
      totalPages: Math.ceil(customers.totalCount / PAGINATED_QUERY_PAGE_SIZE),
      page: Number(params.args.query.page || 1),
    });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const getCustomerController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.facilityIdentifier = params.args.params.id;
    const customer = await getCustomer(params);
    if (customer) {
      return res.status(200).json({
        data: project([customer])[0],
      });
    }
    return res
      .status(404)
      .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const updateCustomerController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.oldFacilityId = params.args.params.id;
    params.args.input.facilityId = params.args.input.connectionId;
    const customerDetails = await updateCustomer(params);
    if (customerDetails)
      return res.status(200).json({
        message: "Connection updated successfully!",
        data: { connectionId: customerDetails.facilityIdentifier },
      });
    return res
      .status(404)
      .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};
