import moment from "moment";
import type { Contact, Thread } from "nylas";
import { roles, features } from "~/directives";
import { encrypt, getEmailConfigFromOem, throwIfError } from "~/utils";
import type { IOem } from "~/datamodels/Oem/interface";
import type { IContext } from "~/types/common";
import { getFileUrl, sendEmailFileUrl } from "~/datacomponents/Email/email-data/_helper";
import { config } from "~/config/_nylas";
import { getEmailDraft, getEmailDraftAndJunkCount, listEmailDrafts } from "~/services/email/fetch";
import type { ITicket } from "~/datamodels/Ticket/interface";

const { technician, oem: oemRole, staff } = roles.is;
const { emails } = features.oemOwns;

export const queryTypes = `#graphql
  type Query {
    getEmailAuthUrl: String! @${oemRole}
    getEmailFileUploadUrl: String @${technician}
    listEmailFoldersNylas(pageToken: String, emailAddressId: ID!): PaginatedEmailFolders @${emails} @${staff}
    listEmailThreadsByFolderNylas(pageToken: String, folder: String!, search: String!, emailAddressId: ID!): PaginatedEmailThreads @${emails} @${staff}
    listEmailThreadsByTicketNylas(ticketId: ID!, emailAddressId: ID!): [JSON] @${emails} @${technician}
    getEmailThreadNylas(threadId: String!, emailAddressId: ID!): EmailThreadWithTicket @${emails} @${technician}
    getEmailMessageNylas(messageId: String!, emailAddressId: ID!): EmailMessageWithFileURL @${emails} @${technician}
    downloadEmailFile(fileId: String!, uuid: String!, messageId: String!, emailAddressId: ID!): String @${emails} @${technician}
    listEmailContacts(pageToken: String, search: String, emailAddressId: ID!): PaginatedEmailContacts @${emails} @${technician}
    listDrafts(pageToken: String, search: String, emailAddressId: ID!): PaginatedEmailDrafts @${emails} @${technician}
    getDraft(emailAddressId: ID!, draftId: String!): EmailDraft @${emails} @${technician}
    getDraftAndJunkEmailCount(emailAddressId: ID!): DraftJunkCount @${emails} @${technician}
  }
`;

export const queryResolvers = {
  Query: {
    getEmailAuthUrl: async (_: any, __: any, { dataSources }: IContext) =>
      dataSources.NylasApi.generateAuthUrl(config.emailRedirectUri),
    getEmailFileUploadUrl: async (_: any, __: any, { user: contextUser }: IContext) => {
      if (!contextUser.organization) return null;
      const data = {
        oemId: contextUser.organization,
        ttl: moment().utc().add(5, "minutes").format(),
      };
      return `${process.env.SERVER_URI}/email/file/${encrypt(
        JSON.stringify(data),
        process.env.EMAIL_FILE_PASSWORD,
      )}`;
    },
    listEmailFoldersNylas: async (
      _: any,
      args: { pageToken: string; emailAddressId: string },
      { dataSources, user }: IContext,
    ) => {
      const { pageToken: incomingPageToken } = args || {};
      const oem = await dataSources.Oem.getById(user.organization);
      const { grantId } = getEmailConfigFromOem({ emailAddressId: args.emailAddressId, oem });
      const { pageToken, folders } = await dataSources.NylasApi.getFolders(
        grantId!,
        incomingPageToken,
      );
      return {
        pageToken,
        folders,
      };
    },
    listEmailThreadsByFolderNylas: async (
      _: any,
      args: { pageToken: string; folder: string; search: string; emailAddressId: string },
      { dataSources, user }: IContext,
    ) => {
      const { pageToken: incomingPageToken, folder, search } = args || {};
      const oem = await dataSources.Oem.getById(user.organization);
      const { grantId } = getEmailConfigFromOem({ emailAddressId: args.emailAddressId, oem });
      const { pageToken, threads } = await dataSources.NylasApi.getThreads(
        grantId!,
        folder,
        search,
        incomingPageToken,
      );
      return {
        pageToken,
        threads,
      };
    },
    listEmailThreadsByTicketNylas: async (
      _: any,
      args: { ticketId: string; emailAddressId: string },
      { dataSources, user }: IContext,
    ) => {
      const { ticketId } = args || {};
      const oem: IOem = await dataSources.Oem.getById(user.organization);
      const ticket: ITicket = await dataSources.Ticket.getById(ticketId);
      const { grantId, _id } = getEmailConfigFromOem({ emailAddressId: args.emailAddressId, oem });
      const emailThreadsToFetch = ticket?.linkedEmailThreads?.filter(
        emailThread => emailThread.accountId?.toString() === _id?.toString(),
      );
      const promises = emailThreadsToFetch?.length
        ? emailThreadsToFetch?.map(
            emailThreadToFetch =>
              new Promise((resolve, reject) => {
                dataSources.NylasApi.getThread(grantId as string, emailThreadToFetch.threadId)
                  .then((thread: Thread) => resolve(thread))
                  .catch((e: Error) => reject(e));
              }),
          )
        : [];

      const result: Thread[] = (await Promise.all(promises)) as Thread[];
      return result.sort((a, b) => b.latestMessageReceivedDate! - a.latestMessageReceivedDate!);
    },
    getEmailThreadNylas: async (
      _: any,
      args: { threadId: string; emailAddressId: string },
      { dataSources, user }: IContext,
    ) => {
      const { threadId } = args || {};
      const oem: IOem = await dataSources.Oem.getById(user.organization);
      const { grantId, _id } = getEmailConfigFromOem({ emailAddressId: args.emailAddressId, oem });
      const ticket = await dataSources.Ticket.getOne({
        linkedEmailThreads: {
          $elemMatch: { threadId, accountId: _id },
        },
        oem: user.organization,
      });

      const thread = await dataSources.NylasApi.getThread(grantId as string, threadId);
      return {
        thread,
        ticket,
      };
    },
    getEmailMessageNylas: async (
      _: any,
      args: { messageId: string; emailAddressId: string },
      { dataSources, user }: IContext,
    ) => {
      const { messageId } = args || {};
      const oem: IOem = await dataSources.Oem.getById(user.organization);
      const { grantId, emailAddress } = getEmailConfigFromOem({
        emailAddressId: args.emailAddressId,
        oem,
      });
      const message = await dataSources.NylasApi.getMessage(grantId!, messageId);
      return {
        message,
        fileUrl: getFileUrl(message, user, emailAddress!),
      };
    },
    downloadEmailFile: async (
      _: any,
      args: { fileId: string; messageId: string; uuid: string; emailAddressId: string },
      { dataSources, user }: IContext,
    ) => {
      const { messageId, fileId, uuid } = args || {};

      if (!messageId) throwIfError("messageId is required");
      if (!fileId) throwIfError("fileId is required");

      const oem: IOem = await dataSources.Oem.getById(user.organization);
      const { grantId } = getEmailConfigFromOem({ emailAddressId: args.emailAddressId, oem });
      if (!grantId) throwIfError("grantId not available for the OEM");

      const attachment = await dataSources.NylasApi.getFile(grantId!, messageId, fileId);

      sendEmailFileUrl({
        grantId: grantId!,
        messageId,
        attachment,
        userId: user.id as string,
        dataSources,
        oemSlug: oem.urlOem,
        uuid,
      });
      return "downloading file...";
    },
    listEmailContacts: async (
      _: any,
      args: { pageToken: string; search: string; emailAddressId: string },
      { dataSources, user }: IContext,
    ) => {
      const { pageToken: incomingPageToken, search = "" } = args || {};
      const oem: IOem = await dataSources.Oem.getById(user.organization);
      const { grantId } = getEmailConfigFromOem({ emailAddressId: args.emailAddressId, oem });
      const { contacts, pageToken } = (await dataSources.NylasApi.getContacts(
        grantId!,
        incomingPageToken,
        search,
      )) as { contacts: Contact[]; pageToken: string; search: string };
      return {
        pageToken,
        contacts,
      };
    },
    listDrafts: async (
      _: any,
      {
        emailAddressId,
        pageToken: incomingPageToken,
        search,
      }: { emailAddressId: string; pageToken: string; search: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await listEmailDrafts({
        // @ts-ignore
        args: { input: { emailAddressId, incomingPageToken, search } },
        dataSources,
        user,
        translate,
      }),
    getDraft: async (
      _: any,
      { emailAddressId, draftId }: { emailAddressId: string; draftId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await getEmailDraft({
        // @ts-ignore
        args: { input: { emailAddressId, draftId } },
        dataSources,
        user,
        translate,
      }),
    getDraftAndJunkEmailCount: async (
      _: any,
      { emailAddressId }: { emailAddressId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await getEmailDraftAndJunkCount({
        // @ts-ignore
        args: { input: { emailAddressId } },
        dataSources,
        user,
        translate,
      }),
  },
};
