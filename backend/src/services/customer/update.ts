import to from "await-to-js";
import { ERROR, FORBIDDEN } from "~/environment";
import { EnumRoles } from "~/graphql/types";
import { ICustomFieldObject } from "~/datamodels/CustomAdditionalField/interface";
import { ServiceParamsType } from "~/types/common";
import {
  throwIfError,
  validateCustomFields,
  belongsToUserTeams,
  createGeneralMachineUser,
} from "~/utils";
import QR_CODE_ACCESS from "$/settings/enums/customer/qrCodeAccess.json";
import InvalidInputError from "~/errors/InvalidInputError";
import ConflictError from "~/errors/ConflictError";
import { MONGODB_ERRORS } from "~/errors/constants";
import { ICustomer } from "~/datamodels/Customer/interface";

export const updateCustomer = async ({
  args,
  dataSources,
  user,
  isRestAPI,
  translate,
}: ServiceParamsType) => {
  const { input } = args || {};
  if (!input) return null;
  const { _id, facilityId, oldFacilityId, ...doc } = input;

  if (doc?.customFields?.length && !user.roles.includes(EnumRoles.Staff)) throw Error(FORBIDDEN);
  const facility = await dataSources.Customer.getOne({
    ...(_id ? { _id } : { facilityIdentifier: oldFacilityId }),
    oem: user.organization,
    ...belongsToUserTeams(user),
  });

  if (!facility)
    throw new InvalidInputError(
      isRestAPI ? translate("error_messages.invalid_id_utf_encode") : "Invalid id.",
    );

  if (input.facilityId) {
    doc.facilityIdentifier = input.facilityId || input.facilityIdentifier;
    delete doc.facilityId;
  }

  if (doc.qrCodeAccess !== undefined && doc.qrCodeAccess !== QR_CODE_ACCESS.SELECTIVE) {
    const isQRCodeEnabled = doc.qrCodeAccess === QR_CODE_ACCESS.ENABLE_ALL;

    await dataSources.Machine.updateManyByQuery(
      {
        $or: [{ customer: _id }],
      },
      {
        $set: { isQRCodeEnabled },
      },
    );

    await createGeneralMachineUser(dataSources, _id, user);
  }

  if (doc?.customFields?.length) {
    const existingCustomFields = [
      ...JSON.parse(JSON.stringify(facility.customFields)),
      ...doc.customFields,
    ];

    const fields = existingCustomFields.reduce((arr, item) => {
      const index = arr.findIndex((ar: ICustomFieldObject) => ar.fieldId === item.fieldId);
      if (index !== -1) {
        arr[index].value = item.value;
      } else {
        arr.push(item);
      }
      return arr;
    }, []);

    const { isValid, validatedCustomFields, errors } = await validateCustomFields({
      dataSources,
      oem: user.organization!.toString(),
      type: "facilities",
      customFields: fields,
    });

    if (!isValid) throw new InvalidInputError(errors?.join(", ") as string);

    doc.customFields = validatedCustomFields;
  }

  const [errNewCustomer, newCustomer] = await to<ICustomer>(
    dataSources.Customer.save({
      _id: facility._id,
      ...doc,
      ...(isRestAPI ? { updatedFromAPI: true } : {}),
    }),
  );

  await dataSources.CustomerPortal.updateOne(
    {
      connection: newCustomer!._id,
    },
    {
      $set: {
        name: `${newCustomer!.name} Portal`,
      },
    },
  );

  if ((errNewCustomer as any)?.code === MONGODB_ERRORS.DUPLICATE_KEY_ERROR_CODE) {
    throw new ConflictError(
      `Facility ${translate("error_messages.already_exists_id").replace("{id}", facilityId)}`,
    );
  } else {
    throwIfError(errNewCustomer, ERROR.USER.BAD_USER_INPUT);
  }

  return newCustomer;
};
