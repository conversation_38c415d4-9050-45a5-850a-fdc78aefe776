import { agenda } from "~/config";

const createAndScheduleUniqueJob = async <T extends Record<string, any>>({
  jobName,
  uniqueCriteria,
  runAt = new Date(),
  ...data
}: T & {
  jobName: string;
  uniqueCriteria: Record<string, any>;
  runAt?: Date;
}) => {
  const job = agenda.create(jobName, data);
  job.unique(uniqueCriteria).schedule(runAt);
  await job.save();
  return job;
};

export default createAndScheduleUniqueJob;
