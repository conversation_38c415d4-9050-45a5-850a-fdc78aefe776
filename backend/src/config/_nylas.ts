import Nylas from "nylas";

export const config = {
  clientId: process.env.NYLAS_CLIENT_ID as string,
  apiKey: process.env.NYLAS_API_KEY as string,
  apiUri: process.env.NYLAS_API_URI as string,
  emailRedirectUri: `${process.env.OEM_APP_URI}/app/settings/company/email-configuration/success`,
  calendarSyncRedirectUri: `${process.env.OEM_APP_URI}/app/settings/personal/calendar-sync/success`,
};

export const nylas = new Nylas({
  apiKey: config.apiKey,
  apiUri: config.apiUri,
});
