import { IContext } from "~/types/common";
import QRUsers from "$/settings/enums/user/_qr-users.json";
import { ANALYTICS_AXIS_OPTIONS } from "~/config";
import {
  belongsToUserTeams,
  entityTypes,
  generateFilters,
  generateTableValues,
  gernerateAxisValues,
  getEnums,
} from "~/utils";
import { checkForTeamsEnable } from "~/utils/analytics";
import { OEM_ROLES } from "~/utils/user";
import { Report } from "~/graphql/types";
import { ICustomAdditionalField } from "~/datamodels/CustomAdditionalField/interface";

const QRUserNames = getEnums(QRUsers, "values");

export const types = `
  type Report {
    _id: ID
    title: String
    xAxis : ReportField
    filters: [ReportFilter]
    entity: String
    segment : ReportField
    chartType: String
    oem: Oem
    table: [ReportField]
    inputs: ReportOptions
    data: MJSON
  }

  type PaginatedReports {
    currentPage: Int
    limit: Int
    reports: [Report]
    skip: Int
    totalCount: Int
  }
  
  type ReportFilter {
    filterType: String
    field: String
    condition: String
    value: [ReportField]
  }

  type ReportOptionFilter {
    filterType: String
    field: String
    condition: [String]
    value: [ReportField]
  }

  type ReportOptions {
    xAxis: [ReportField]
    segment: [ReportField]
    filter: [ReportOptionFilter]
    table: [ReportField]
  }

  type Analytics {
    data: MJSON
    inputTableOptions: [ReportField]
  }

  type ReportField {
    value: Mixed
    label: String
  }
`;

export const typeResolvers = {
  Report: {
    xAxis: async ({ xAxis, entity }: Report, _: any, { dataSources, user }: IContext) => {
      if (
        xAxis &&
        // @ts-ignore
        (xAxis.value === ANALYTICS_AXIS_OPTIONS.Team || !ANALYTICS_AXIS_OPTIONS[xAxis.value])
      ) {
        if (xAxis.value === ANALYTICS_AXIS_OPTIONS.Team) {
          const [teams, userOem] = await Promise.all([
            dataSources.Team.getMany({
              oem: user.organization,
            }),
            dataSources.Oem.getOne({
              _id: user.organization,
            }),
          ]);

          return (await checkForTeamsEnable(userOem, dataSources)) && (teams || []).length > 0
            ? xAxis
            : { value: "none", label: "None" };
        }

        const customFields: Array<ICustomAdditionalField> =
          await dataSources.CustomAdditionalField.getMany({
            oem: user.organization,
            type: entityTypes[entity!],
            fieldType: "singleSelect",
            enabled: true,
            deleted: false,
            sort: ["order:asc", "created_at:asc"],
          });

        const found = customFields.find(customField => customField._id.toString() === xAxis.value);

        return found ? xAxis : { value: "none", label: "None" };
      }

      return xAxis;
    },
    segment: async ({ segment, entity }: Report, _: any, { dataSources, user }: IContext) => {
      if (
        segment &&
        // @ts-ignore
        (segment.value === ANALYTICS_AXIS_OPTIONS.Team || !ANALYTICS_AXIS_OPTIONS[segment.value])
      ) {
        if (segment.value === ANALYTICS_AXIS_OPTIONS.Team) {
          const [teams, userOem] = await Promise.all([
            dataSources.Team.getMany({
              oem: user.organization,
            }),
            dataSources.Oem.getOne({
              _id: user.organization,
            }),
          ]);

          return (await checkForTeamsEnable(userOem, dataSources)) && (teams || []).length > 0
            ? segment
            : { value: "none", label: "None" };
        }

        const customFields: Array<ICustomAdditionalField> =
          await dataSources.CustomAdditionalField.getMany({
            oem: user.organization,
            type: entityTypes[entity!],
            fieldType: "singleSelect",
            enabled: true,
            deleted: false,
            sort: ["order:asc", "created_at:asc"],
          });

        const found = customFields.find(
          customField => customField._id.toString() === segment.value,
        );

        return found ? segment : { value: "none", label: "None" };
      }

      return segment;
    },
    filters: async ({ entity, filters }: Report, _: any, { dataSources, user }: IContext) => {
      let filtersToReturn = filters;

      const fieldIndex = filters?.findIndex(
        (filter: any) => filter.field === ANALYTICS_AXIS_OPTIONS.Team,
      );

      if (fieldIndex !== -1) {
        const queryPayload = {
          oem: user.organization,
          sort: ["name:asc"],
        };
        const [teams, userOem] = await Promise.all([
          dataSources.Team.getMany({
            ...queryPayload,
          }),
          dataSources.Oem.getOne({
            _id: user.organization,
          }),
        ]);

        const isTeamEnabled = await checkForTeamsEnable(userOem, dataSources);

        if ((isTeamEnabled && (teams || []).length === 0) || !isTeamEnabled)
          filtersToReturn = filters?.filter(
            (filter: any) => filter.field !== ANALYTICS_AXIS_OPTIONS.Team,
          );
      }

      const doesCustomFieldExist = filtersToReturn?.find(
        (filter: any) => filter.filterType === null,
      );
      if (doesCustomFieldExist) {
        const customFields: Array<ICustomAdditionalField> =
          await dataSources.CustomAdditionalField.getMany({
            oem: user.organization,
            type: entityTypes[entity!],
            fieldType: "singleSelect",
            enabled: true,
            deleted: false,
            sort: ["order:asc", "created_at:asc"],
          });

        const customFieldsMap = customFields.reduce((acc, item) => {
          const label = item.label!;
          const value = item;
          // @ts-ignore
          acc[label] = value;
          return acc;
        }, {});

        filtersToReturn = filtersToReturn?.filter(
          // @ts-ignore
          (filter: any) => filter.filterType !== null || customFieldsMap[filter.field],
        );
      }

      return filtersToReturn;
    },
    inputs: async ({ _id, entity }: Report, _: any, { dataSources, user }: IContext) => {
      let xAxis = [];
      let table = [];
      let filter: any = [];
      const queryPayload = {
        oem: user.organization,
        sort: ["name:asc"],
      };

      const [userOem, report, customers, teams, users, facilityUser] = await Promise.all([
        dataSources.Oem.getOne({ _id: user.organization }),
        dataSources.Report.getOne({
          _id,
          oem: user.organization,
        }),
        dataSources.Customer.getMany(queryPayload, { _id: 1, name: 1 }),
        dataSources.Team.getMany(
          { ...queryPayload, ...belongsToUserTeams(user, "_id") },
          { _id: 1, name: 1 },
        ),
        dataSources.User.getMany(
          {
            oem: user.organization,
            role: { $in: [...OEM_ROLES] },
          },
          { _id: 1, name: 1 },
        ),
        dataSources.User.getManyByQuery(
          {
            facility: { $exists: true },
            oem: user.organization,
            name: { $nin: QRUserNames },
          },
          { _id: 1, name: 1 },
        ),
      ]);

      const customFields = await dataSources.CustomAdditionalField.getMany(
        {
          oem: user.organization,
          type: entityTypes[report.entity],
          fieldType: "singleSelect",
          enabled: true,
          deleted: false,
          sort: ["order:asc", "created_at:asc"],
        },
        { _id: 1, label: 1, options: 1 },
      );

      [xAxis, table, filter] = await Promise.all([
        gernerateAxisValues({ userOem, customFields, entity, dataSources }),
        generateTableValues({ userOem, customFields, entity, dataSources }),
        generateFilters({
          userOem,
          customFields,
          entity,
          data: { customers, teams, users, facilityUser },
          dataSources,
        }),
      ]);

      return { xAxis, table, segment: xAxis, filter };
    },
  },
};
