import { GraphQLResolveInfo } from "graphql";
import { ExecutionContext } from "graphql/execution/execute";
import { createConnectionRequest as creationService } from "~/services/connectionRequest/create";
import {
  acceptConnectionRequest as acceptService,
  declineConnectionRequest as declineService,
} from "~/services/connectionRequest/update";

import { roles } from "~/directives";
import { IContext } from "~/types/common";

const { technician, oem } = roles.is;

export const mutationTypes = `#graphql
  type Mutation {
    createConnectionRequest(oemId: ID!): String @${technician}
    acceptConnectionRequest(requestId: ID!, portalToAttach: ID!): String @${oem}
    declineConnectionRequest(requestId: ID!): String @${oem}
  }
`;

export const createConnectionRequest = async (
  _: ExecutionContext["contextValue"],
  {
    oemId,
  }: {
    oemId: string;
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await creationService({
    // @ts-ignore
    args: { input: { oemId }, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const acceptConnectionRequest = async (
  _: ExecutionContext["contextValue"],
  {
    requestId,
    portalToAttach,
  }: {
    requestId: string;
    portalToAttach: string;
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await acceptService({
    // @ts-ignore
    args: { input: { requestId, portalToAttach }, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const declineConnectionRequest = async (
  _: ExecutionContext["contextValue"],
  {
    requestId,
  }: {
    requestId: string;
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await declineService({
    // @ts-ignore
    args: { input: { requestId }, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });
