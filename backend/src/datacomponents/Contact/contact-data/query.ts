import { GraphQLResolveInfo } from "graphql";
import type { ExecutionContext } from "graphql/execution/execute";
import { roles } from "~/directives";
import { getAllContacts } from "~/services/contact/fetch";
import { IContext } from "~/types/common";

const { technician } = roles.is;

export const queryTypes = `#graphql
  type Query {
    listContacts(params: InputQueryParams): PaginatedContacts @${technician}
  }
`;

export const listContacts = async (
  _: ExecutionContext["contextValue"],
  args: { params: { skip: number; limit: number } },
  { dataSources, user, req: { t: translate } }: IContext,
  ___: GraphQLResolveInfo,
) =>
  await getAllContacts({
    args: { input: {}, params: args.params, headers: {}, files: null, query: {} },
    dataSources,
    user,
    translate,
  });
