export default `
    input InputOption {
        _id: ID 
        color: String
        description: String
        value: String
    }

    input InputInsertCustomAdditionalField {
        type: Types
        label: String
        fieldType: FieldTypes
        options: [InputOption]
        isAdditionalField: Boolean
        showOnCustomerPortal: Boolean
        customFieldsWithOrder: [InputCustomFieldOrder]
        description: String
    }

    input InputUpdateCustomAdditionalField {
        _id: ID!
        type: Types
        label: String
        description: String
        fieldType: FieldTypes
        options: [InputOption]
        isAdditionalField: Boolean
        showOnCustomerPortal: Boolean
    }

    input InputCustomFieldOrder {
        _id: ID!
        order: Int!
    }

    input InputUpdateCustomFieldsOrder {
        customFieldsWithOrder: [InputCustomFieldOrder]!
    }

    input InputDeleteOwnOemCustomAdditionalField {
        _id: ID!
        customFieldsWithOrder: [InputCustomFieldOrder]
    }
`;
