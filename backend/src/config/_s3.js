import aws from "aws-sdk";
import { S3Client } from "@aws-sdk/client-s3";

export const S3 = {
  BUCKET: process.env.AWS_S3_BUCKET,
  CREDENTIALS: {
    signatureVersion: "v4",
    region: process.env.AWS_S3_BUCKET_REGION || "eu-north-1",
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
  CDN: process.env.AWS_CLOUDFRONT_URL,
};

export const s3 = new aws.S3(S3.CREDENTIALS);

export const s3Client = new S3Client({
  region: process.env.AWS_S3_BUCKET_REGION || "eu-north-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});
