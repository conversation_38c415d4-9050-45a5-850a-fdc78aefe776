import { Request, Response } from "express";
import { invalidateEventsCache, nylasWeb<PERSON>ookChallenge } from "~/services/userCalendar";
import { logger, verifyWebhookSignature } from "~/utils";
import { serviceFnInputGenerator } from "~/utils/inputGenerator";

export const nylasWebHookChallengeController = (req: Request, res: Response) =>
  res.send(nylasWebHookChallenge(serviceFnInputGenerator(req, res)));

export const userCalendarEventsController = async (req: Request, res: Response) => {
  logger.log("info", "userCalendarEventsController called");

  const parsedBody = JSON.parse(req.body.toString());

  logger.log("info", `grant id: ${parsedBody.data?.object?.grant_id}`);
  logger.log("info", `event id: ${parsedBody.data?.object?.id}`);
  logger.log("info", `event type: ${parsedBody.type}`);

  try {
    const isRequestVerified = verifyWebhookSignature(
      req.body,
      process.env.NYLAS_CALENDAR_WEBHOOK_SECRET!,
      req.headers["x-nylas-signature"] as string,
    );
    if (isRequestVerified) {
      if (parsedBody.data) {
        await invalidateEventsCache(serviceFnInputGenerator(req, res, true));
      }
      return res.send("ok");
    }
    return res.status(403).json({ success: false, error: "Invalid Authentication Header" });
  } catch (err) {
    logger.log("error", `nylas webhook error: ${err}`);
    return res.status(500).json({ success: false, error: err });
  }
};
