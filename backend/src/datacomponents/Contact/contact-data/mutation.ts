import { GraphQLResolveInfo } from "graphql";
import { ExecutionContext } from "graphql/execution/execute";
import { createContact as creationService } from "~/services/contact/create";
import {
  updateContact as modificationService,
  inviteContacts as invitationService,
  approveInvite as approvalService,
  declineInvite as declineService,
  removeCustomerInvite as removeCustomerInviteService,
  resendCustomerInvite as resendCustomerInviteService,
} from "~/services/contact/update";
import { deleteContact as deletionService } from "~/services/contact/delete";

import { features, roles } from "~/directives";
import { IContext } from "~/types/common";
import { CreateContact, InviteContacts, UpdateContact } from "~/types/contact";

const { technician } = roles.is;
const { customerPortal } = features.oemOwns;

export const mutationTypes = `#graphql
  type Mutation {
    createContact(input: InputCreateContact!): Contact @${technician}
    updateContact(input: InputUpdateContact!): Contact @${technician}
    deleteContact(id: ID): ID @${technician}
    inviteContacts(input: InputInviteContacts!): String @${technician} @${customerPortal}
    approveInvite(token: String!): String
    declineInvite(token: String!): String
    removeCustomerInvite(id: ID): ID @${technician} @${customerPortal}
    resendCustomerInvite(id: ID): ID @${technician} @${customerPortal}
  }
`;

export const createContact = async (
  _: ExecutionContext["contextValue"],
  {
    input,
  }: {
    input: CreateContact["args"]["input"];
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await creationService({
    args: { input, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const updateContact = async (
  _: ExecutionContext["contextValue"],
  {
    input,
  }: {
    input: UpdateContact["args"]["input"];
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await modificationService({
    args: { input, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const inviteContacts = async (
  _: ExecutionContext["contextValue"],
  {
    input,
  }: {
    input: InviteContacts["args"]["input"];
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await invitationService({
    args: { input, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const deleteContact = async (
  _: ExecutionContext["contextValue"],
  {
    id,
  }: {
    id: string;
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await deletionService({
    args: { input: { id }, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const approveInvite = async (
  _: ExecutionContext["contextValue"],
  {
    token,
  }: {
    token: string;
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await approvalService({
    args: { input: { token }, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const declineInvite = async (
  _: ExecutionContext["contextValue"],
  {
    token,
  }: {
    token: string;
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await declineService({
    args: { input: { token }, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const removeCustomerInvite = async (
  _: ExecutionContext["contextValue"],
  {
    id,
  }: {
    id: string;
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await removeCustomerInviteService({
    args: { input: { id }, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });

export const resendCustomerInvite = async (
  _: ExecutionContext["contextValue"],
  {
    id,
  }: {
    id: string;
  },
  { dataSources, user, req: { t: translate } }: IContext,
  __: GraphQLResolveInfo,
) =>
  await resendCustomerInviteService({
    args: { input: { id }, files: null, headers: {}, query: {}, params: {} },
    user,
    dataSources,
    translate,
  });
