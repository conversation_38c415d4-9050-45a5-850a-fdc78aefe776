import inputTypes from "~/datacomponents/ConnectionRequest/connection-request-data/input";
import {
  mutationTypes,
  createConnectionRequest,
  acceptConnectionRequest,
  declineConnectionRequest,
} from "~/datacomponents/ConnectionRequest/connection-request-data/mutation";
import {
  queryTypes,
  listConnectionRequests,
} from "~/datacomponents/ConnectionRequest/connection-request-data/query";
import types, {
  typeResolvers,
} from "~/datacomponents/ConnectionRequest/connection-request-data/type";

export default {
  resolvers: {
    Mutation: {
      createConnectionRequest,
      acceptConnectionRequest,
      declineConnectionRequest,
    },
    Query: {
      listConnectionRequests,
    },
    ...typeResolvers,
  },
  types: `
      ${inputTypes}
      ${mutationTypes}
      ${queryTypes}
      ${types}
    `,
};
