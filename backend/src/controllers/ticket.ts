import { Request, Response } from "express";
import { createTicket } from "~/services/ticket/create";
import { updateTicket } from "~/services/ticket/update";
import { getAllTickets, getTicket, getTicketTimeTrackerLogs } from "~/services/ticket/fetch";
import { serviceFnInputGenerator } from "~/utils/inputGenerator";
import { controllerErrorHandler } from "~/utils/controllerErrorHandler";
import { PAGINATED_QUERY_PAGE_SIZE } from "~/constants/global";
import type { ITicket, ITimeTracker } from "~/datamodels/Ticket/interface";
import { secondsToHourMinute } from "~/utils";
import { buildQueryFilters, fetchFilterMappings } from "~/utils/fetchFilterMappings";
import { IUser } from "~/datamodels/User/interface";
import { ERROR_TO_STATUS_CODE_MAP } from "~/errors/constants";
import { ERROR } from "~/environment/_errors";

const project = (tickets: ITicket[]) =>
  tickets.map((ticket: ITicket) => ({
    title: ticket.title,
    description: ticket.description,
    ticketType: ticket.ticketType,
    status: ticket.status,
    id: ticket.id,
    connectionId: ticket.facility ?? null,
    assetSerialNumber: ticket.machine,
    parts: ticket.inventoryParts,
    customFields: ticket.customFields ?? [],
    reporter: ticket.user,
    assignees: ticket.assignees,
    schedule: ticket.schedule
      ? { startTime: ticket.schedule.startTime, endTime: ticket.schedule.endTime }
      : null,
    // @ts-ignore
    createdAt: ticket.created_at,
    // @ts-ignore
    updatedAt: ticket.updated_at,
    ...(ticket?.procedures ? { procedures: ticket?.procedures } : {}),
  }));

const projectTimeTracker = (
  tickets: ITicket[],
  tagMap: Map<string, ITimeTracker>,
  creatorMap: Map<string, IUser>,
) => {
  const allLogs = tickets.reduce((acc, ticket) => {
    if (ticket.timeTrackerLogs) {
      const logs = ticket.timeTrackerLogs
        .map(timeTrackerLog => {
          const ticketTag = tagMap.get(timeTrackerLog.ticketTag?.toString()) ?? null;
          const createdBy = creatorMap.get(timeTrackerLog.createdBy?.toString()) ?? null;

          return {
            ticketId: ticket.id, // Include ticket ID for context
            description: timeTrackerLog.description,
            startTime: timeTrackerLog.startDateTime,
            endTime: timeTrackerLog.endDateTime,
            tag: ticketTag?.label ?? null,
            isBillable: timeTrackerLog.isBillable,
            createdBy: createdBy?.deleted
              ? { name: "Deleted User", email: null }
              : createdBy?.email
              ? { name: createdBy?.name, email: createdBy?.email }
              : {
                  name: "Unknown User",
                  email: null,
                },
            totalTimeSpent: secondsToHourMinute(timeTrackerLog.timeElapsedInSeconds, true),
          };
        })
        .sort((a, b) => (a.startTime < b.startTime ? 1 : -1));
      // @ts-ignore
      return acc.concat(logs);
    }
    return acc;
  }, []);

  return { timeTrackerLogs: allLogs };
};

export const createTicketController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.machineSerialNumber =
      params.args.input.assetSerialNumber ?? params.args.input.machineSerialNumber;
    params.args.input.facilityId = params.args.input.connectionId;

    const ticketDetails = await createTicket(params);
    return res.status(200).json({
      message: "Work Order created successfully!",
      data: { ticketId: ticketDetails?.id },
    });
  } catch (exception: any) {
    controllerErrorHandler(res, exception);
  }
};

export const getTicketController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.ticketId = params.args.params.id;
    const ticketDetails = await getTicket(params);
    if (ticketDetails) {
      return res.status(200).json({
        data: project([ticketDetails])[0],
      });
    }
    return res
      .status(404)
      .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
  } catch (exception: any) {
    controllerErrorHandler(res, exception);
  }
};

export const getTicketTimeTrackerLogsController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.ticketId = params.args.query.ticketId;
    params.args.input.userEmail = params.args.query.userEmail;
    const [ticketDetails, tagMap, creatorMap] = await getTicketTimeTrackerLogs(params);
    if (ticketDetails) {
      return res.status(200).json({
        // @ts-ignore
        data: projectTimeTracker(ticketDetails, tagMap, creatorMap),
      });
    }
    return res
      .status(404)
      .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
  } catch (exception: any) {
    controllerErrorHandler(res, exception);
  }
};

export const getAllTicketsController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.query.facilityIdentifier = params.args.query.connectionId;
    params.args.params.limit = PAGINATED_QUERY_PAGE_SIZE;
    params.args.params.skip = (Number(params.args.query.page || 1) - 1) * PAGINATED_QUERY_PAGE_SIZE;
    const mappings = await fetchFilterMappings(params.args.query, params.dataSources, params.user);
    const filters = buildQueryFilters(params.args.query, mappings);
    // @ts-ignore
    params.args.params.where = filters;
    const tickets = await getAllTickets(params);
    return res.status(200).json({
      data: project(tickets?.tickets),
      totalPages: Math.ceil(tickets!.totalCount / PAGINATED_QUERY_PAGE_SIZE),
      page: Number(params.args.query.page || 1),
    });
  } catch (exception: any) {
    if (exception.code === ERROR_TO_STATUS_CODE_MAP[ERROR.DOCUMENT.NOT_FOUND]) {
      return res.status(200).json({
        data: [],
        totalPages: 0,
        page: 1,
      });
    }
    controllerErrorHandler(res, exception);
  }
};

export const updateTicketController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.ticketIdentifier = params.args.params.id;
    const ticketDetails = await updateTicket(params);
    if (ticketDetails) {
      return res.status(200).json({
        message: "Work Order updated successfully!",
        data: { ticketId: ticketDetails?.id },
      });
    }
    return res
      .status(404)
      .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
  } catch (exception: any) {
    controllerErrorHandler(res, exception);
  }
};
