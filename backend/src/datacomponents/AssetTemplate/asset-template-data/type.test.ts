import { afterEach, describe, expect, test, vi } from "vitest";
import { typeResolvers } from "~/datacomponents/AssetTemplate/asset-template-data/type";

const { AssetTemplatePart } = typeResolvers;

const _id = "6499a52b5dfe76648ff67a91";

const dataSources = {
  InventoryPart: {
    loadOne: vi.fn(),
  },
  User: {
    loadOne: vi.fn(),
  },
};

vi.mock("~/utils", async () => ({}));

describe("Type Asset Template", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("AssetTemplatePart", async () => {
    describe("part", async () => {
      test("return null if no part", async () => {
        const result = await AssetTemplatePart.part(
          // @ts-ignore
          {},
          null,
          {
            dataSources,
          },
        );

        expect(result).toBeNull();
      });

      test("return part", async () => {
        await AssetTemplatePart.part(
          {
            // @ts-ignore
            part: _id,
          },
          null,
          {
            dataSources,
          },
        );

        expect(dataSources.InventoryPart.loadOne).toHaveBeenCalledOnce();
        expect(dataSources.InventoryPart.loadOne).toHaveBeenCalledWith(_id);
      });
    });

    describe("part", async () => {
      test("return null if no addedBy", async () => {
        const result = await AssetTemplatePart.addedBy(
          // @ts-ignore
          {},
          null,
          {
            dataSources,
          },
        );

        expect(result).toBeNull();
      });

      test("addedBy", async () => {
        await AssetTemplatePart.addedBy(
          {
            // @ts-ignore
            addedBy: _id,
          },
          null,
          {
            dataSources,
          },
        );

        expect(dataSources.User.loadOne).toHaveBeenCalledOnce();
        expect(dataSources.User.loadOne).toHaveBeenCalledWith(_id);
      });
    });
  });
});
