/* eslint-disable max-classes-per-file */
/* eslint-disable */
// @ts-nocheck
import { FilterQuery, Model, Types } from "mongoose";
import Random from "random-seed-generator";
import { expect, test, vi } from "vitest";
import { definitions } from "~/agenda/definitions";
import { createIndexingOperationProgressMessage } from "~/datacomponents/AiAssistant/ai-assistant-data/helpers";
import {
  AssistantType,
  DocumentStatus as AiAssistantDocumentStatus,
} from "~/datamodels/AiAssistant/constants";
import { mutationResolvers } from "~/datacomponents/AiAssistant/ai-assistant-data/mutation";
import { queryResolvers } from "~/datacomponents/AiAssistant/ai-assistant-data/query";
import { mutationResolvers as assetMutationResolvers } from "~/datacomponents/Asset/asset-data/mutation";
import { mutationResolvers as oemMutationResolvers } from "~/datacomponents/Oem/oem-data/_mutation";
import { queryResolvers as oemQueryResolvers } from "~/datacomponents/Oem/oem-data/_query";
import { queryResolvers as userQueryResolvers } from "~/datacomponents/User/user-data/_query";
import { mutationResolvers as teamMutationResolvers } from "~/datacomponents/Team/team-data/_mutation";
import { db } from "~/dataconnectors/mongodb";
import { AiAssistantDataSource } from "~/datasources/db/AiAssistant";
import AiAssistant from "~/datamodels/AiAssistant";
import { AppConfigDataSource } from "~/datasources/db/AppConfig";
import AppConfig from "~/datamodels/AppConfig";
import { MachineTemplateDataSource } from "~/datasources/db/MachineTemplate";
import MachineTemplate from "~/datamodels/MachineTemplate";
import { IAiAssistant } from "~/datamodels/AiAssistant/interface";
import { CustomAdditionalFieldDataSource } from "~/datasources/db/CustomAdditionalField";
import CustomAdditionalField from "~/datamodels/CustomAdditionalField";
import { KnowledgeBaseDataSource } from "~/datasources/db/KnowledgeBase";
import AiAssistantChat from "~/datamodels/AiAssistantChat";
import { AiAssistantChatDataSource } from "~/datasources/db/AiAssistantChat";
import KnowledgeBase from "~/datamodels/KnowledgeBase";
import { MachineDataSource } from "~/datasources/db/Machine";
import Machine from "~/datamodels/Machine";
import { OemDataSource } from "~/datasources/db/Oem";
import Oem from "~/datamodels/Oem";
import { TeamDataSource } from "~/datasources/db/Team";
import Team from "~/datamodels/Team";
import { UserDataSource } from "~/datasources/db/User";
import User from "~/datamodels/User";
import { NOTIFICATION_IDENTIFIERS } from "~/constants/notification-identifiers";
import { getOemChannelId } from "~/utils";
import { processFileDeletionWebhook } from "~/services/box";
import { mockAppConfig } from "#/tests/mocks/AppConfig";

const mutations = mutationResolvers.Mutation;
const queries = queryResolvers.Query;

const {
  addAiAssistantDocuments,
  createAiAssistantV2,
  removeAiAssistant,
  removeAiAssistantDocuments,
  renameAiAssistant,
} = mutations;

const { getAiAssistant, listAiAssistants } = queries;

let jobsData = [];

vi.mock("fs", () => ({ unlinkSync: vi.fn() }));

vi.mock("~/agenda/run", () => ({ default: data => jobsData.push(data) }));

vi.mock("~/agenda/helpers", () => ({
  getJobsByDataAttributeFilters: () => [],
}));

vi.mock("~/utils/chat", () => ({
  getOemUserChannelId: vi.fn(() => "mocked-channel-id"),
  getOemChannelId: vi.fn(() => "mocked-channel-id-2"),
}));

vi.mock("~/datasources/db/baseDataSource", () => {
  class BaseModelDataSource {
    collectionName: string;

    constructor(collection: { [key: string]: Model<IAiAssistant> }) {
      const collectionName = Object.keys(collection).find(Boolean);
      this.collectionName = collectionName;
      this[this.collectionName] = collection[collectionName];
    }

    aggregate = (...ops) => this[this.collectionName].aggregate(...ops);

    bulkWrite = operations => this[this.collectionName].bulkWrite(operations);

    deleteById = id => this[this.collectionName].findByIdAndDelete(id);

    getById = docID => this[this.collectionName].findById(docID);

    getOne = (filters: FilterQuery<IAiAssistant>) => this[this.collectionName].findOne(filters);

    getOneByQuery = (query, projection, options = {}) =>
      this[this.collectionName].findOne(query, projection, options);

    getManyByQuery = (query, projection, options = {}) =>
      this[this.collectionName].find(query, projection, options);

    getMany = (filters: FilterQuery<IAiAssistant>) => this[this.collectionName].find(filters);

    loadOne = docID => this[this.collectionName].findById(docID);

    countDocuments = (params = {}) => {
      return this[this.collectionName].countDocuments(params);
    };

    save = (data, options) => {
      const {
        conditions = {},
        new: returnNew = true,
        setDefaultsOnInsert = true,
        timestamps = true,
      } = options || {};
      const { _id, ...doc } = data;
      const opts = {
        runValidators: true,
        setDefaultsOnInsert,
        upsert: false,
        new: returnNew,
        context: "query",
        timestamps,
      };

      if (_id) return this[this.collectionName].findOneAndUpdate({ _id, ...conditions }, doc, opts);

      return this[this.collectionName].create(doc);
    };

    updateManyByQuery = async (query, doc, options = {}) => {
      try {
        return await this[this.collectionName].updateMany(query, doc, options);
      } catch (error) {
        console.error("Error in updateManyByQuery:", error);
        throw error;
      }
    };

    updateOne = async (query, doc, options = {}) => {
      try {
        return await this[this.collectionName].updateOne(query, doc, options);
      } catch (error) {
        console.error("Error in updateOne:", error);
        throw error;
      }
    };

    softDeleteById = async docID =>
      this.save({
        _id: docID,
        deleted: true,
      });

    softDeleteMany = async (filterQuery: any, userId: string) => {
      return this[this.collectionName].delete(filterQuery, userId);
    };
  }

  return { BaseDataSource: BaseModelDataSource };
});

vi.mock("mailgun.js", () => ({
  default: class {
    client = () => ({
      messages: { create: () => new Promise(resolve => resolve()) },
    });
  },
}));

vi.mock("~/utils/logger", () => ({
  default: {
    error: console.error,
    info: console.info,
    log: console.log,
    warn: console.warn,
  },
}));

const mockTranslate = vi.fn(key => key);

const aiAssistantName: string = Random.string({
  length: 24,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
});
const invalidOemID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const fileSize = Number(
  Random.string({
    length: 3,
    pool: "0123456789",
  }),
);
const machineExternalfileID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const machineExternalfolderID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const machineID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const machineInternalfileID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const machineInternalfolderID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});

const machineName: string = Random.string({
  length: 24,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
});
const machineSerialNumber: string = Random.string({
  length: 6,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
});
const oemEmail: string = `${Random.string({
  length: 5,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
})}@${Random.string({ length: 5, pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ" })}.com`;
const oemName: string = Random.string({
  length: 24,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
});
const oemUrl: string = `${Random.string({
  length: 5,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
})}/${Random.string({ length: 5, pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ" })}.com`;
const staffUserEmail: string = `${Random.string({
  length: 5,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
})}@${Random.string({ length: 5, pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ" })}.com`;
const staffUserName: string = Random.string({
  length: 24,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
});
const teamName: string = Random.string({
  length: 24,
  pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
});

const getDataSources = (boxFolderToFileMapping: { [key: string]: Array<string> }) => {
  let _boxFileToFolderMapping = {};
  const _boxFolderToFileMapping = boxFolderToFileMapping;

  const addMapping = (folderID: string, fileIds: Array<string>) => {
    _boxFolderToFileMapping[folderID] = fileIds;
    _boxFileToFolderMapping = Object.entries(_boxFolderToFileMapping).reduce(
      (map, [_folderID, _fileIds]) => ({
        ...map,
        ..._fileIds.reduce(
          (_map, fileID) => ({
            ..._map,
            [fileID]: _folderID,
          }),
          {},
        ),
      }),
      {},
    );
  };

  return {
    AiAssistant: new AiAssistantDataSource({ AiAssistant: AiAssistant(db) }),
    AiAssistantChat: new AiAssistantChatDataSource({ AiAssistantChat: AiAssistantChat(db) }),
    AppConfig: new AppConfigDataSource({ AppConfig: AppConfig(db) }),
    BoxApi: {
      createFolder: vi.fn(),
      downloadFileById: vi.fn(
        ({ callback }) =>
          new Promise(resolve => {
            callback();
            resolve();
          }),
      ),
      getFileById: vi.fn(({ fileID }) => ({
        id: fileID,
        parent: {
          id: _boxFileToFolderMapping[fileID],
        },
        size: fileSize,
      })),
      getFolderById: vi.fn(({ id }) => ({
        item_collection: {
          entries: (_boxFolderToFileMapping[id] || []).map(_id => ({
            id: _id,
            type: "file",
            name: "sample.pdf",
          })),
        },
      })),
    },
    KnowledgeBase: new KnowledgeBaseDataSource({ KnowledgeBase: KnowledgeBase(db) }),
    CustomAdditionalField: new CustomAdditionalFieldDataSource({
      CustomAdditionalField: CustomAdditionalField(db),
    }),
    Machine: new MachineDataSource({ Machine: Machine(db) }),
    MachineTemplate: new MachineTemplateDataSource({ MachineTemplate: MachineTemplate(db) }),
    Oem: new OemDataSource({ Oem: Oem(db) }),
    PubnubApi: {
      addChannelsToChannelGroup: vi.fn(),
      publishMessage: vi.fn(() => new Promise(resolve => resolve())),
      removeChannelsFromChannelGroup: vi.fn(),
      setChannelMembers: vi.fn(),
    },
    Team: new TeamDataSource({ Team: Team(db) }),
    User: new UserDataSource({ User: User(db) }),
    Utils: { addMapping },
    VectaraApi: {
      createAssistant: vi.fn(),
      deleteAssistant: vi.fn(),
      deleteDocument: vi.fn(),
      uploadFileToAiAssistant: vi.fn(
        () => new Promise(resolve => resolve({ storage_usage: { bytes_used: fileSize } })),
      ),
    },
  };
};

const failJobsForAiAssistant = async (
  _id: string,
  dataSources: ReturnType<typeof getDataSources>,
) => {
  await Promise.all(
    jobsData
      .filter(({ aiAssistantID }) => aiAssistantID === _id)
      .map(jobData =>
        definitions[jobData.jobName].onFailure(dataSources, null, {
          attrs: { data: jobData },
        }),
      ),
  );

  jobsData = jobsData.filter(({ aiAssistantID }) => aiAssistantID !== _id);
};

const runJobsForAiAssistant = async (
  _id: string,
  dataSources: ReturnType<typeof getDataSources>,
  oemId = null,
) => {
  await Promise.all(
    jobsData
      .filter(
        ({ aiAssistantID, aiAssistantOemId }) =>
          aiAssistantID === _id || aiAssistantOemId === oemId,
      )
      .map(jobData =>
        definitions[jobData.jobName].func(dataSources, {
          attrs: { data: jobData },
        }),
      ),
  );

  jobsData = jobsData.filter(({ aiAssistantID }) => aiAssistantID !== _id);
};

const createNecessaryData = async () => {
  const _extrafileID: string = Random.string({
    length: 24,
    pool: "ABCDEF0123456789",
  });
  const _machineExternalfileID: string = Random.string({
    length: 24,
    pool: "ABCDEF0123456789",
  });
  const _machineExternalfolderID: string = Random.string({
    length: 24,
    pool: "ABCDEF0123456789",
  });
  const _machineInternalfileID: string = Random.string({
    length: 24,
    pool: "ABCDEF0123456789",
  });
  const _machineInternalfolderID: string = Random.string({
    length: 24,
    pool: "ABCDEF0123456789",
  });
  const _machineName: string = Random.string({
    length: 24,
    pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
  });
  const _machineSerialNumber: string = Random.string({
    length: 6,
    pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
  });
  const _oemEmail: string = `${Random.string({
    length: 5,
    pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
  })}@${Random.string({
    length: 5,
    pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
  })}.com`;
  const _oemName: string = Random.string({
    length: 24,
    pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
  });
  const _oemUrl: string = `${Random.string({
    length: 5,
    pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
  })}/${Random.string({
    length: 5,
    pool: "ABCDEFEFGHIJKLMNOPQRSTUVWXYZ",
  })}.com`;

  const foldersMap = {
    extrafolderID: [_extrafileID],
    [_machineExternalfolderID]: [_machineExternalfileID],
    [_machineInternalfolderID]: [_machineInternalfileID],
  };

  const dataSources = getDataSources(foldersMap);

  dataSources.Utils.addMapping(_machineExternalfolderID, [_machineExternalfileID]);
  dataSources.Utils.addMapping(_machineInternalfolderID, [_machineInternalfileID]);

  await dataSources.AppConfig.save({
    plans: mockAppConfig.plans,
    allocatedQueries: 0,
    allocatedStorage: 0,
  });

  let oemData = await oemMutationResolvers.Mutation.createOem(
    null,
    {
      input: {
        name: _oemName,
        oemEmail: _oemEmail,
        oemName: _oemName,
        paidFeatures: ["aiAssistants"],
        urlOem: _oemUrl,
      },
    },
    { dataSources, req: { t: mockTranslate } },
  );

  const oemOwner = await oemQueryResolvers.Query.oemByIdWithAdmin(
    null,
    {
      id: oemData._id.toString(),
    },
    { dataSources },
  );

  const oemAdminUser = {
    id: oemOwner?.admin._id.toString(),
    organization: oemData._id.toString(),
    primaryRole: "OWNER",
    teams: [],
    aiAssistantConfiguration: { consumedStorage: 0 },
  };

  let [machineData] = await Promise.all([
    assetMutationResolvers.Mutation.createOwnOemAsset(
      null,
      {
        input: {
          name: _machineName,
          serialNumber: _machineSerialNumber,
          teams: [],
        },
      },
      {
        dataSources,
        user: oemAdminUser,
        req: { t: (_: string) => "" },
      },
    ),
    oemMutationResolvers.Mutation.updateOemSupportAccount(
      null,
      {
        input: {
          hasAiAssistantAccess: true,
          role: "OWNER",
          _id: oemAdminUser.id,
        },
      },
      { dataSources, user: oemAdminUser, req: { t: (_: string) => "" } },
    ),
  ]);

  machineData = await dataSources.Machine.save({
    _id: machineData._id.toString(),
    documentFolders: {
      internalId: _machineInternalfolderID,
      externalId: _machineExternalfolderID,
    },
  });

  const aiAssistant = await createAiAssistantV2(
    null,
    {
      input: {
        assistantType: AssistantType.INTERNAL,
        assetId: machineData._id,
        description: "123",
        documentIds: [_machineExternalfileID, _machineInternalfileID],
        name: "test",
      },
    },
    {
      dataSources,
      user: oemAdminUser,
      req: { t: (_: string) => "" },
    },
  );

  await runJobsForAiAssistant(aiAssistant._id?.toString(), dataSources);

  return {
    aiAssistant,
    dataSources,
    foldersMap,
    machineData,
    externalfolderID: _machineExternalfolderID,
    internalfolderID: _machineInternalfolderID,
    oemAdminUser,
    oemData,
  };
};

test("Ai Assistant CRUD operations", async () => {
  const dataSources = getDataSources({
    [machineExternalfolderID]: [machineExternalfileID],
    [machineInternalfolderID]: [machineInternalfileID],
  });
  dataSources.Utils.addMapping(machineExternalfolderID, [machineExternalfileID]);
  dataSources.Utils.addMapping(machineInternalfolderID, [machineInternalfileID]);

  const PubnubApiPublishMessage = dataSources.PubnubApi.publishMessage;
  const VectaraApiCreate = dataSources.VectaraApi.createAssistant;
  const VectaraApiDelete = dataSources.VectaraApi.deleteAssistant;

  await dataSources.AppConfig.save({
    plans: mockAppConfig.plans,
    allocatedQueries: 0,
    allocatedStorage: 0,
  });

  // create an OEM
  let oemData = await oemMutationResolvers.Mutation.createOem(
    null,
    {
      input: { name: oemName, oemEmail, oemName, urlOem: oemUrl },
    },
    { dataSources, req: { t: (_: string) => "" } },
  );

  const { oem, admin } = await oemQueryResolvers.Query.oemByIdWithAdmin(
    null,
    {
      id: oemData._id.toString(),
    },
    { dataSources, req: { t: (_: string) => "" } },
  );

  const oemAdminUser = {
    id: admin._id.toString(),
    organization: oemData._id.toString(),
    primaryRole: "OWNER",
    teams: [],
  };

  // create a machine in the OEM
  const machineData = await assetMutationResolvers.Mutation.createOwnOemAsset(
    null,
    {
      input: {
        name: machineName,
        serialNumber: machineSerialNumber,
        teams: [],
      },
    },
    {
      dataSources,
      user: oemAdminUser,
      req: { t: (_: string) => "" },
    },
  );

  await dataSources.Machine.save({
    _id: machineData._id.toString(),
    documentFolders: {
      internalId: machineInternalfolderID,
      externalId: machineExternalfolderID,
    },
  });

  // verify that one user is consumed in the configuration
  let currentOemData = await oemQueryResolvers.Query.getOwnOem(null, null, {
    dataSources,
    user: oemAdminUser,
  });
  expect(currentOemData.aiAssistantConfiguration.consumedUsers).toEqual(1);

  // Create machine assistant for non existent machine
  expect(
    async () =>
      await createAiAssistantV2(
        null,
        {
          input: {
            assistantType: AssistantType.INTERNAL,
            assetId: machineID,
            description: "123",
            documentIds: [machineExternalfileID, machineInternalfileID],
            name: "test",
          },
        },
        {
          dataSources,
          user: oemAdminUser,
          req: { t: (_: string) => "" },
        },
      ),
  ).rejects.toThrowError();

  const newAiAssistant = await createAiAssistantV2(
    null,
    {
      input: {
        assistantType: AssistantType.INTERNAL,
        assetId: machineData._id,
        description: "123",
        documentIds: [machineExternalfileID, machineInternalfileID],
        name: "test",
      },
    },
    {
      dataSources,
      user: oemAdminUser,
      req: { t: (_: string) => "" },
    },
  );

  expect(VectaraApiCreate).toHaveBeenCalledWith(new Types.ObjectId(newAiAssistant._id));

  // Create machine assistant for machine with existing Machine Assistant
  expect(
    async () =>
      await createAiAssistantV2(
        null,
        {
          input: {
            assistantType: AssistantType.INTERNAL,
            assetId: machineData._id,
            description: "123",
            documentIds: [machineExternalfileID, machineInternalfileID],
            name: "test",
          },
        },
        {
          dataSources,
          user: oemAdminUser,
          req: { t: (_: string) => "" },
        },
      ),
  ).rejects.toThrowError();

  // verify the Ai Assistant is now available
  // and the documents are awaiting indexing
  let response = await listAiAssistants(
    null,
    {},
    {
      dataSources,
      user: oemAdminUser,
      req: { t: (_: string) => "" },
    },
    null,
  );
  response = response.aiAssistants;
  expect(response).toHaveLength(1);
  expect(response.find(Boolean)?._id).toEqual(newAiAssistant._id);
  expect(response.find(Boolean)?.assistantName).toEqual("test");
  response
    .find(Boolean)
    .documents.externalDocuments.forEach(({ consumedStorage, status }) =>
      expect([consumedStorage, status]).toEqual([0, AiAssistantDocumentStatus.INITIATED_INDEXING]),
    );
  response
    .find(Boolean)
    .documents.internalDocuments.forEach(({ consumedStorage, status }) =>
      expect([consumedStorage, status]).toEqual([0, AiAssistantDocumentStatus.INITIATED_INDEXING]),
    );

  await runJobsForAiAssistant(newAiAssistant._id?.toString(), dataSources, oemData._id.toString());

  // should recieve notifications on PubNub
  response
    .find(Boolean)
    .documents.externalDocuments.forEach(({ _id, externalStorageServiceDocumentID }) =>
      expect(PubnubApiPublishMessage).toHaveBeenCalledWith(
        createIndexingOperationProgressMessage({
          aiAssistantID: newAiAssistant._id?.toString(),
          consumedStorage: fileSize,
          documentID: _id?.toString(),
          externalStorageServiceDocumentID,
          key: "externalDocuments",
          oemID: oemAdminUser.organization,
          status: AiAssistantDocumentStatus.INDEXED,
        }),
      ),
    );
  response
    .find(Boolean)
    .documents.internalDocuments.forEach(({ _id, externalStorageServiceDocumentID }) =>
      expect(PubnubApiPublishMessage).toHaveBeenCalledWith(
        createIndexingOperationProgressMessage({
          aiAssistantID: newAiAssistant._id?.toString(),
          consumedStorage: fileSize,
          documentID: _id?.toString(),
          externalStorageServiceDocumentID,
          key: "internalDocuments",
          oemID: oemAdminUser.organization,
          status: AiAssistantDocumentStatus.INDEXED,
        }),
      ),
    );

  // verify the Ai Assistant documents are indexed
  response = await listAiAssistants(
    null,
    { limit: 100 },
    {
      dataSources,
      user: oemAdminUser,
      req: { t: (_: string) => "" },
    },
    null,
  );
  response = response.aiAssistants;
  response
    .find(Boolean)
    .documents.externalDocuments.forEach(({ consumedStorage, status }) =>
      expect([consumedStorage, status]).toEqual([fileSize, AiAssistantDocumentStatus.INDEXED]),
    );
  response
    .find(Boolean)
    .documents.internalDocuments.forEach(({ consumedStorage, status }) =>
      expect([consumedStorage, status]).toEqual([fileSize, AiAssistantDocumentStatus.INDEXED]),
    );

  // verify the USER consumption has increased
  let currentUserData = await userQueryResolvers.Query.currentUser(null, null, {
    dataSources,
    user: oemAdminUser,
  });
  expect(currentUserData.aiAssistantConfiguration.consumedStorage).toEqual(2 * fileSize);

  await renameAiAssistant(
    null,
    {
      input: { _id: newAiAssistant._id, assistantName: aiAssistantName },
    },
    {
      dataSources,
      user: oemAdminUser,
      req: { t: (_: string) => "" },
    },
  );

  // verify the name has changed
  response = await listAiAssistants(
    null,
    { limit: 100 },
    {
      dataSources,
      user: oemAdminUser,
      req: { t: (_: string) => "" },
    },
    null,
  );
  response = response.aiAssistants;
  expect(response.find(Boolean)?.assistantName).toEqual(aiAssistantName);

  // create an oem support account and grant AiAssistant access
  const staffUserData = await oemMutationResolvers.Mutation.createOemSupportAccount(
    null,
    { input: { email: staffUserEmail, name: staffUserName } },
    {
      dataSources,
      user: oemAdminUser,
      req: { t: (_: string) => "" },
    },
    null,
  );

  const oemStaffUser = {
    id: staffUserData._id.toString(),
    organization: staffUserData.oem.toString(),
    primaryRole: "STAFF",
    teams: [],
  };

  await oemMutationResolvers.Mutation.updateOemSupportAccount(
    null,
    {
      input: {
        hasAiAssistantAccess: true,
        role: "STAFF",
        _id: oemStaffUser.id,
      },
    },
    { dataSources, req: {}, user: oemAdminUser },
  );

  // create a team with staff user in that team
  await teamMutationResolvers.Mutation.createTeam(
    null,
    { input: { name: teamName, users: [oemStaffUser.id] } },
    {
      dataSources,
      user: oemAdminUser,
    },
    null,
  );

  // verify the user doesn't have access to the Ai Assistant anymore
  response = await listAiAssistants(
    null,
    { limit: 100 },
    {
      dataSources,
      user: oemStaffUser,
    },
    null,
  );
  response = response.aiAssistants;
  expect(response).toHaveLength(0);

  await removeAiAssistant(
    null,
    { aiAssistantID: newAiAssistant._id },
    {
      dataSources,
      user: oemAdminUser,
    },
    null,
  );
  expect(VectaraApiDelete).toHaveBeenCalledWith(new Types.ObjectId(newAiAssistant._id));

  // verify the assistant is now removed
  response = await listAiAssistants(
    null,
    { limit: 100 },
    {
      dataSources,
      user: oemAdminUser,
    },
    null,
  );
  response = response.aiAssistants;
  expect(response).toHaveLength(0);

  // verify the USER consumption has decreased
  let newCurrentUserData = await userQueryResolvers.Query.currentUser(null, null, {
    dataSources,
    user: oemAdminUser,
  });
  expect(newCurrentUserData.aiAssistantConfiguration.consumedStorage).toBeLessThanOrEqual(
    currentUserData.aiAssistantConfiguration.consumedStorage,
  );
});

test("Ai Assistant file de indexing", async () => {
  const { aiAssistant, dataSources, oemAdminUser, oemData } = await createNecessaryData();
  const documentID = aiAssistant.documents?.externalDocuments.find(Boolean)?._id.toString();
  const externalStorageServiceDocumentID =
    aiAssistant.documents?.externalDocuments.find(Boolean)?.externalStorageServiceDocumentID;

  // de index a file
  await removeAiAssistantDocuments(
    null,
    {
      input: {
        _id: aiAssistant._id?.toString(),
        documentIds: [documentID],
      },
    },
    { dataSources, user: oemAdminUser },
    null,
  );

  expect(dataSources.PubnubApi.publishMessage).toHaveBeenCalledWith(
    createIndexingOperationProgressMessage({
      aiAssistantID: aiAssistant._id?.toString(),
      documentID,
      externalStorageServiceDocumentID,
      key: "externalDocuments",
      oemID: oemData._id.toString(),
      status: AiAssistantDocumentStatus.INITIATED_DEINDEXING,
    }),
  );

  // de indexing the same file should throw error
  // CAUTION: Currently this is only throwing error because
  // we have mocked logger to console which returns undefined
  // but winston logger will return an instance of winston
  // so on production, it will exit silently instead of throwing
  expect(
    async () =>
      await removeAiAssistantDocuments(
        null,
        {
          input: {
            _id: aiAssistant._id?.toString(),
            documentIds: [documentID],
          },
        },
        { dataSources, user: oemAdminUser },
        null,
      ),
  ).rejects.toThrowError();

  await runJobsForAiAssistant(aiAssistant._id?.toString(), dataSources);

  expect(dataSources.PubnubApi.publishMessage).toHaveBeenCalledWith(
    createIndexingOperationProgressMessage({
      aiAssistantID: aiAssistant._id?.toString(),
      consumedStorage: 0,
      documentID,
      externalStorageServiceDocumentID,
      key: "externalDocuments",
      oemID: oemData._id.toString(),
      status: AiAssistantDocumentStatus.DE_INDEXED,
    }),
  );

  // external documents array should be empty now
  const updatedAiAssistant = await getAiAssistant(
    null,
    { id: aiAssistant._id?.toString() },
    { dataSources, user: oemAdminUser },
    null,
  );
  expect(updatedAiAssistant._doc.documents?.externalDocuments).toHaveLength(0);

  // only a single file should be taking up the space now
  let currentUserData = await userQueryResolvers.Query.currentUser(null, null, {
    dataSources,
    user: oemAdminUser,
  });
  expect(currentUserData.aiAssistantConfiguration.consumedStorage).toEqual(fileSize);
});

test("Ai Assistant file indexing for new file", async () => {
  const { aiAssistant, dataSources, externalfolderID, machineData, oemAdminUser, oemData } =
    await createNecessaryData();
  dataSources.PubnubApi.publishMessage.mockClear();

  const newfileID: string = Random.string({
    length: 24,
    pool: "ABCDEF0123456789",
  });

  dataSources.Utils.addMapping(externalfolderID, [newfileID]);

  // index a file
  await addAiAssistantDocuments(
    null,
    {
      input: {
        _id: aiAssistant._id?.toString(),
        files: [newfileID],
      },
    },
    { dataSources, user: oemAdminUser },
    null,
  );

  expect([
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.aiAssistantID,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.status,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.text,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].channel,
  ]).toEqual([
    aiAssistant._id?.toString(),
    AiAssistantDocumentStatus.INITIATED_INDEXING,
    NOTIFICATION_IDENTIFIERS.AI_ASSISTANT_INDEXING_PROGRESS,
    getOemChannelId(oemAdminUser.id),
  ]);
  dataSources.PubnubApi.publishMessage.mockClear();

  // re indexing the same file should be ignored
  await addAiAssistantDocuments(
    null,
    {
      input: {
        _id: aiAssistant._id?.toString(),
        files: [newfileID],
      },
    },
    { dataSources, user: oemAdminUser },
    null,
  );
  expect(dataSources.PubnubApi.publishMessage).not.toHaveBeenCalled();

  await runJobsForAiAssistant(aiAssistant._id?.toString(), dataSources);

  expect([
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.aiAssistantID,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.status,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.text,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].channel,
  ]).toEqual([
    aiAssistant._id?.toString(),
    AiAssistantDocumentStatus.INDEXED,
    NOTIFICATION_IDENTIFIERS.AI_ASSISTANT_INDEXING_PROGRESS,
    getOemChannelId(oemAdminUser.id),
  ]);
});

test("Ai Assistant file indexing retry for failed file", async () => {
  const { aiAssistant, dataSources, externalfolderID, machineData, oemAdminUser, oemData } =
    await createNecessaryData();
  dataSources.PubnubApi.publishMessage.mockClear();

  const newfileID: string = Random.string({
    length: 24,
    pool: "ABCDEF0123456789",
  });

  dataSources.Utils.addMapping(externalfolderID, [newfileID]);

  // index a file
  await addAiAssistantDocuments(
    null,
    {
      input: {
        _id: aiAssistant._id?.toString(),
        files: [newfileID],
      },
    },
    { dataSources, user: oemAdminUser },
    null,
  );

  dataSources.PubnubApi.publishMessage.mockClear();
  await failJobsForAiAssistant(aiAssistant._id?.toString(), dataSources);

  expect([
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.aiAssistantID,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.status,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.text,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].channel,
  ]).toEqual([
    aiAssistant._id?.toString(),
    AiAssistantDocumentStatus.INDEXING_FAILED,
    NOTIFICATION_IDENTIFIERS.AI_ASSISTANT_INDEXING_PROGRESS,
    getOemChannelId(oemAdminUser.id),
  ]);

  dataSources.PubnubApi.publishMessage.mockClear();
  // retry indexing
  await addAiAssistantDocuments(
    null,
    {
      input: {
        _id: aiAssistant._id?.toString(),
        files: [newfileID],
      },
    },
    { dataSources, user: oemAdminUser },
    null,
  );

  expect([
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.aiAssistantID,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.status,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.text,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].channel,
  ]).toEqual([
    aiAssistant._id?.toString(),
    AiAssistantDocumentStatus.INITIATED_INDEXING,
    NOTIFICATION_IDENTIFIERS.AI_ASSISTANT_INDEXING_PROGRESS,
    getOemChannelId(oemAdminUser.id),
  ]);
  dataSources.PubnubApi.publishMessage.mockClear();

  await runJobsForAiAssistant(aiAssistant._id?.toString(), dataSources);

  expect([
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.aiAssistantID,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.status,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].message.text,
    dataSources.PubnubApi.publishMessage.mock.calls[0][0].channel,
  ]).toEqual([
    aiAssistant._id?.toString(),
    AiAssistantDocumentStatus.INDEXED,
    NOTIFICATION_IDENTIFIERS.AI_ASSISTANT_INDEXING_PROGRESS,
    getOemChannelId(oemAdminUser.id),
  ]);
});

test("Document de indexing", async () => {
  const {
    aiAssistant,
    dataSources,
    externalfolderID,
    foldersMap,
    machineData,
    oemAdminUser,
    oemData,
  } = await createNecessaryData();
  dataSources.PubnubApi.publishMessage.mockClear();

  const fileID = foldersMap[externalfolderID].find(Boolean);
  await processFileDeletionWebhook(dataSources, fileID, oemAdminUser);
  expect(dataSources.PubnubApi.publishMessage).not.toHaveBeenCalled();

  // re running shouldn't do anything since the file is already in de indexing phase
  dataSources.PubnubApi.publishMessage.mockClear();
  await processFileDeletionWebhook(dataSources, fileID, oemAdminUser);
  expect(dataSources.PubnubApi.publishMessage).not.toHaveBeenCalled();

  await runJobsForAiAssistant(aiAssistant._id?.toString(), dataSources);

  // re running shouldn't do anything since the file is de indexed
  dataSources.PubnubApi.publishMessage.mockClear();
  await processFileDeletionWebhook(dataSources, fileID, oemAdminUser);
  expect(dataSources.PubnubApi.publishMessage).not.toHaveBeenCalled();
});
