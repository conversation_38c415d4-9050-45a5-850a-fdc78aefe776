import { SCHEDULE_TICKET_CREATION_JOB } from "~/agenda/constants";
import { definitions } from "~/agenda/definitions";
import { defineJob } from "~/agenda/helpers";
import runJob from "~/agenda/run";
import { agenda } from "~/config";
import { IContext } from "~/types/common";
import { logger } from "~/utils";

const handleLegacyJobs = async () => {
  const time = new Date();
  const jobs = await agenda.jobs({
    "data.eventId": { $exists: true },
    name: { $ne: SCHEDULE_TICKET_CREATION_JOB },
    nextRunAt: { $lte: new Date() },
  });

  logger.info(`[AGENDA][WATCHER][CHECK][JOBS] Found ${jobs.length} legacy jobs`);

  await Promise.all(
    jobs.map(async job => {
      await agenda.cancel({ name: job.attrs.name });
      const { eventId, contextUser, maintenanceOn } = job.attrs.data || {};
      await runJob({
        contextUser,
        eventId,
        jobName: SCHEDULE_TICKET_CREATION_JOB,
        maintenanceOn,
      });
      logger.log(
        "info",
        `[AGENDA][WATCHER][SCHEDULED JOB][RECOVER][RUN][STARTUP][EVENT_ID: ${eventId}]`,
        {
          name: job.attrs?.name,
          attrs: { ...job.attrs },
          data: { ...(job.attrs?.data ?? {}) },
          time,
        },
      );
    }),
  );

  logger.log("info", `[AGENDA][WATCHER][CHECK][END]`);
};

const startAgenda = (dataSources: IContext["dataSources"]) =>
  agenda.on("ready", async () => {
    await agenda.start();
    const currentTime = new Date();

    const [pendingJobs] = await Promise.all([
      // Ignore legacy Job Watcher
      agenda.jobs({ name: { $ne: "job watcher" }, nextRunAt: { $lte: currentTime } }),
      agenda.cancel({ nextRunAt: null }),
    ]);
    const processedDefinitions = new Set<string>();

    if (pendingJobs.length) logger.info(`[AGENDA][CHECK] Found ${pendingJobs.length} pending jobs`);
    else logger.info("[AGENDA][CHECK] No pending jobs found");

    await Promise.all(
      pendingJobs.map(async job => {
        const definition = definitions[job.attrs.name];
        if (!definition)
          return logger.info(
            `[AGENDA][JOB][DEFINITION] Ignoring job ${job.attrs.name} as no definition was found for this`,
          );

        defineJob({
          dataSources,
          name: job.attrs.name,
          processedDefinitions,
        });
        logger.info(`[AGENDA][JOB][DEFINITION] Defined job ${job.attrs.name}`);

        if (!definition.interval) {
          await runJob({
            jobName: job.attrs.name,
            ...job.attrs.data,
          });
          logger.info(`[AGENDA][RECOVERY] Triggered job "${job.attrs.name}"`);
        }
      }),
    );

    Object.keys(definitions).forEach(name =>
      defineJob({ dataSources, name, processedDefinitions }),
    );

    await handleLegacyJobs();
  });

export default startAgenda;
