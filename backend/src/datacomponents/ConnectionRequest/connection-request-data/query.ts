import { GraphQLResolveInfo } from "graphql";
import type { ExecutionContext } from "graphql/execution/execute";
import { getAllConnectionRequests } from "~/services/connectionRequest/fetch";
import { IContext } from "~/types/common";

export const queryTypes = `#graphql
  type Query {
    listConnectionRequests(params: InputQueryParams): [ConnectionRequest]
  }
`;

export const listConnectionRequests = async (
  _: ExecutionContext["contextValue"],
  args: { params: { skip: number; limit: number } },
  { dataSources, user, req: { t: translate } }: IContext,
  ___: GraphQLResolveInfo,
) =>
  await getAllConnectionRequests({
    args: { input: {}, params: args.params, headers: {}, files: null, query: {} },
    dataSources,
    user,
    translate,
  });
