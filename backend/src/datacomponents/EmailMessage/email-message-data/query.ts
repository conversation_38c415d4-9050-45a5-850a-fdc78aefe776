import { GraphQLResolveInfo } from "graphql";
import type { ExecutionContext } from "graphql/execution/execute";
import { features, roles } from "~/directives";
import { getEmailMessage as _getEmailMessage } from "~/services/emailMessage/fetch";
import { IContext } from "~/types/common";

const { technician } = roles.is;
const { emails } = features.oemOwns;

export const queryTypes = `#graphql
  type Query {
    getEmailMessage(messageId: String!, emailAddressId: ID!): EmailMessageWithFileURL @${emails} @${technician}
  }
`;

export const getEmailMessage = async (
  _: ExecutionContext["contextValue"],
  args: { messageId: string; emailAddressId: string },
  { dataSources, user, req: { t: translate } }: IContext,
  ___: GraphQLResolveInfo,
) =>
  await _getEmailMessage({
    args: {
      input: { messageId: args.messageId, emailAddressId: args.emailAddressId },
      params: {},
      headers: {},
      files: null,
      query: {},
    },
    dataSources,
    user,
    translate,
  });
