import { Types } from "mongoose";
import type { ICustomerPortal } from "~/datamodels/CustomerPortal/interface";
import type { IContext } from "~/types/common";

export default `#graphql

type ProductAccess {
    allowCopying: Boolean
    documentation: Boolean
    parts: Boolean
    procedures: Boolean
    _3dModel: Boolean
}

type CustomerPortal {
    _id: ID
    assetAccess: AssetAccess
    contacts: [Contact]
    name: String
    connection: Customer
    oem: ID
    createdBy: User
    deleted: Boolean
    updatedBy: User
    productAccess: ProductAccess
}

type PaginatedCustomerPortals {
    totalCount: Int
    limit: Int
    isExistingContactLinkable: Boolean
    isConnectionAlreadyEstablished: Boolean
    skip: Int
    currentPage: Int
    customerPortals: [CustomerPortal]
}
`;

export const typeResolvers = {
  CustomerPortal: {
    createdBy: (
      { createdBy }: ICustomerPortal,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.User.loadOne(new Types.ObjectId(createdBy)),
    connection: (
      { connection }: ICustomerPortal,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.Customer.loadOne(connection),
    contacts: ({ _id }: ICustomerPortal, __: unknown, { dataSources }: IContext) =>
      dataSources.Contact.getManyByQuery({
        customerPortal: _id,
      }),
    updatedBy: (
      { updatedBy }: ICustomerPortal,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.User.loadOne(new Types.ObjectId(updatedBy)),
  },
};
