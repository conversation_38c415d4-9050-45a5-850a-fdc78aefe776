import { describe, afterEach, expect, test, vi, it, beforeEach } from "vitest";
import { Types } from "mongoose";
import { queryResolvers } from "./_query";
import { IUser } from "~/datamodels/User/interface";

const { Query } = queryResolvers;

const OEM_ID = new Types.ObjectId("6499a52b5dfe76648ff67a81");
const REPORT_ID = new Types.ObjectId("6499a52b5dfe76648ff67a93");

vi.mock("~/utils/analytics");
const user: IUser = {
  organization: OEM_ID,
  username: "User",
};

const dataSources = {
  AppConfig: {
    getOne: vi.fn(),
  },
  Report: {
    getMany: vi.fn(),
    getOne: vi.fn(),
    getById: vi.fn(),
    search: vi.fn(),
    totalCount: vi.fn(),
  },
  Machine: {
    getById: vi.fn(),
    getMany: vi.fn(),
    getOne: vi.fn(),
    countDocuments: vi.fn(),
    aggregate: vi.fn(),
  },
  Ticket: {
    getMany: vi.fn(),
    countDocuments: vi.fn(),
    aggregate: vi.fn(),
  },
  Customer: {
    getMany: vi.fn(),
    countDocuments: vi.fn(),
  },
  User: {
    getById: vi.fn(),
    getMany: vi.fn(),
  },
  Team: {
    getMany: vi.fn(),
  },
  Oem: {
    getOne: vi.fn(),
  },
  CustomAdditionalField: {
    getMany: vi.fn(),
  },
};

vi.mock("~/utils/validations", async () => ({
  validateCustomFields: vi.fn(),
}));

vi.mock("~/utils/teams", async () => ({
  belongsToUserTeams: vi.fn(),
  getValidTeams: vi.fn(),
}));

describe("Query Analytics", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("listAllOwnOemReports", () => {
    it("should return analytics reports for the user organization and teams", async () => {
      // Mock the query object
      const query = {
        limit: 10,
        oem: user.organization,
        skip: 0,
        sort: ["created_at:desc"],
      };
      const mockReports = [
        { id: "report1", name: "Report 1" },
        { id: "report2", name: "Report 2" },
      ];
      const mockResponse = {
        currentPage: 1,
        limit: 10,
        reports: mockReports.map(report => ({ ...report, data: [] })),
        skip: 0,
        totalCount: 2,
      };
      dataSources.Report.getMany = vi.fn().mockResolvedValue(mockReports);
      dataSources.Report.totalCount = vi.fn().mockResolvedValue(2);

      // Call the function and await the result
      const result = await Query.listAllOwnOemReports(
        null,
        { params: { skip: 0, limit: 10 } },
        { dataSources, user },
      );

      expect(dataSources.Report.getMany).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResponse);
    });

    it("should return analytics reports for the user organization and teams", async () => {
      const query = {
        limit: 10,
        oem: user.organization,
        skip: 0,
        sort: ["created_at:desc"],
      };

      const mockReports = [
        { id: "report1", chartType: "TABLE", entity: "Work Orders", inputs: { table: {} } },
        { id: "report2", chartType: "TABLE", entity: "Machine", inputs: { table: {} } },
      ];
      const mockResponse = {
        currentPage: 1,
        limit: 10,
        reports: mockReports.map(report => ({ ...report, data: [] })),
        skip: 0,
        totalCount: 2,
      };

      const mockUserOem = { paidFeatures: ["teams"] };

      const mockCustomFields = [{}];

      dataSources.Report.getMany.mockResolvedValue(mockReports);
      dataSources.Report.totalCount = vi.fn().mockResolvedValue(2);
      dataSources.Oem.getOne.mockResolvedValue(mockUserOem);
      dataSources.CustomAdditionalField.getMany.mockResolvedValue(mockCustomFields);

      const result = await Query.listAllOwnOemReports(
        null,
        { params: { skip: 0, limit: 10 } },
        { dataSources, user },
      );

      expect(dataSources.Report.getMany).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResponse);
    });
  });

  describe("getOwnOemReportByID", async () => {
    test("get Report by id", async () => {
      const mockOem = { Oem: "testOem" };
      const mockReport = { report: "Test report" };
      const mockCustomers = [{ facility: "Test Facility" }];
      const mockTeams = [{ Team: 1 }];
      const mockUsers = [{ User: 1 }];
      const mockCustomFields = [{}];

      await Query.getOwnOemReportByID(
        null,
        { _id: REPORT_ID },
        {
          dataSources,
          user,
        },
      );

      dataSources.Oem.getOne.mockResolvedValue(mockOem);
      dataSources.Report.getOne.mockResolvedValue(mockReport);
      dataSources.Customer.getMany.mockResolvedValue(mockCustomers);
      dataSources.Team.getMany.mockResolvedValue(mockTeams);
      dataSources.User.getMany.mockResolvedValue(mockUsers);
      dataSources.CustomAdditionalField.getMany.mockResolvedValue(mockCustomFields);

      const result = await Query.getOwnOemReportByID(null, { REPORT_ID }, { dataSources, user });

      expect(result).toBeDefined();
    });
  });
  describe("getAnalytics", () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    test("should return data with report input", async () => {
      const args = {
        input: {
          _id: "123",
          entity: "someEntity",
          xAxis: "xAxisValue",
          segment: "segmentValue",
          filters: "filtersValue",
        },
      };
      const report = {
        _id: "123",
        entity: "someEntity",
        xAxis: "xAxisValue",
        segment: "segmentValue",
        filters: "filtersValue",
      };

      dataSources.AppConfig.getOne.mockResolvedValue({ features: [] });

      const result = await Query.getOwnOemAnalytics(null, args, { dataSources, user });

      expect(dataSources.Report.getOne).toHaveBeenCalledWith({ oem: OEM_ID, _id: "123" });
      expect(result).toEqual({ data: [], inputTableOptions: [] });
    });

    test("should return null if no args are provided", async () => {
      const args = {
        input: {
          entity: "",
          xAxis: "",
        },
      };
      dataSources.AppConfig.getOne.mockResolvedValue({ features: [] });
      const result = await Query.getOwnOemAnalytics(null, args, { dataSources, user });
      expect(result).toEqual({
        data: [],
        inputTableOptions: [],
      });
    });
  });
});
