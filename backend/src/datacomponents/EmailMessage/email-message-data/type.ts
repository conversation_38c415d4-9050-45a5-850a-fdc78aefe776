export default `#graphql

type EmailAttachment {
    contentId: String
    contentType: String
    filename: String
    grantId: String
    id: String
    isInline: Boolean
    size: Int
}

type EmailMessage {
    _id: ID
    oem: ID
    id: String
    grantId: String
    starred: Boolean
    unread: Boolean
    folders: [String]
    subject: String
    threadId: String
    body: String
    snippet: String
    bcc: [EmailParticipant]
    cc: [EmailParticipant]
    attachments: [EmailAttachment]
    from: [EmailParticipant]
    replyTo: [EmailParticipant]
    to: [EmailParticipant]
    date: Int
}

type EmailMessageWithFileURL {
    message: EmailMessage
    fileUrl: JSON
}

`;
