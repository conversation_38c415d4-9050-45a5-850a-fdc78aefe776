import cors from "cors";
import APP from "./_app";
import JWT from "./_jwt";

const origins = process.env.ALLOWED_ORIGINS?.split(",") || [];

const corsOptions = {
  origin(origin, callback) {
    callback(null, origins);
  },
  exposedHeaders: [
    JWT.HEADER.TOKEN.NAME,
    JWT.HEADER.REFRESH_TOKEN.NAME,
    `${APP.PREFIX}${APP.NAMESPACE}${APP.VERSION_HEADER_SUFFIX}`,
  ],
};

export default () => cors(corsOptions);
