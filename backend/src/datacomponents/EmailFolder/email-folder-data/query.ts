import { GraphQLResolveInfo } from "graphql";
import type { ExecutionContext } from "graphql/execution/execute";
import { features, roles } from "~/directives";
import { getAllEmailFolders } from "~/services/emailFolder/fetch";
import { IContext } from "~/types/common";

const { staff } = roles.is;
const { emails } = features.oemOwns;

export const queryTypes = `#graphql
  type Query {
    listEmailFolders(params: InputQueryParams): PaginatedEmailFolders @${emails} @${staff}
  }
`;

export const listEmailFolders = async (
  _: ExecutionContext["contextValue"],
  args: { params: { skip: number; limit: number } },
  { dataSources, user, req: { t: translate } }: IContext,
  ___: GraphQLResolveInfo,
) =>
  await getAllEmailFolders({
    args: { input: {}, params: args.params, headers: {}, files: null, query: {} },
    dataSources,
    user,
    translate,
  });
