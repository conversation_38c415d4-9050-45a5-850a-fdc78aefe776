import to from "await-to-js";
import { Types } from "mongoose";
import InvalidInputError from "~/errors/InvalidInputError";
import type { ICustomer } from "~/datamodels/Customer/interface";
import type { IContact } from "~/datamodels/Contact/interface";
import ConflictError from "~/errors/ConflictError";
import { MONGODB_ERRORS } from "~/errors/constants";
import { ServiceParamsType } from "~/types/common";

export const createCustomerPortal = async ({
  args,
  dataSources,
  user,
}: ServiceParamsType<{
  connectionId: string;
}>) => {
  const { connectionId } = args.input || {};

  const connection: ICustomer = await dataSources.Customer.loadOne(
    new Types.ObjectId(connectionId),
  );

  if (!connection) throw new InvalidInputError("Invalid Connection Id");

  const alreadyExists = await dataSources.CustomerPortal.getOne({
    connection: connection._id,
  });

  if (alreadyExists) {
    throw new ConflictError("Customer Portal already exists");
  }

  const [creationError, customerPortal]: [any, IContact | undefined] = await to(
    dataSources.CustomerPortal.save({
      name: `${connection.name} Portal`,
      oem: user.organization,
      connection: connection._id,
      createdBy: user.id,
    }),
  );
  if ((creationError as any)?.code === MONGODB_ERRORS.DUPLICATE_KEY_ERROR_CODE) {
    throw new ConflictError("Customer Portal already exists");
  }

  return customerPortal;
};
