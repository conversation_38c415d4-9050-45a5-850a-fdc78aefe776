import { GraphQLResolveInfo } from "graphql";
import type { ExecutionContext } from "graphql/execution/execute";
import { features, roles } from "~/directives";
import {
  getAllEmailThreadsByFolder,
  getEmailThread as _getEmailThread,
  listEmailThreadsByTicket as _listEmailThreadsByTicket,
} from "~/services/emailThread/fetch";
import type { IContext } from "~/types/common";

const { staff, technician } = roles.is;
const { emails } = features.oemOwns;

export const queryTypes = `#graphql
  type Query {
    listEmailThreadsByFolder(params: InputQueryParams): PaginatedEmailThreads @${emails} @${staff}
    listEmailThreadsByTicket(ticketId: ID!, emailAddressId: ID!): [EmailThread] @${emails} @${technician}
    getEmailThread(threadId: String!, emailAddressId: ID!): EmailThreadWithTicket @${emails} @${technician}
  }
`;

export const listEmailThreadsByFolder = async (
  _: ExecutionContext["contextValue"],
  args: { params: { skip: number; limit: number } },
  { dataSources, user, req: { t: translate } }: IContext,
  ___: GraphQLResolveInfo,
) =>
  await getAllEmailThreadsByFolder({
    args: { input: {}, params: args.params, headers: {}, files: null, query: {} },
    dataSources,
    user,
    translate,
  });

export const getEmailThread = async (
  _: ExecutionContext["contextValue"],
  args: { threadId: string; emailAddressId: string },
  { dataSources, user, req: { t: translate } }: IContext,
  ___: GraphQLResolveInfo,
) =>
  await _getEmailThread({
    args: { input: args, params: {}, headers: {}, files: null, query: {} },
    dataSources,
    user,
    translate,
  });

export const listEmailThreadsByTicket = async (
  _: ExecutionContext["contextValue"],
  args: { ticketId: string; emailAddressId: string },
  { dataSources, user, req: { t: translate } }: IContext,
  ___: GraphQLResolveInfo,
) =>
  await _listEmailThreadsByTicket({
    args: { input: args, params: {}, headers: {}, files: null, query: {} },
    dataSources,
    user,
    translate,
  });
