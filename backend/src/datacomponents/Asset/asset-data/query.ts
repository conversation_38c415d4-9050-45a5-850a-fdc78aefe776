import { ExecutionContext } from "graphql/execution/execute";
import { roles } from "~/directives";
import { InputQueryParams } from "~/graphql/types";
import {
  getAllAssets,
  getAsset,
  getCustomerAssetById,
  getCustomerAssetByUuid,
  listCustomerAssets,
  listAssetTicketHistoryById,
  listAssetsMissingSerialNumbers,
  listKanbanAssets,
  getAssetsWithLinkedTemplates,
  getLinkedOrganizationsForSharedAssets,
  getAssetBoxFolderAccessToken as getBoxFolderAccessTokenService,
  getRequestableAssets,
  get3DModels,
} from "~/services/asset/fetch";
import { IContext } from "~/types/common";

export const queryTypes = `#graphql
  type Query {
    getOwnCustomerAssetById(id: ID!): Asset @${roles.is.gmu}
    getOwnCustomerAssetByUuid(uuid: ID!): Asset
    getOwnOemAssetById(id: ID!, isSharedAsset: Boolean): Asset @${roles.is.technician}
    listAllOwnOemAssets(params: InputQueryParams): PaginatedAssets @${roles.is.technician}
    listOwnCustomerAssets(params: InputQueryParams): PaginatedAssets @${roles.is.gmu}
    listOwnOemAssetTicketHistoryById(id: ID!): [Ticket] @${roles.is.gmu}
    listOwnOemAssetsMissingSerialNumbers(serialNumbers: [String!]!): [String] @${roles.is.technician}
    listOwnOemKanbanAssets(params: InputQueryParams): PaginatedKanbanAssets @${roles.is.technician}
    listAssetsWithLinkedTemplates(serialNumbers: [String]): [String!]! @${roles.is.oem}
    listSharedAssetsOrganizations(params: InputQueryParams): PaginatedSharedAssetsOem @${roles.is.technician}
    getBoxFolderAccessToken(assetId: ID!): String @${roles.is.technician}
    listRequestableAssets(params: InputQueryParams): PaginatedAssets @${roles.is.technician}
    get3DModels(params: InputQueryParams): PaginatedModels @${roles.is.technician}
  }
`;

export const queryResolvers = {
  Query: {
    getOwnCustomerAssetById: async (
      _: ExecutionContext["contextValue"],
      args: { id: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await getCustomerAssetById({
        args: { input: args, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    getOwnCustomerAssetByUuid: async (
      _: ExecutionContext["contextValue"],
      args: { uuid: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await getCustomerAssetByUuid({
        args: { input: args, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    getOwnOemAssetById: async (
      _: ExecutionContext["contextValue"],
      args: { id: string; isSharedAsset: boolean },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      getAsset({
        args: { input: args, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    listAllOwnOemAssets: async (
      _: ExecutionContext["contextValue"],
      args: { params: InputQueryParams },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      getAllAssets({
        args: {
          input: {},
          params: args.params,
          files: null,
          headers: {},
          query: {},
        },
        dataSources,
        user,
        translate,
      }),

    listOwnCustomerAssets: async (
      _: ExecutionContext["contextValue"],
      args: { params: InputQueryParams },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await listCustomerAssets({
        args: {
          input: {},
          params: args.params,
          files: null,
          headers: {},
          query: {},
        },
        dataSources,
        user,
        translate,
      }),

    listOwnOemAssetTicketHistoryById: async (
      _: ExecutionContext["contextValue"],
      args: { id: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await listAssetTicketHistoryById({
        args: { input: args, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    listOwnOemAssetsMissingSerialNumbers: async (
      _: ExecutionContext["contextValue"],
      args: { serialNumbers: string[] },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await listAssetsMissingSerialNumbers({
        args: { input: args, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    listOwnOemKanbanAssets: async (
      _: ExecutionContext["contextValue"],
      args: { params: InputQueryParams },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await listKanbanAssets({
        args: {
          input: {},
          params: args.params,
          files: null,
          headers: {},
          query: {},
        },
        dataSources,
        user,
        translate,
      }),

    listAssetsWithLinkedTemplates: async (
      _: ExecutionContext["contextValue"],
      args: { serialNumbers: string[] },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await getAssetsWithLinkedTemplates({
        args: {
          input: { serialNumbers: args.serialNumbers },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),

    listSharedAssetsOrganizations: async (
      _: ExecutionContext["contextValue"],
      args: { params: InputQueryParams },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await getLinkedOrganizationsForSharedAssets({
        // @ts-ignore
        args,
        dataSources,
        user,
        translate,
      }),

    getBoxFolderAccessToken: async (
      _: ExecutionContext["contextValue"],
      args: { assetId: string; connectionId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await getBoxFolderAccessTokenService({
        args: { input: args, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    listRequestableAssets: async (
      _: ExecutionContext["contextValue"],
      args: { params: InputQueryParams },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await getRequestableAssets({
        // @ts-ignore
        args,
        dataSources,
        user,
        translate,
      }),
    get3DModels: async (
      _: ExecutionContext["contextValue"],
      args: { params: InputQueryParams },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await get3DModels({
        // @ts-ignore
        args,
        dataSources,
        user,
        translate,
      }),
  },
};
