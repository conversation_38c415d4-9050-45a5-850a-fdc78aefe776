export const PERMISSIONS = {
  CREATE_WORK_ORDER: "createWorkOrder",
  ASSIGN_WORK_ORDER: "assignWorkOrder",
  ASSIGN_WORK_ORDER_ACROSS_TEAMS: "assignWorkOrderAcrossTeams",
  ASSIGN_WORK_ORDER_WITHIN_TEAM: "assignWorkOrderWithinTeam",
  ASSIGN_WORK_ORDER_TO_GLOBAL_USERS: "assignWorkOrderToGlobalUsers",
  UPDATE_WORK_ORDER_CUSTOM_FIELDS: "editWorkOrderCustomFields",
  UPDATE_ASSET_CUSTOM_FIELDS: "editAsset<PERSON>ustomFields",
  UPDATE_CONNECTIONS_CUSTOM_FIELDS: "editConnectionsCustomFields",
};

export const PERMISSIONS_BY_ROLES = {
  OWNER: Object.values(PERMISSIONS),
  OEM: Object.values(PERMISSIONS),
  STAFF: [
    PERMISSIONS.CREATE_WORK_ORDER,
    PERMISSIONS.ASSIGN_WORK_ORDER,
    PERMISSIONS.ASSIGN_WORK_ORDER_WITHIN_TEAM,
    PERMISSIONS.ASSIGN_WORK_ORDER_TO_GLOBAL_USERS,
    PERMISSIONS.UPDATE_CONNECTIONS_CUSTOM_FIELDS,
    PERMISSIONS.UPDATE_WORK_ORDER_CUSTOM_FIELDS,
    PERMISSIONS.UPDATE_ASSET_CUSTOM_FIELDS,
  ],
  TECHNICIAN: [
    PERMISSIONS.CREATE_WORK_ORDER,
    PERMISSIONS.UPDATE_WORK_ORDER_CUSTOM_FIELDS,
    PERMISSIONS.ASSIGN_WORK_ORDER,
  ],
};
