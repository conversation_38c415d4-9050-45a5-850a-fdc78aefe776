import { v4 as uuidv4 } from "uuid";
import { Job } from "agenda";
import { simpleParser } from "mailparser";
import { readFile } from "fs/promises";
import textract from "textract";
import fs, { unlinkSync as deleteLocalFile } from "fs";
import mongoose from "mongoose";
import { NodeHtmlMarkdown } from "node-html-markdown";
import { PdfCounter } from "page-count";
import type { OCRPageObject, OCRResponse } from "@mistralai/mistralai/models/components";
import removeMd from "remove-markdown";
import { MarkdownTextSplitter } from "langchain/text_splitter";
import type { IContext, ServiceParamsType } from "~/types/common";
import type { IDocument } from "~/datamodels/Document/interface";
import logger from "~/utils/logger";
import { serverEnvironmentTag } from "~/environment";
import { DOCUMENT_STATUS, IMAGE_FILE_TYPES } from "~/constants/document";
import {
  GENERATE_DOCUMENT_AUTHORS_JOB,
  GENERATE_DOCUMENT_CUSTOM_FIELDS_JOB,
  GENERATE_DOCUMENT_DATE_JOB,
  GENERATE_DOCUMENT_EMBEDDING_JOB,
  GENERATE_DOCUMENT_TITLE_JOB,
  OCR_DOCUMENT_JOB,
  PARSE_DOCUMENT_JOB,
} from "~/agenda/constants";
import runJob from "~/agenda/run";
import type { IDocumentChunk } from "~/datamodels/DocumentChunk/interface";
import { POSTGRES_VECTOR_SEARCH_DB_TABLE } from "~/datasources/api/postgres/constants";
import { getOemChannelId, getOemUserChannelId } from "~/utils";
import { NOTIFICATION_IDENTIFIERS } from "~/constants/notification-identifiers";
import { IOem } from "~/datamodels/Oem/interface";
import CustomFieldTypes from "$/settings/enums/customAdditionalField/_type.json";
import FieldTypes from "$/settings/enums/customAdditionalField/_field-type.json";
import { getEnums } from "~/utils/_get-enums";
import { ICustomAdditionalField } from "~/datamodels/CustomAdditionalField/interface";
import { AiAssistantDefaultConfigurationValues } from "~/datamodels/Oem/constants";
import { IDocumentPage } from "~/datamodels/DocumentPage/interface";
import { uploadToS3 } from "~/datasources/db/_utils/_s3";
import type { IAppConfig } from "~/datamodels/AppConfig/interface";
import { PROMPT_FEATURES } from "~/constants/prompts";
import { chunkMarkdown, splitMarkdownWithContext } from "~/services/document/markdownSplitter";
import {
  AnswerSchema,
  AuthorSchema,
  DocumentDateSchema,
  DocumentMetaSchema,
  DocumentTitleSchema,
  HFAnswerSchema,
  MultiSelectCustomFieldSchema,
  SingleSelectCustomFieldSchema,
} from "~/schemas";
import type { SummarizeSearchResults } from "~/services/document/types";
import { timeLogger } from "~/utils/logger/TimeLogger";
import APP_FEATURES from "$/settings/app-features.json";

const appFeaturesEnum = getEnums(APP_FEATURES, "reference");
const customFieldTypes = getEnums(CustomFieldTypes, "reference");
const fieldTypes = getEnums(FieldTypes, "reference");

const isOCRDoc = (document: IDocument): boolean => {
  const fileType = document.title.split(".").at(-1)!.toLowerCase();
  return fileType === "pdf" || IMAGE_FILE_TYPES.includes(fileType);
};

const extractTextFromFile = (filePath: string): Promise<string> =>
  new Promise((resolve, reject) => {
    textract.fromFileWithPath(filePath, (error, text: string) => {
      if (error) {
        reject(error);
      } else {
        resolve(text);
      }
    });
  });

export const extractText = async (filePath: string) => {
  try {
    const extractedText: string = await extractTextFromFile(filePath);
    return extractedText;
  } catch (error) {
    console.log(error);
  }
  return "";
};

const processOcrPages = async (
  dataSources: IContext["dataSources"],
  documentId: string,
  pages: OCRPageObject[],
): Promise<{
  processedPages: string[];
  s3Urls: string[];
  globalImageMap: { url: string; summary: string }[];
}> => {
  if (!pages?.length) return { processedPages: [], s3Urls: [], globalImageMap: [] };

  const processedPages: string[] = [];
  const s3Urls: string[] = [];

  const appConfig: IAppConfig = await dataSources.AppConfig.getOne();

  const prompt = appConfig?.prompts?.find(
    featurePrompt => featurePrompt.featureKey === PROMPT_FEATURES.IMAGE_DESCRIPTION_GENERATION,
  )?.value!;
  const globalImageMap = [];

  for (const page of pages) {
    const { markdown, images } = page;
    const imageMap: Record<string, { url: string; summary?: string }> = {};

    // Upload images and replace IDs
    for (const image of images) {
      const { id, imageBase64 } = image;
      const matches = imageBase64!.match(/^data:(image\/\w+);base64,(.+)$/);
      const mimeType = matches![1];
      const extension = mimeType.split("/")[1];
      const buffer = Buffer.from(matches![2], "base64");
      const filename = `${uuidv4()}.${extension}`;

      const s3ImageUrl = await uploadToS3({
        bufferData: buffer,
        uploadPath: `results/${documentId}/${filename}`,
      });
      s3Urls.push(s3ImageUrl);

      let summary = "";
      try {
        summary = await dataSources.MistralAi.generateResponse([
          {
            type: "text",
            text: prompt,
          },
          {
            type: "image_url",
            imageUrl: { url: imageBase64! },
          },
        ]);
      } catch (err) {
        console.warn(`Image summary failed for ${id}`, err);
      }

      imageMap[id] = { url: s3ImageUrl, summary };
      globalImageMap.push({ url: s3ImageUrl, summary });
    }

    const rewrittenMarkdown = markdown.replace(/!\[(.*?)\]\((.*?)\)/g, (_, alt, filename) => {
      const imageEntry = imageMap[filename];
      if (!imageEntry?.url) return "";

      const imageMarkdown = `![${alt}](${imageEntry.url})`;

      return imageMarkdown;
    });

    processedPages.push(rewrittenMarkdown);
  }

  return { processedPages, s3Urls, globalImageMap };
};

export const indexDocuments = async ({
  args,
  dataSources,
  user,
}: ServiceParamsType<{
  documentsToIndex: string[];
}>) => {
  const { documentsToIndex } = args.input;

  if (!documentsToIndex?.length) return "ok";

  const documents: IDocument[] = await dataSources.Document.getMany({
    _id: { $in: documentsToIndex },
    oem: user.organization,
    status: {
      $in: [
        DOCUMENT_STATUS.PENDING_INDEXING,
        DOCUMENT_STATUS.FAILED_INDEXING,
        DOCUMENT_STATUS.PENDING_APPROVAL,
      ],
    },
  });

  await Promise.all([
    dataSources.Document.updateManyByQuery(
      {
        _id: { $in: documents.map(doc => doc._id) },
        oem: user.organization,
      },
      {
        status: DOCUMENT_STATUS.INDEXING,
      },
    ),
    documents.map(document => {
      const fileType = document.title.split(".").at(-1)!.toLowerCase();
      return runJob({
        jobName: GENERATE_DOCUMENT_EMBEDDING_JOB,
        userId: user.id,
        oemId: user.organization,
        documentId: document._id,
        generateMeta: fileType === "pdf" || IMAGE_FILE_TYPES.includes(fileType),
      });
    }),
  ]);

  return "ok";
};

export const approveOcrDocuments = async ({
  args,
  dataSources,
  user,
  translate,
}: ServiceParamsType<{
  documentsToApprove: string[];
}>) => {
  const { documentsToApprove } = args.input;

  if (!documentsToApprove?.length) return "ok";

  await dataSources.Document.updateManyByQuery(
    {
      _id: { $in: documentsToApprove },
      oem: user.organization,
    },
    {
      status: DOCUMENT_STATUS.PENDING_INDEXING,
    },
  );

  await indexDocuments({
    args: {
      input: { documentsToIndex: documentsToApprove },
      files: null,
      headers: {},
      query: {},
      params: {},
    },
    user,
    dataSources,
    translate,
  });

  return "ok";
};

export const parseDocument = async (
  dataSources: IContext["dataSources"],
  {
    attrs,
  }: Job<{
    oemId: string;
    userId: string;
    documentId: mongoose.Types.ObjectId;
  }>,
) => {
  const { data } = attrs;
  if (!data) logger.info("[AGENDA][PARSE_DOCUMENT] Data is not present");

  const { documentId, oemId, userId } = data!;

  const document: IDocument = await dataSources.Document.loadOne(documentId);
  if (!document) {
    logger.info(`[AGENDA][PARSE_DOCUMENT] Document not found against ID: ${documentId}`);
    return;
  }

  await Promise.all([
    dataSources.Document.updateOne(
      { _id: document._id },
      { $set: { status: DOCUMENT_STATUS.INDEXING } },
    ),
    dataSources.PubnubApi.publishMessage({
      channel: getOemChannelId(oemId),
      message: {
        documentId: document._id.toString(),
        status: DOCUMENT_STATUS.INDEXING,
        text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_STATUS_UPDATE,
      },
    }),
  ]);

  const boxOemUserName = `${oemId!.toString()}${serverEnvironmentTag}`;

  try {
    const targetPath = `${__dirname}/${document.title}`;
    await dataSources.BoxApi.downloadFileById({
      boxOemUserName,
      fileID: document.boxDocumentId,
      targetPath,
    });

    const fileType = document.title.split(".").at(-1)!.toLowerCase();

    let content = "";

    if (fileType === "eml" || fileType === "msg") {
      const emlContent = await readFile(targetPath);
      const parsed = await simpleParser(emlContent);
      content = parsed.html?.toString();
    } else {
      try {
        content = await extractText(targetPath);
      } catch (error) {
        logger.error(
          `[AGENDA][PARSE_DOCUMENT] Error occurred while parsing document ${document._id}: ${error}`,
        );
      }
    }

    deleteLocalFile(targetPath);

    const markdownContent = NodeHtmlMarkdown.translate(content);

    const markdownSplitter = new MarkdownTextSplitter({
      chunkSize: 2048,
      chunkOverlap: 512,
    });

    const documentChunks = (await markdownSplitter.splitText(markdownContent)).filter(Boolean);

    const documentPage = await dataSources.DocumentPage.save({
      content: markdownContent,
      documentId,
      oem: oemId,
      pageIndex: 1,
    });

    await dataSources.DocumentChunk.insertMany(
      documentChunks.map((text, index) => ({
        documentId: document._id,
        chunkIndex: index,
        content: text,
        documentPageId: documentPage._id,
        oem: oemId,
        pageNumber: 1,
        imageSummaries: [],
      })),
    );

    await Promise.all([
      runJob({
        jobName: GENERATE_DOCUMENT_TITLE_JOB,
        userId,
        oemId,
        documentId: document._id,
      }),
      runJob({
        jobName: GENERATE_DOCUMENT_EMBEDDING_JOB,
        userId,
        oemId,
        documentId: document._id,
        generateMeta: false,
      }),
      runJob({
        jobName: GENERATE_DOCUMENT_AUTHORS_JOB,
        userId,
        oemId,
        documentId: document._id,
      }),
      runJob({
        jobName: GENERATE_DOCUMENT_CUSTOM_FIELDS_JOB,
        userId,
        oemId,
        documentId: document._id,
      }),
      runJob({
        jobName: GENERATE_DOCUMENT_DATE_JOB,
        userId,
        oemId,
        documentId: document._id,
      }),
    ]);
  } catch (err) {
    logger.error(
      `[AGENDA][PARSE_DOCUMENT] Error occurred while parsing document ${document._id}: ${err}`,
    );
    // @ts-ignore
    logger.error(err?.stack);
    await Promise.all([
      dataSources.Document.updateOne(
        { _id: document._id },
        { $set: { status: DOCUMENT_STATUS.FAILED_INDEXING } },
      ),
      dataSources.PubnubApi.publishMessage({
        channel: getOemChannelId(oemId),
        message: {
          documentId: document._id.toString(),
          status: DOCUMENT_STATUS.FAILED_INDEXING,
          text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_STATUS_UPDATE,
        },
      }),
    ]);
  }
};

export const ocrDocument = async (
  dataSources: IContext["dataSources"],
  {
    attrs,
  }: Job<{
    userId: string;
    oemId: string;
    documentId: mongoose.Types.ObjectId;
  }>,
) => {
  const { data } = attrs;
  if (!data) logger.info("[AGENDA][OCR_DOCUMENT] Data is not present");

  const { documentId, oemId } = data!;

  const [document, oem]: [IDocument, IOem] = await Promise.all([
    dataSources.Document.loadOne(documentId),
    dataSources.Oem.loadOne(new mongoose.Types.ObjectId(oemId)),
  ]);
  if (!document) {
    logger.info(`[AGENDA][OCR_DOCUMENT] Document not found against ID: ${documentId}`);
    return;
  }
  if (
    [
      DOCUMENT_STATUS.SCANNING,
      DOCUMENT_STATUS.INDEXING,
      DOCUMENT_STATUS.INDEXED,
      DOCUMENT_STATUS.FAILED_INDEXING,
      DOCUMENT_STATUS.PENDING_INDEXING,
    ].includes(document.status) ||
    ![DOCUMENT_STATUS.PENDING_SCANNING, DOCUMENT_STATUS.FAILED_SCANNING].includes(document.status)
  ) {
    logger.info(`[AGENDA][OCR_DOCUMENT] Document is already being scanned`);
    return;
  }
  if (!oem) {
    logger.info(`[AGENDA][OCR_DOCUMENT] Oem not found against ID: ${oemId}`);
    return;
  }

  const markdownSplitter = new MarkdownTextSplitter({
    chunkSize: 2048,
    chunkOverlap: 512,
  });

  await Promise.all([
    dataSources.Document.updateOne(
      { _id: document._id },
      { $set: { status: DOCUMENT_STATUS.SCANNING } },
    ),
    dataSources.PubnubApi.publishMessage({
      channel: getOemChannelId(oemId),
      message: {
        documentId: document._id.toString(),
        status: DOCUMENT_STATUS.SCANNING,
        text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_STATUS_UPDATE,
      },
    }),
  ]);

  const boxOemUserName = `${oemId!.toString()}${serverEnvironmentTag}`;
  let documentAnalysisSuccess = false;
  let totalPages: number = 1;

  try {
    const targetPath = `${__dirname}/${document.title}`;
    await dataSources.BoxApi.downloadFileById({
      boxOemUserName,
      fileID: document.boxDocumentId,
      targetPath,
    });

    const fileType = document.title.split(".").at(-1)!.toLowerCase();
    const dataBuffer = fs.readFileSync(targetPath);

    const fileUrl = await dataSources.BoxApi.getFileDownloadURL({
      boxOemUserName,
      fileID: document.boxDocumentId,
    });

    if (fileType === "pdf") {
      totalPages = await PdfCounter.count(dataBuffer);
    }
    deleteLocalFile(targetPath);

    if (
      (oem.aiAssistantConfiguration.consumedOcrScans || 0) + totalPages >
      (oem.aiAssistantConfiguration.allowedOcrScans ??
        AiAssistantDefaultConfigurationValues.allowedOcrScans)
    ) {
      await Promise.all([
        dataSources.Document.updateOne(
          { _id: document._id },
          { $set: { status: DOCUMENT_STATUS.FAILED_SCANNING } },
        ),
        dataSources.PubnubApi.publishMessage({
          channel: getOemChannelId(oemId),
          message: {
            documentId: document._id.toString(),
            status: DOCUMENT_STATUS.FAILED_SCANNING,
            text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_SCAN_LIMIT_REACHED,
            documentTitle: document.title,
          },
        }),
      ]);
      return;
    }

    const [ocrResponse, documentMetaResponse]: [OCRResponse, OCRResponse] = await Promise.all([
      dataSources.MistralAi.ocrDocument(fileUrl, fileType === "pdf"),
      dataSources.MistralAi.ocrDocument(fileUrl, fileType === "pdf", DocumentMetaSchema),
    ]);

    try {
      const documentMetadata: {
        title: string;
        date: string;
        authors: { name: string; confidence: string }[];
      } = JSON.parse(documentMetaResponse.documentAnnotation!);

      if (
        documentMetadata &&
        (documentMetadata.title || documentMetadata.date || documentMetadata.authors)
      ) {
        await dataSources.Document.updateOne(
          { _id: document._id },
          {
            $set: {
              aiGeneratedTitle: documentMetadata.title || document.aiGeneratedTitle,
              date: documentMetadata.date || document.date,
              authors: documentMetadata.authors || document.authors,
            },
          },
        );
        await dataSources.PubnubApi.publishMessage({
          channel: getOemChannelId(oemId),
          message: {
            documentId: documentId.toString(),
            text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_META_UPDATE,
          },
        });
      }
    } catch (e) {
      console.log(e);
    }

    documentAnalysisSuccess = true;

    await dataSources.Oem.updateOne(
      { _id: oemId },
      {
        $inc: {
          "aiAssistantConfiguration.consumedOcrScans": totalPages,
        },
      },
    );

    const pdfTextChunks: {
      chunkIndex: number;
      documentPageId: IDocumentPage["_id"];
      pageNumber: number;
      text: string;
      imageSummaries: string[];
    }[] = [];
    let textChunks: {
      content: string;
      documentPageId: IDocumentPage["_id"];
      imageSummaries: string[];
    }[] = [];

    const { processedPages, s3Urls, globalImageMap } = await processOcrPages(
      dataSources,
      document._id.toString(),
      ocrResponse.pages || [],
    );

    await dataSources.Document.updateOne(
      {
        _id: document._id,
      },
      {
        $set: {
          s3Urls,
        },
      },
    );

    if (fileType === "pdf") {
      await dataSources.DocumentPage.insertMany(
        processedPages?.map((page, index) => ({
          content: page,
          documentId,
          oem: oemId,
          pageIndex: index + 1,
        })),
      );

      const documentPages: IDocumentPage[] = await dataSources.DocumentPage.getManyByQuery({
        documentId,
        oem: oemId,
      });

      let chunkIndex = 0;

      for (let i = 0; i < documentPages.length; i += 1) {
        const documentPage = documentPages[i];
        let pageTextChunks = null;
        try {
          pageTextChunks = await markdownSplitter.splitText(documentPage.content);
        } catch (err) {
          logger.error("Error chunking document page", err);
          pageTextChunks = splitMarkdownWithContext(documentPage.content);
        }

        pdfTextChunks.push(
          // eslint-disable-next-line no-loop-func
          ...pageTextChunks.map(textChunk => {
            const imageSummaries: string[] = [];
            globalImageMap.forEach(imageDetails => {
              if (textChunk.includes(imageDetails.url)) {
                imageSummaries.push(imageDetails.summary);
              }
            });
            const pageChunk = {
              chunkIndex,
              imageSummaries,
              pageNumber: documentPage.pageIndex,
              documentPageId: documentPage._id,
              // @ts-ignore
              text: textChunk,
            };
            chunkIndex += 1;
            return pageChunk;
          }),
        );
      }
    } else {
      const documentPage = await dataSources.DocumentPage.save({
        content: processedPages[0],
        documentId,
        pageIndex: 1,
        oem: oemId,
      });
      const imageSummaries: string[] = [];
      try {
        textChunks = chunkMarkdown(processedPages[0]).map(chunk => {
          globalImageMap.forEach(imageDetails => {
            if (chunk.includes(imageDetails.url)) {
              imageSummaries.push(imageDetails.summary);
            }
          });
          return {
            content: chunk,
            documentPageId: documentPage._id,
            imageSummaries,
          };
        });
      } catch (err) {
        logger.error("Error chunking document page", err);
        textChunks = splitMarkdownWithContext(processedPages[0]).map(chunk => {
          globalImageMap.forEach(imageDetails => {
            if (chunk.includes(imageDetails.url)) {
              imageSummaries.push(imageDetails.summary);
            }
          });
          return {
            content: chunk,
            documentPageId: documentPage._id,
            imageSummaries,
          };
        });
      }
    }

    await dataSources.DocumentChunk.insertMany(
      fileType === "pdf"
        ? pdfTextChunks
            .map(({ chunkIndex, documentPageId, pageNumber, text, imageSummaries }) =>
              text
                ? {
                    chunkIndex,
                    content: text,
                    documentId: document._id,
                    documentPageId,
                    oem: oemId,
                    pageNumber,
                    imageSummaries,
                  }
                : false,
            )
            .filter(Boolean)
        : textChunks
            .map((textChunk, index) =>
              textChunk && textChunk.content
                ? {
                    documentId: document._id,
                    chunkIndex: index,
                    content: textChunk.content,
                    documentPageId: textChunk.documentPageId,
                    oem: oemId,
                    pageNumber: 1,
                    imageSummaries: textChunk.imageSummaries,
                  }
                : false,
            )
            .filter(Boolean),
    );

    if (oem.aiAssistantConfiguration.autoApproveOCR) {
      await approveOcrDocuments({
        args: {
          input: {
            documentsToApprove: [document._id.toString()],
          },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        // @ts-ignore
        user: {
          organization: new mongoose.Types.ObjectId(oemId),
        },
        dataSources,
      });
    } else {
      await Promise.all([
        dataSources.Document.updateOne(
          { _id: document._id },
          {
            ocrGeneratedContent: processedPages.map(page => page).join("\n\n"),
            status: DOCUMENT_STATUS.PENDING_APPROVAL,
          },
        ),
        dataSources.PubnubApi.publishMessage({
          channel: getOemChannelId(oemId),
          message: {
            documentId: document._id.toString(),
            status: DOCUMENT_STATUS.PENDING_APPROVAL,
            text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_STATUS_UPDATE,
          },
        }),
      ]);
    }
  } catch (err) {
    logger.error(
      `[AGENDA][OCR_DOCUMENT] Error occurred while OCR'ing document ${document._id}: ${err}`,
    );
    // @ts-ignore
    logger.error(err?.stack);
    await Promise.all([
      dataSources.Document.updateOne(
        { _id: document._id },
        { $set: { status: DOCUMENT_STATUS.FAILED_SCANNING } },
      ),
      ...(!documentAnalysisSuccess
        ? [
            dataSources.Oem.updateOne(
              { _id: oemId },
              {
                $inc: {
                  "aiAssistantConfiguration.consumedOcrScans": -1 * totalPages,
                },
              },
            ),
          ]
        : []),
      dataSources.PubnubApi.publishMessage({
        channel: getOemChannelId(oemId),
        message: {
          documentId: document._id.toString(),
          status: DOCUMENT_STATUS.FAILED_SCANNING,
          text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_STATUS_UPDATE,
        },
      }),
    ]);
  }
};

export const formatDocumentContentForModel = (markdownString: string) => {
  // Remove images
  const imageStrippedMarkdown = markdownString
    .replaceAll(/!\[.*?\]\(.*?\)/g, "")
    .replaceAll(/img-[0-9].jpeg/g, "");

  // Replace line breaks with whitespace
  const lineBreakStrippedMarkdown = imageStrippedMarkdown.replaceAll(/\n/g, " ");

  // Remove markdown
  const extractedContent = removeMd(lineBreakStrippedMarkdown);

  return extractedContent;
};

export const generateDocumentTitle = async (
  dataSources: IContext["dataSources"],
  {
    attrs,
  }: Job<{
    userId: string;
    oemId: string;
    documentId: mongoose.Types.ObjectId;
  }>,
) => {
  const { data } = attrs;
  if (!data) logger.info("[AGENDA][GENERATE_DOCUMENT_TITLE] Data is not present");

  const { documentId, oemId } = data!;

  const [document, oem, appConfig]: [IDocument, IOem, IAppConfig] = await Promise.all([
    dataSources.Document.loadOne(documentId),
    dataSources.Oem.loadOne(new mongoose.Types.ObjectId(oemId)),
    dataSources.AppConfig.getOne(),
  ]);
  if (!document) {
    logger.info(`[AGENDA][GENERATE_DOCUMENT_TITLE] Document not found against ID: ${documentId}`);
    return;
  }
  if (!oem) {
    logger.info(`[AGENDA][GENERATE_DOCUMENT_TITLE] Oem not found against ID: ${oemId}`);
    return;
  }

  const prompt = appConfig?.prompts?.find(
    featurePrompt => featurePrompt.featureKey === PROMPT_FEATURES.DOCUMENT_TITLE_GENERATION,
  )?.value!;

  try {
    const boxOemUserName = `${oemId!.toString()}${serverEnvironmentTag}`;

    const fileUrl = await dataSources.BoxApi.getFileDownloadURL({
      boxOemUserName,
      fileID: document.boxDocumentId,
    });
    let allContent = "";

    const isOCRDocument = isOCRDoc(document);

    if (!isOCRDocument) {
      const allPages: IDocumentPage[] = await dataSources.DocumentPage.getMany({
        documentId,
        oem: oemId,
        sort: ["pageNumber:asc"],
      });

      allPages.forEach(page => {
        allContent += page.content;
      });
    }

    const { parsed } = await dataSources.MistralAi.generateFormattedResponse(
      isOCRDocument
        ? [
            {
              type: "text",
              text: prompt,
            },
            {
              type: "document_url",
              documentUrl: fileUrl,
            },
          ]
        : `
        <context>
        ${dataSources.MistralAi.truncateByChars(allContent)}
        </context>

        ${prompt}
        `,
      DocumentTitleSchema,
    );

    const aiGeneratedTitle = parsed?.title;

    await dataSources.Document.updateOne(
      { _id: documentId },
      {
        $set: {
          aiGeneratedTitle,
        },
      },
    );

    await dataSources.PubnubApi.publishMessage({
      channel: getOemChannelId(oemId),
      message: {
        documentId: documentId.toString(),
        aiGeneratedTitle,
        text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_TITLE_UPDATE,
      },
    });
    return aiGeneratedTitle;
  } catch (err) {
    logger.error(
      `[AGENDA][GENERATE_DOCUMENT_TITLE] Error occurred while generating custom fields for document ${documentId}: ${err}`,
    );
    // @ts-ignore
    logger.error(err?.stack);
  }
};

export const generateDocumentEmbeddings = async (
  dataSources: IContext["dataSources"],
  {
    attrs,
  }: Job<{
    userId: string;
    oemId: string;
    documentId: mongoose.Types.ObjectId;
    chunkIndex: number;
    generateMeta: boolean;
  }>,
) => {
  const { data } = attrs;
  if (!data) logger.info("[AGENDA][EMBEDDING_DOCUMENT] Data is not present");

  const { documentId, oemId, generateMeta } = data!;

  const document: IDocument = await dataSources.Document.loadOne(documentId);
  if (!document) {
    logger.info(`[AGENDA][EMBEDDING_DOCUMENT] Document not found against ID: ${documentId}`);
    return;
  }
  const aiTitle = document.aiGeneratedTitle;

  try {
    const documentChunk: IDocumentChunk = await dataSources.DocumentChunk.getOne({
      documentId,
      "embedding.0": { $exists: false },
      chunkIndex: data!.chunkIndex || 0,
      indexed: false,
    });

    if (documentChunk) {
      const documentEmbedding = await dataSources.HuggingFace.generateEmbedding(
        `${aiTitle} ${removeMd(documentChunk.content)} ${documentChunk.imageSummaries.join(" ")}`,
      );
      await dataSources.VectorDB.query(
        `INSERT INTO ${POSTGRES_VECTOR_SEARCH_DB_TABLE} (oem, vector, document_id, chunk_index, content) VALUES ($1, $2::float[] ::vector, $3, $4, $5)`,
        [
          oemId?.toString(),
          documentEmbedding,
          document._id.toString(),
          documentChunk.chunkIndex,
          `${removeMd(documentChunk.content)} ${documentChunk.imageSummaries.join(" ")}`,
        ],
      );

      await dataSources.DocumentChunk.updateOne(
        { _id: documentChunk._id },
        {
          embedding: documentEmbedding,
          indexed: true,
        },
      );
    }

    const documentExists: IDocument = await dataSources.Document.loadOne(documentId);

    if (!documentExists) {
      const query = `
        DELETE FROM ${POSTGRES_VECTOR_SEARCH_DB_TABLE} 
        WHERE document_id = $1;
      `;

      await Promise.all([
        dataSources.VectorDB.query(query, [documentId]),
        dataSources.DocumentChunk.softDeleteMany({ documentId }),
      ]);
      return;
    }

    const areMoreChunksToProcess = await dataSources.DocumentChunk.getOne({
      documentId,
      chunkIndex: (data?.chunkIndex || 0) + 1,
    });

    if (areMoreChunksToProcess) {
      await runJob({
        jobName: GENERATE_DOCUMENT_EMBEDDING_JOB,
        userId: data!.userId,
        oemId,
        documentId,
        chunkIndex: (data?.chunkIndex || 0) + 1,
        generateMeta,
      });
      return;
    }

    await Promise.all([
      dataSources.Document.updateOne(
        { _id: documentId },
        { $set: { status: DOCUMENT_STATUS.INDEXED } },
      ),
      dataSources.PubnubApi.publishMessage({
        channel: getOemChannelId(oemId),
        message: {
          documentId: document._id.toString(),
          status: DOCUMENT_STATUS.INDEXED,
          text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_STATUS_UPDATE,
        },
      }),
    ]);

    await Promise.all([
      runJob({
        jobName: GENERATE_DOCUMENT_CUSTOM_FIELDS_JOB,
        oemId,
        documentId: document._id,
      }),
      ...(!document.authors?.length && generateMeta
        ? [
            runJob({
              jobName: GENERATE_DOCUMENT_AUTHORS_JOB,
              oemId,
              documentId: document._id,
            }),
          ]
        : []),
      ...(!document.aiGeneratedTitle && generateMeta
        ? [
            runJob({
              jobName: GENERATE_DOCUMENT_TITLE_JOB,
              oemId,
              documentId: document._id,
            }),
          ]
        : []),
      ...(!document.date && generateMeta
        ? [
            runJob({
              jobName: GENERATE_DOCUMENT_DATE_JOB,
              oemId,
              documentId: document._id,
            }),
          ]
        : []),
    ]);
  } catch (err) {
    logger.error(
      `[AGENDA][EMBEDDING_DOCUMENT] Error occurred while generating embeddings for document ${documentId}: ${err}`,
    );
    // @ts-ignore
    logger.error(err?.stack);
    await Promise.all([
      dataSources.Document.updateOne(
        { _id: document._id },
        { $set: { status: DOCUMENT_STATUS.FAILED_INDEXING } },
      ),
      dataSources.PubnubApi.publishMessage({
        channel: getOemChannelId(oemId),
        message: {
          documentId: document._id.toString(),
          status: DOCUMENT_STATUS.FAILED_INDEXING,
          text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_STATUS_UPDATE,
        },
      }),
    ]);
  }
};

export const generateSearchEmbeddings = async (
  dataSources: IContext["dataSources"],
  job: Job<{
    queryText: string;
  }>,
) => {
  const { data } = job.attrs;
  if (!data) logger.info("[AGENDA][GENERATE_SEARCH_EMBEDDING_JOB] Data is not present");

  const { queryText } = data!;
  timeLogger.time(`generateSearchEmbeddings: ${queryText}`);
  const isDataAvailable = await dataSources.Cache.getCachedData(queryText);
  if (isDataAvailable) {
    timeLogger.timeEnd(`generateSearchEmbeddings: ${queryText}`);
    return;
  }
  const embedding = await dataSources.HuggingFace.generateEmbedding(queryText);
  timeLogger.timeEnd(`generateSearchEmbeddings: ${queryText}`);

  await dataSources.Cache.setCacheData(queryText, embedding, 10 * 60 * 24);
  return embedding;
};

export const generateCustomFields = async (
  dataSources: IContext["dataSources"],
  {
    attrs,
  }: Job<{
    userId: string;
    oemId: string;
    documentId: mongoose.Types.ObjectId;
    customFieldIdsToGenerate: string[];
  }>,
) => {
  const { data } = attrs;
  if (!data) logger.info("[AGENDA][GENERATING_CUSTOM_FIELDS] Data is not present");

  const { documentId, oemId, customFieldIdsToGenerate = [] } = data!;

  const [document, oem, appConfig]: [IDocument, IOem, IAppConfig] = await Promise.all([
    dataSources.Document.loadOne(documentId),
    dataSources.Oem.loadOne(new mongoose.Types.ObjectId(oemId)),
    dataSources.AppConfig.getOne(),
  ]);
  if (!document) {
    logger.info(`[AGENDA][GENERATING_CUSTOM_FIELDS] Document not found against ID: ${documentId}`);
    return;
  }
  if (!oem) {
    logger.info(`[AGENDA][GENERATING_CUSTOM_FIELDS] Oem not found against ID: ${oemId}`);
    return;
  }

  const customFields: ICustomAdditionalField[] = await dataSources.CustomAdditionalField.getMany({
    _id: customFieldIdsToGenerate?.length
      ? { $in: customFieldIdsToGenerate }
      : { $nin: document.customFields.filter(field => !field.value).map(field => field.fieldId) },
    oem: oemId,
    type: customFieldTypes.knowledgeBase,
    enabled: true,
    deleted: false,
    "options.0": { $exists: true },
  });
  if (!customFields.length) return;

  const multiSelectPrompt = appConfig?.prompts?.find(
    featurePrompt =>
      featurePrompt.featureKey === PROMPT_FEATURES.MULTI_SELECT_CUSTOM_FIELD_GENERATION,
  )?.value!;

  const singleSelectPrompt = appConfig?.prompts?.find(
    featurePrompt =>
      featurePrompt.featureKey === PROMPT_FEATURES.SINGLE_SELECT_CUSTOM_FIELDS_GENERATION,
  )?.value!;

  const isOCRDocument = isOCRDoc(document);
  let allContent = "";

  if (!isOCRDocument) {
    const allPages: IDocumentPage[] = await dataSources.DocumentPage.getMany({
      documentId,
      oem: oemId,
      sort: ["pageNumber:asc"],
    });

    allPages.forEach(page => {
      allContent += page.content;
    });
  }

  try {
    const customFieldMap: { [key: string]: string | string[] } = {};
    const reasonReferenceMap: {
      [key: string]: {
        value: string;
        reason: string;
        reference: string;
      }[];
    } = {};

    const boxOemUserName = `${oemId!.toString()}${serverEnvironmentTag}`;

    const fileUrl = await dataSources.BoxApi.getFileDownloadURL({
      boxOemUserName,
      fileID: document.boxDocumentId,
    });

    for (let i = 0; i < customFields.length; i += 1) {
      const customField = customFields[i];

      const allPossibleValues = customField.options.map(option => option.value);

      const { parsed } = await dataSources.MistralAi.generateFormattedResponse(
        isOCRDocument
          ? [
              {
                type: "text",
                text: (customField.fieldType === fieldTypes.singleSelect
                  ? singleSelectPrompt
                  : multiSelectPrompt
                )?.replace(
                  "{classifiers}",
                  customField.options
                    .map(
                      (option, index) =>
                        ` Category ${index + 1}: ${option.value}
                      Category Criteria: ${
                        option.description
                          ? option.description
                          : `Only tag with this category if you find "${
                              option.value
                            }" written in the document. ${customField.description ?? ""}`
                      }
                `,
                    )
                    .join("\n\n"),
                ),
              },
              {
                type: "document_url",
                documentUrl: fileUrl,
              },
            ]
          : `
        <context>
        ${dataSources.MistralAi.truncateByChars(allContent)}
        </context>

        ${(customField.fieldType === fieldTypes.singleSelect
          ? singleSelectPrompt
          : multiSelectPrompt
        )?.replace(
          "{classifiers}",
          customField.options
            .map(
              (option, index) =>
                ` Category ${index + 1}: ${option.value}
                  Category Criteria: ${
                    option.description
                      ? option.description
                      : `Only tag with this category if you find "${
                          option.value
                        }" written in the document. ${customField.description ?? ""}`
                  }
            `,
            )
            .join("\n\n"),
        )}
        `,
        customField.fieldType === fieldTypes.singleSelect
          ? SingleSelectCustomFieldSchema
          : MultiSelectCustomFieldSchema,
      );

      if (parsed?.category && fieldTypes.singleSelect === customField.fieldType) {
        const parsedValue: string = parsed?.category;
        if (allPossibleValues?.includes(parsedValue)) {
          customFieldMap[customField._id.toString()] = parsedValue;
          reasonReferenceMap[customField._id.toString()] = [
            {
              value: parsedValue,
              reason: parsed?.reason,
              reference: parsed?.reference,
            },
          ];
        }
      } else if (parsed?.categories && fieldTypes.multiSelect === customField.fieldType) {
        const parsedValues = parsed?.categories;
        const validValues = parsedValues.filter(
          (category: { category: string; reference: string; reason: string }) =>
            allPossibleValues?.includes(category?.category),
        );
        if (validValues.length) {
          customFieldMap[customField._id.toString()] = validValues?.map(
            (value: { category: string; reference: string; reason: string }) => value.category,
          );
          reasonReferenceMap[customField._id.toString()] = validValues?.map(
            (value: { category: string; reference: string; reason: string }) => ({
              value: value.category,
              reason: value.reason,
              reference: value.reference,
            }),
          );
        }
      }
    }

    const documentExistingCustomFields = document.customFields.filter(
      customField => !customFieldIdsToGenerate.includes(customField.fieldId?.toString()),
    );

    await dataSources.Document.updateOne(
      { _id: documentId },
      {
        $set: {
          customFields: customFieldIdsToGenerate?.length
            ? [
                ...documentExistingCustomFields,
                ...customFields
                  .filter(field => customFieldMap[field._id.toString()])
                  .map(field => ({
                    fieldId: field._id,
                    ...(field.fieldType === fieldTypes.singleSelect
                      ? {
                          value: customFieldMap[field._id.toString()],
                          reasonReferenceMap: reasonReferenceMap[field._id.toString()],
                        }
                      : {
                          values: customFieldMap[field._id.toString()],
                          reasonReferenceMap: reasonReferenceMap[field._id.toString()],
                        }),
                  })),
              ]
            : customFields
                .filter(field => customFieldMap[field._id.toString()])
                .map(field => ({
                  fieldId: field._id,
                  ...(field.fieldType === fieldTypes.singleSelect
                    ? {
                        value: customFieldMap[field._id.toString()],
                        reasonReferenceMap: reasonReferenceMap[field._id.toString()],
                      }
                    : {
                        values: customFieldMap[field._id.toString()],
                        reasonReferenceMap: reasonReferenceMap[field._id.toString()],
                      }),
                })),
        },
      },
    );

    await dataSources.PubnubApi.publishMessage({
      channel: getOemChannelId(oemId),
      message: {
        documentId: documentId.toString(),
        customFieldMap,
        text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_CUSTOM_FIELDS_UPDATE,
      },
    });
  } catch (err) {
    logger.error(
      `[AGENDA][GENERATING_CUSTOM_FIELDS] Error occurred while generating custom fields for document ${documentId}: ${err}`,
    );
    // @ts-ignore
    logger.error(err?.stack);
  }
};

export const generateDocumentDate = async (
  dataSources: IContext["dataSources"],
  {
    attrs,
  }: Job<{
    userId: string;
    oemId: string;
    documentId: mongoose.Types.ObjectId;
  }>,
) => {
  const { data } = attrs;
  if (!data) logger.info("[AGENDA][GENERATE_DOCUMENT_DATE] Data is not present");

  const { documentId, oemId } = data!;

  const [document, oem, appConfig]: [IDocument, IOem, IAppConfig] = await Promise.all([
    dataSources.Document.loadOne(documentId),
    dataSources.Oem.loadOne(new mongoose.Types.ObjectId(oemId)),
    dataSources.AppConfig.getOne(),
  ]);
  if (!document) {
    logger.info(`[AGENDA][GENERATE_DOCUMENT_DATE] Document not found against ID: ${documentId}`);
    return;
  }
  if (!oem) {
    logger.info(`[AGENDA][GENERATE_DOCUMENT_DATE] Oem not found against ID: ${oemId}`);
    return;
  }

  const prompt = appConfig?.prompts?.find(
    featurePrompt => featurePrompt.featureKey === PROMPT_FEATURES.DOCUMENT_DATE_GENERATION,
  )?.value!;

  const isOCRDocument = isOCRDoc(document);
  let allContent = "";

  if (!isOCRDocument) {
    const allPages: IDocumentPage[] = await dataSources.DocumentPage.getMany({
      documentId,
      oem: oemId,
      sort: ["pageNumber:asc"],
    });

    allPages.forEach(page => {
      allContent += page.content;
    });
  }

  try {
    const boxOemUserName = `${oemId!.toString()}${serverEnvironmentTag}`;

    const fileUrl = await dataSources.BoxApi.getFileDownloadURL({
      boxOemUserName,
      fileID: document.boxDocumentId,
    });

    const { parsed } = await dataSources.MistralAi.generateFormattedResponse(
      isOCRDocument
        ? [
            {
              type: "text",
              text: prompt,
            },
            {
              type: "document_url",
              documentUrl: fileUrl,
            },
          ]
        : `
    <context>
    ${dataSources.MistralAi.truncateByChars(allContent)}
    </context>

    ${prompt}
    `,
      DocumentDateSchema,
    );

    const aiGeneratedDate = parsed?.date;

    await dataSources.Document.updateOne(
      { _id: documentId },
      {
        $set: {
          date: aiGeneratedDate,
        },
      },
    );

    await dataSources.PubnubApi.publishMessage({
      channel: getOemChannelId(oemId),
      message: {
        documentId: documentId.toString(),
        date: aiGeneratedDate,
        text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_DATE_UPDATE,
      },
    });
  } catch (err) {
    logger.error(
      `[AGENDA][GENERATE_DOCUMENT_DATE] Error occurred while generating custom fields for document ${documentId}: ${err}`,
    );
    // @ts-ignore
    logger.error(err?.stack);
  }
};

export const generateDocumentAuthor = async (
  dataSources: IContext["dataSources"],
  {
    attrs,
  }: Job<{
    userId: string;
    oemId: string;
    documentId: mongoose.Types.ObjectId;
  }>,
) => {
  const { data } = attrs;
  if (!data) logger.info("[AGENDA][GENERATE_DOCUMENT_DATE] Data is not present");

  const { documentId, oemId } = data!;

  const [document, oem, appConfig]: [IDocument, IOem, IAppConfig] = await Promise.all([
    dataSources.Document.loadOne(documentId),
    dataSources.Oem.loadOne(new mongoose.Types.ObjectId(oemId)),
    dataSources.AppConfig.getOne(),
  ]);
  if (!document) {
    logger.info(`[AGENDA][GENERATE_DOCUMENT_AUTHORS] Document not found against ID: ${documentId}`);
    return;
  }
  if (!oem) {
    logger.info(`[AGENDA][GENERATE_DOCUMENT_AUTHORS] Oem not found against ID: ${oemId}`);
    return;
  }

  const prompt = appConfig?.prompts?.find(
    featurePrompt => featurePrompt.featureKey === PROMPT_FEATURES.DOCUMENT_AUTHORS_GENERATION,
  )?.value!;

  try {
    const boxOemUserName = `${oemId!.toString()}${serverEnvironmentTag}`;

    const fileUrl = await dataSources.BoxApi.getFileDownloadURL({
      boxOemUserName,
      fileID: document.boxDocumentId,
    });

    const isOCRDocument = isOCRDoc(document);
    let allContent = "";

    if (!isOCRDocument) {
      const allPages: IDocumentPage[] = await dataSources.DocumentPage.getMany({
        documentId,
        oem: oemId,
        sort: ["pageNumber:asc"],
      });

      allPages.forEach(page => {
        allContent += page.content;
      });
    }

    const { parsed } = await dataSources.MistralAi.generateFormattedResponse(
      isOCRDocument
        ? [
            {
              type: "document_url",
              documentUrl: fileUrl,
            },
            {
              type: "text",
              text: prompt,
            },
          ]
        : `
      <context>
      ${dataSources.MistralAi.truncateByChars(allContent)}
      </context>
      
      ${prompt}
      `,
      AuthorSchema,
    );

    const authors = parsed?.authors;
    const editors = parsed?.editors;
    const contributors = parsed?.contributors;

    await dataSources.Document.updateOne(
      { _id: documentId },
      {
        $set: {
          authors,
          editors,
          contributors,
        },
      },
    );

    await dataSources.PubnubApi.publishMessage({
      channel: getOemChannelId(oemId),
      message: {
        documentId: documentId.toString(),
        text: NOTIFICATION_IDENTIFIERS.KNOWLEDGE_BASE_DOCUMENT_DATE_UPDATE,
      },
    });
  } catch (err) {
    logger.error(
      `[AGENDA][GENERATE_DOCUMENT_AUTHORS] Error occurred while generating authors for document ${documentId}: ${err}`,
    );
    // @ts-ignore
    logger.error(err?.stack);
  }
};

export const summarizeSearchResults = async (
  dataSources: IContext["dataSources"],
  query: string,
  results: Array<IDocumentChunk>,
  user: IContext["user"],
): Promise<SummarizeSearchResults> => {
  const [oem, appConfig]: [IOem, IAppConfig] = await Promise.all([
    dataSources.Oem.loadOne(user.organization),
    dataSources.AppConfig.getOne(),
  ]);
  const combinedText = results
    .map(
      doc =>
        `Chunk ID: ${doc._id?.toString()}\nChunk Content: ${doc.content}\n${doc.imageSummaries.join(
          "\n",
        )}`,
    )
    .join("\n\n");

  const prompt = appConfig?.prompts?.find(
    featurePrompt => featurePrompt.featureKey === PROMPT_FEATURES.ANSWER_GENERATION,
  )?.value;

  const { parsed } = await dataSources.MistralAi.generateFormattedResponse(
    `
      <context>
      ${combinedText}
      </context>
      <question>
      ${query}
      </question>
      ${
        oem.aiAssistantConfiguration.role ? `<role>${oem.aiAssistantConfiguration.role}</role>` : ""
      }
      ${prompt}
      **Answer:**
  `,
    AnswerSchema,
  );
  return parsed;
};

const generateDocumentResult = async ({
  query,
  document,
  results,
  dataSources,
  oemId,
  oem,
  appConfig,
  userId,
  searchResultJobId,
}: {
  query: string;
  document: IDocument;
  results: Array<IDocumentChunk>;
  dataSources: IContext["dataSources"];
  oemId: string;
  oem: IOem;
  appConfig: IAppConfig;
  userId: string;
  searchResultJobId: string;
}) => {
  const documentChunks = results.filter(
    result => result.documentId?.toString() === document._id?.toString(),
  );
  const isOCRDocument = isOCRDoc(document);
  const uniquePages = documentChunks.reduce((acc, result) => {
    const { pageNumber } = result;
    if (!acc.includes(pageNumber?.toString())) acc.push(pageNumber?.toString());
    return acc;
  }, [] as string[]);
  const allDocumentChunks: IDocumentChunk[] = await dataSources.DocumentChunk.getMany({
    pageNumber: { $in: uniquePages },
    oem: oemId,
    documentId: document._id,
    sort: ["pageNumber:asc"],
  });
  const combinedText = (isOCRDocument ? allDocumentChunks : documentChunks)
    .map(
      doc =>
        `Chunk ID: ${doc._id?.toString()}\nChunk Content: ${doc.content}\n${doc.imageSummaries.join(
          "\n",
        )}`,
    )
    .join("\n\n");

  const prompt = appConfig?.prompts?.find(
    featurePrompt => featurePrompt.featureKey === PROMPT_FEATURES.ANSWER_GENERATION,
  )?.value;

  const useHFInference = appConfig?.features?.includes(appFeaturesEnum.hfInferenceForGenAI);

  timeLogger.time(
    `generateSearchResultAnswers: query: ${query}, document: ${document.aiGeneratedTitle}`,
  );

  const { parsed } = await (useHFInference
    ? dataSources.HuggingFace.generateAnswer(
        ` <context>
      ${combinedText}
      </context>
      <question>
      ${query}
      </question>
      ${
        oem.aiAssistantConfiguration.role ? `<role>${oem.aiAssistantConfiguration.role}</role>` : ""
      }
      ${prompt}
      **Answer:**`,
        HFAnswerSchema,
      )
    : dataSources.MistralAi.generateFormattedResponse(
        ` <context>
      ${combinedText}
      </context>
      <question>
      ${query}
      </question>
      ${
        oem.aiAssistantConfiguration.role ? `<role>${oem.aiAssistantConfiguration.role}</role>` : ""
      }
      ${prompt}
      **Answer:**`,
        AnswerSchema,
      ));

  timeLogger.timeEnd(
    `generateSearchResultAnswers: query: ${query}, document: ${document.aiGeneratedTitle}`,
  );

  let processedContent = {};

  try {
    processedContent = {
      ...parsed,
      pageNumber: allDocumentChunks.find(chunk => chunk._id?.toString() === parsed.chunkID)
        ?.pageNumber,
      // @ts-ignore
      citations: (parsed?.citations || [])?.map(
        (citation: { id: string; source: string; highlight: string }) => ({
          ...citation,
          pageNumber: allDocumentChunks.find(chunk => chunk._id?.toString() === citation.source)
            ?.pageNumber,
        }),
      ),
    };
  } catch (e) {
    console.error("Error:", e);
  }

  await dataSources.PubnubApi.publishMessage({
    channel: getOemUserChannelId(userId),
    message: {
      documentId: document._id.toString(),
      documentTitle: document.aiGeneratedTitle,
      results: processedContent,
      jobId: searchResultJobId,
      text: NOTIFICATION_IDENTIFIERS.SEARCH_RESULT_GENERATED,
    },
  });
};

export const generateSearchResultAnswers = async (
  dataSources: IContext["dataSources"],
  job: Job<{
    query: string;
    results: Array<IDocumentChunk>;
    searchResultJobId: string;
    oemId: string;
    userId: string;
  }>,
) => {
  const { data } = job.attrs;
  if (!data) logger.info("[AGENDA][GENERATE_SEARCH_RESULT_ANSWERS] Data is not present");
  const { query, results, oemId, userId, searchResultJobId } = data!;
  const uniqueDocumentIds = results.reduce((acc, result) => {
    const { documentId } = result;
    if (!acc.includes(documentId?.toString())) acc.push(documentId?.toString());
    return acc;
  }, [] as string[]);

  const [documents, oem, appConfig]: [IDocument[], IOem, IAppConfig] = await Promise.all([
    dataSources.Document.getMany({
      _id: { $in: uniqueDocumentIds },
      oem: oemId,
    }),
    dataSources.Oem.loadOne(oemId),
    dataSources.AppConfig.getOne(),
  ]);

  await Promise.all(
    documents.map(document =>
      generateDocumentResult({
        query,
        dataSources,
        oem,
        appConfig,
        oemId,
        document,
        results,
        userId,
        searchResultJobId,
      }),
    ),
  );
};

export const scanDocuments = async ({
  args,
  dataSources,
  user,
}: ServiceParamsType<{
  documentsToScan: string[];
}>) => {
  const { documentsToScan } = args?.input || {};
  if (!documentsToScan?.length) return null;

  const documents: IDocument[] = await dataSources.Document.getMany({
    _id: { $in: documentsToScan },
    oem: user.organization,
    status: { $in: [DOCUMENT_STATUS.PENDING_SCANNING, DOCUMENT_STATUS.FAILED_SCANNING] },
  });

  await Promise.all(
    documents.map(document => {
      const fileType = document.title.split(".").at(-1)!.toLowerCase();
      if (fileType === "pdf" || IMAGE_FILE_TYPES.includes(fileType)) {
        return runJob({
          jobName: OCR_DOCUMENT_JOB,
          userId: user.id,
          oemId: user.organization,
          documentId: document._id,
        });
      }
      return runJob({
        jobName: PARSE_DOCUMENT_JOB,
        userId: user.id,
        oemId: user.organization,
        documentId: document._id,
      });
    }),
  );
};
