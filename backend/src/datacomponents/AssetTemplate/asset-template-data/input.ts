export default `#graphql
  input InputAssignAssetTemplateInventoryParts {
    assetTemplateId: ID!
    inventoryParts: [ID!]!
  }

  input InputCreateAssetTemplate {
    title: String!
    templateId: String
    description: String
    image: String
    thumbnail: String
    visibility: Int
  }

  input InputRemoveAssetTemplateInventoryPart {
    assetTemplateId: ID!
    partId: ID!
  }

  input InputUpdateAssetTemplate {
    _id: ID!
    title: String
    templateId: String
    description: String
    image: String
    thumbnail: String
    visibility: Int
  }
`;
