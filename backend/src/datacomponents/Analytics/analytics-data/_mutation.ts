import to from "await-to-js";
import { roles } from "~/directives";
import { throwIfError, entityTypes, getEnums } from "~/utils";
import { ERROR } from "~/environment";
import chartTypes from "$/settings/enums/analytics/_chart-type.json";
import entityType from "$/settings/enums/analytics/_entity-type.json";
import { InputCreateOwnOemReport } from "~/graphql/types";
import { ANALYTICS_AXIS_OPTIONS } from "~/config";
import { IContext } from "~/types/common";
import isPaidFeatureAvailable from "~/utils/isPaidFeatureAvailable";

const chartTypeEnum = getEnums(chartTypes, "reference");
const entityEnum = getEnums(entityType, "reference");
const xAxisKeys = getEnums(ANALYTICS_AXIS_OPTIONS, "reference");

const { oem }: { oem?: string } = roles.is;

export const mutationTypes = `
  type Mutation {
    createOwnOemReport(input:InputCreateOwnOemReport): Report @${oem}
    duplicateOwnOemReport(input:InputDuplicateOwnOemReport): Report @${oem}
    updateOwnOemReport(input:InputUpdateOwnOemReport): Report @${oem}
    deleteOwnOemReport(id: ID!): String @${oem}
  }
`;

export const mutationResolvers = {
  Mutation: {
    createOwnOemReport: async (
      _: any,
      args: { input: InputCreateOwnOemReport },
      { user, dataSources }: IContext,
    ) => {
      const { input } = args || {};
      if (!input) return null;
      const { entity } = input;
      if (!entity) return null;
      let xAxis = {};

      switch (entity) {
        case entityEnum.workOrder:
          xAxis = {
            value: xAxisKeys.WorkOrderType,
            label: ANALYTICS_AXIS_OPTIONS.WorkOrderType,
          };
          break;
        case entityEnum.machine:
        case entityEnum.facility: {
          const [customFields, userOem] = await Promise.all([
            dataSources.CustomAdditionalField.getMany({
              oem: user.organization,
              type: entityTypes[entity],
              fieldType: "singleSelect",
              enabled: true,
              deleted: false,
              sort: ["order:asc", "created_at:asc"],
              limit: 1,
            }),
            dataSources.Oem.getOne({ _id: user.organization }),
          ]);

          if (customFields.length !== 0) {
            xAxis = { value: String(customFields[0]._id), label: customFields[0].label };
          } else if (await isPaidFeatureAvailable(dataSources, "teams", userOem)) {
            xAxis = {
              value: xAxisKeys.Team,
              label: ANALYTICS_AXIS_OPTIONS.Team,
            };
          } else {
            xAxis = { value: "none", label: "None" };
          }
          break;
        }
        default:
          break;
      }
      const chartType = chartTypeEnum.horizontalBar;
      const segment = { value: "none", label: "None" };

      let title = "New ";
      // @ts-ignore
      if (entityEnum.machine === entity) {
        title += "Asset";
      } else if (entityEnum.facility === entity) {
        title += "Connection";
      } else {
        // @ts-ignore
        title += entityType[entity];
      }
      title += " Report";
      title = title.charAt(0) + title.substring(1).toLowerCase();

      const payload = {
        ...input,
        title,
        segment,
        xAxis,
        chartType,
        oem: user.organization,
      };
      const [err, report] = await to(dataSources.Report.save(payload));
      throwIfError(err, ERROR.USER.BAD_USER_INPUT);
      return report;
    },

    duplicateOwnOemReport: async (
      _: any,
      args: { input: any },
      { dataSources, user }: IContext,
    ) => {
      const { input } = args || {};
      if (!input) return null;
      const { title } = input;
      const _report = await dataSources.Report.getById(input._id);
      const newReport = {
        title,
        xAxis: _report.xAxis,
        segment: _report.segment,
        chartType: _report.chartType,
        entity: _report.entity,
        filters: _report.filters,
        table: _report.table,
        oem: user.organization,
      };
      const [err, report] = await to(dataSources.Report.save(newReport));
      throwIfError(err, ERROR.USER.BAD_USER_INPUT);
      return report;
    },

    updateOwnOemReport: async (_: any, args: { input: any }, { dataSources, user }: IContext) => {
      const { input } = args || {};
      if (!args) return null;

      const report = await dataSources.Report.getById({ _id: input._id, oem: user.organization });

      if (!report) return throwIfError("Report not found");
      const { ...doc } = input;
      const updatedReport = await dataSources.Report.save({
        ...doc,
      });
      return updatedReport;
    },

    deleteOwnOemReport: async (_: any, args: any, { dataSources, user }: IContext) => {
      const { id } = args || {};
      if (!id) return null;
      const report = await dataSources.Report.getById({ _id: id, oem: user.organization });
      await dataSources.Report.softDeleteById(report._id, user.id);
      return "ok";
    },
  },
};
