import to from "await-to-js";
import { Job } from "agenda";
import type {
  ICustomFieldObject,
  ICustomAdditionalField,
} from "~/datamodels/CustomAdditionalField/interface";
import { IDocument } from "~/datamodels/Document/interface";
import { ERROR } from "~/environment";
import InvalidInputError from "~/errors/InvalidInputError";
import NotFoundError from "~/errors/NotFoundError";
import type { IContext, ServiceParamsType } from "~/types/common";
import validateCustomFields from "~/utils/validations/_custom-field-validation";
import throwIfError from "~/utils/_throw-error";
import type { IOem } from "~/datamodels/Oem/interface";
import type { IUser } from "~/datamodels/User/interface";
import runJob from "~/agenda/run";
import { SYNC_AI_SEARCH_FEEDBACK_JOB } from "~/agenda/constants";
import { logger } from "~/utils";

export const updateDocument = async ({
  args,
  dataSources,
  user,
  translate,
}: ServiceParamsType<{
  customFields: ICustomFieldObject[];
  _id?: string;
}>) => {
  const { _id, ...doc } = args.input || {};

  const document: IDocument = await dataSources.Document.getOne({
    _id,
    oem: user.organization,
  });

  if (!document)
    throw new NotFoundError(translate("error_messages.not_found").replace("{}", "document"));

  if (doc?.customFields?.length) {
    const existingCustomFields = [
      ...JSON.parse(JSON.stringify(document.customFields)),
      ...doc.customFields,
    ];

    const customFields: Pick<ICustomAdditionalField, "_id" | "fieldType">[] =
      await dataSources.CustomAdditionalField.getMany(
        {
          _id: { $in: existingCustomFields.map(cf => cf.fieldId) },
          oem: user.organization,
          type: "knowledgeBase",
        },
        { _id: 1, fieldType: 1 },
      );
    const customFieldsMap = customFields.reduce(
      (map: { [key: string]: { _id: string; fieldType: string } }, cf) => {
        map[cf._id] = cf;
        return map;
      },
      {},
    );

    const fields = existingCustomFields.reduce((arr, item) => {
      const index = arr.findIndex((ar: ICustomFieldObject) => ar.fieldId === item.fieldId);
      const customField = customFieldsMap[item.fieldId];
      if (index !== -1) {
        if (customField.fieldType === "multiSelect") {
          arr[index].values = item.values;
        } else {
          arr[index].value = item.value;
        }
      } else {
        arr.push(item);
      }
      return arr;
    }, []);

    const { isValid, validatedCustomFields, errors } = await validateCustomFields({
      dataSources,
      oem: user.organization!.toString(),
      type: "knowledgeBase",
      customFields: fields,
    });

    if (!isValid) throw new InvalidInputError(errors?.join(", ") as string);

    doc.customFields = validatedCustomFields!;
  }

  const [errUpdatedDocument, updatedDocument] = await to(
    dataSources.Document.save({
      _id: document._id,
      customFields: doc.customFields,
    }),
  );

  throwIfError(errUpdatedDocument, ERROR.USER.BAD_USER_INPUT);

  return updatedDocument;
};

export const dumpFeedbackToGoogleSheet = async (
  _: IContext["dataSources"],
  {
    attrs,
  }: Job<{
    userName: string;
    userEmail: string;
    organizationName: string;
    description: string;
    resultingAnswers: {
      documentId: string;
      documentTitle: string;
      answer: string;
      documentChunks: string[];
    }[];
    question: string;
  }>,
) => {
  const { data } = attrs;
  if (!data) logger.info("[AGENDA][DUMP_FEEDBACK] Data is not present");
  const { userName, userEmail, organizationName, description, resultingAnswers, question } = data!;

  const res = await fetch(process.env.GOOGLE_SHEETS_SCRIPT_URL!, {
    method: "POST",
    body: JSON.stringify({
      name: userName,
      email: userEmail,
      organization: organizationName,
      question,
      description,
      answers: resultingAnswers.map(answer => ({
        ...answer,
        documentChunks: answer.documentChunks.join("\n"),
      })),
    }),
  });
  if (res.status !== 200) throw new Error("Failed to dump feedback to google sheets");
};

export const uploadSearchFeedback = async ({
  dataSources,
  user,
  args,
}: ServiceParamsType<{
  resultDocuments: {
    documentId: string;
    documentTitle: string;
    answer: string;
    documentChunks: string[];
  }[];
  description: string;
  queryText: string;
}>) => {
  const { resultDocuments, description, queryText } = args.input;
  const [userDetails, oemDetails]: [IUser, IOem] = await Promise.all([
    dataSources.User.loadOne(user.id),
    dataSources.Oem.loadOne(user.organization),
  ]);

  runJob({
    jobName: SYNC_AI_SEARCH_FEEDBACK_JOB,
    userName: userDetails.name,
    userEmail: userDetails.email,
    organizationName: oemDetails.name,
    question: queryText,
    description,
    resultingAnswers: resultDocuments,
  });
};
