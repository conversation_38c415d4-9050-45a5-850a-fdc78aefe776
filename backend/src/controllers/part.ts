import { Request, Response } from "express";
import { createInventoryPart } from "~/services/part/create";
import { updateInventoryPart } from "~/services/part/update";
import { getAllInventoryParts, getInventoryPart } from "~/services/part/fetch";
import { serviceFnInputGenerator } from "~/utils/inputGenerator";
import { controllerErrorHandler } from "~/utils/controllerErrorHandler";
import { PAGINATED_QUERY_PAGE_SIZE } from "~/constants/global";
import { IInventoryPart } from "~/datamodels/InventoryPart/interface";

const project = (inventoryParts: IInventoryPart[]) =>
  inventoryParts.map((inventoryPart: IInventoryPart) => ({
    name: inventoryPart.name,
    description: inventoryPart.description,
    articleNumber: inventoryPart.articleNumber,
    customFields: inventoryPart.customFields,
    // @ts-ignore
    createdAt: inventoryPart.created_at,
    // @ts-ignore
    updatedAt: inventoryPart.updated_at,
  }));

export const createInventoryPartController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    const partDetails = await createInventoryPart(params);
    return res.status(200).json({
      message: "Part created successfully!",
      data: { articleNumber: partDetails?.articleNumber },
    });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const getAllInventoryPartsController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.params.limit = PAGINATED_QUERY_PAGE_SIZE;
    params.args.params.skip = (Number(params.args.query.page || 1) - 1) * PAGINATED_QUERY_PAGE_SIZE;
    const inventoryParts = await getAllInventoryParts(params);
    return res.status(200).json({
      data: project(inventoryParts.parts),
      totalPages: Math.ceil(inventoryParts.totalCount / PAGINATED_QUERY_PAGE_SIZE),
      page: Number(params.args.query.page || 1),
    });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const getInventoryPartController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.articleNumber = params.args.params.id;
    const inventoryPart = await getInventoryPart(params);
    if (inventoryPart) {
      return res.status(200).json({
        data: project([inventoryPart])[0],
      });
    }
    return res
      .status(404)
      .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const updateInventoryPartController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    params.args.input.partArticleNumber = params.args.params.id;
    const partDetails = await updateInventoryPart(params);
    if (partDetails) {
      return res.status(200).json({
        message: "Part updated successfully!",
        data: { articleNumber: partDetails?.articleNumber },
      });
    }
    return res
      .status(404)
      .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};
