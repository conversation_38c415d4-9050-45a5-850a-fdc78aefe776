import { Types } from "mongoose";
import type { IContact } from "~/datamodels/Contact/interface";
import type { ICustomer } from "~/datamodels/Customer/interface";
import type { IUser } from "~/datamodels/User/interface";
import type { ServiceParamsType } from "~/types/common";
import { buildQueryParams } from "~/utils";
import { prepareFilterQuery } from "~/utils/customers";

export const getAllCustomerPortals = async ({ args, dataSources, user }: ServiceParamsType) => {
  const { skip, limit, sort, where } = args.params || {};
  const filterQuery = [];
  const query = { ...args };

  let isExistingContactLinkable = true;
  let isConnectionAlreadyEstablished = false;

  const { filterQuery: connectionFilterQuery } = await prepareFilterQuery({
    args,
    user,
    dataSources,
  });

  const connectionPayload = {
    ...(connectionFilterQuery?.length ? { $and: connectionFilterQuery } : {}),
    oem: user.organization,
  };

  const customers: { _id: string }[] = await dataSources.Customer.getManyByQuery(
    connectionPayload,
    {
      _id: 1,
    },
  );

  if (!customers.length)
    return {
      customerPortals: [],
      limit,
      skip,
      currentPage: skip && limit ? Math.floor(skip / limit + 1) : 1,
      totalCount: 0,
    };

  const accessibleConnections: string[] = customers.map(customer => customer._id?.toString());

  if (where.searchQuery) {
    const regex = { $regex: where.searchQuery || "", $options: "i" };
    filterQuery.push({
      $or: [{ name: regex }, { email: regex }],
    });
  }

  const linkableConnectionIds: string[] = [];

  if (where.orgToLink) {
    // if this org is already linked to a connection, then new users of this org can only be linked to that connection
    const isAlreadyLinked: ICustomer = await dataSources.Customer.getOne({
      linkedOrg: where.orgToLink,
      oem: user.organization,
    });
    if (isAlreadyLinked) {
      if (accessibleConnections.includes(isAlreadyLinked._id?.toString()))
        linkableConnectionIds.push(isAlreadyLinked._id?.toString());
      isConnectionAlreadyEstablished = true;
      if (where.userToLink) {
        const existingUser: IUser = await dataSources.User.getOne({
          _id: where.userToLink,
        });
        const existingContact: IContact = await dataSources.Contact.getOne({
          email: existingUser.email,
          oem: user.organization,
        });
        if (
          existingContact &&
          isAlreadyLinked._id?.toString() !== existingContact.connection?.toString()
        ) {
          isExistingContactLinkable = false;
        }
      }
    } else {
      // return those connections that are not already linked to any org
      const connections: ICustomer[] = await dataSources.Customer.getMany({
        oem: user.organization,
        $or: [{ linkedOrg: { $exists: false } }, { linkedOrg: { $eq: null } }],
      });
      connections.forEach(connection => {
        if (accessibleConnections.includes(connection._id?.toString()))
          linkableConnectionIds.push(connection._id?.toString());
      });
    }
  }

  const queryPayload = {
    ...(filterQuery?.length ? { $and: filterQuery } : {}),
    ...(linkableConnectionIds.length
      ? { connection: { $in: linkableConnectionIds } }
      : { connection: { $in: accessibleConnections } }),
    ...buildQueryParams(query),
    oem: user.organization,
    sort: sort?.length ? sort : ["name:asc"],
  };

  delete queryPayload.where;

  const [customerPortals, totalCount] = await Promise.all([
    dataSources.CustomerPortal[where.withDeleted ? "getManyWithDeleted" : "getMany"](queryPayload),
    dataSources.CustomerPortal.totalCount({
      ...queryPayload,
      limit: -1,
      skip: 0,
    }),
  ]);

  return {
    customerPortals,
    limit,
    skip,
    isExistingContactLinkable,
    isConnectionAlreadyEstablished,
    currentPage: skip && limit ? Math.floor(skip / limit + 1) : 1,
    totalCount,
  };
};

export const getCustomerPortal = async ({ args, dataSources, user }: ServiceParamsType) => {
  const { id } = args.input || {};
  const customerPortal = await dataSources.CustomerPortal.getOne({
    _id: new Types.ObjectId(id),
    oem: user.organization,
  });

  if (!customerPortal) return null;

  return customerPortal;
};
