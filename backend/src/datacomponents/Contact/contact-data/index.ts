import inputTypes from "~/datacomponents/Contact/contact-data/input";
import {
  mutationTypes,
  createContact,
  updateContact,
  deleteContact,
  inviteContacts,
  approveInvite,
  declineInvite,
  removeCustomerInvite,
  resendCustomerInvite,
} from "~/datacomponents/Contact/contact-data/mutation";
import { queryTypes, listContacts } from "~/datacomponents/Contact/contact-data/query";
import types, { typeResolvers } from "~/datacomponents/Contact/contact-data/type";

export default {
  resolvers: {
    Mutation: {
      createContact,
      updateContact,
      deleteContact,
      inviteContacts,
      approveInvite,
      declineInvite,
      removeCustomerInvite,
      resendCustomerInvite,
    },
    Query: {
      listContacts,
    },
    ...typeResolvers,
  },
  types: `
      ${inputTypes}
      ${mutationTypes}
      ${queryTypes}
      ${types}
    `,
};
