import formData from "form-data";
import Mailgun from "mailgun.js";

const DOMAIN = process.env.MAIL_DOMAIN;
const API_KEY = process.env.MAIL_API;
const FROM = process.env.MAIL_FROM_EMAIL;
const options = {
  username: "api",
  key: API_KEY,
  url: "https://api.eu.mailgun.net",
};

const mailgun = new Mailgun(formData);
const client = mailgun.client(options);

export const sendEmail = async ({
  from = FROM,
  to,
  subject,
  text = "",
  html = "",
  template,
  data,
  batch = null,
}) => {
  try {
    const payload = {
      from,
      to,
      subject,
      ...(text ? { text } : {}),
      ...(html ? { html } : {}),
      ...(template ? { template } : {}),
      ...(data ? { "t:variables": JSON.stringify(data) } : {}),
      ...(batch ? { "recipient-variables": JSON.stringify(batch) } : {}),
    };

    const res = await client.messages.create(DOMAIN, payload);
    return Promise.resolve(res);
  } catch (error) {
    return Promise.reject(error);
  }
};
