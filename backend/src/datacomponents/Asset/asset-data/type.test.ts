import { describe, it, expect, vi } from "vitest";
import { typeResolvers } from "~/datacomponents/Asset/asset-data/type";

const createMockContext = () => ({
  dataSources: {
    Machine: { save: vi.fn(), getManyByQuery: vi.fn() },
    Oem: { loadOne: vi.fn() },
    Ticket: { countDocuments: vi.fn() },
    User: { loadOne: vi.fn(), countDocuments: vi.fn() },
    Guide: { getOne: vi.fn() },
    MachineTemplate: { getById: vi.fn(), loadOne: vi.fn() },
    Team: { loadManyByQuery: vi.fn() },
    InventoryPart: { loadOne: vi.fn() },
    Customer: { loadOne: vi.fn() },
    AppConfig: { getOne: vi.fn() },
  },
  user: { id: "user123", organization: "org456" },
});

const mockMachine = {
  _id: "machine123",
  image: "image_url_override",
  description: "description",
  documentFolders: { internalId: "internal456", externalId: "external789" },
  uuid: "uuid-123",
  isQRCodeEnabled: true,
  oem: "oem123",
  customer: "customer123",
  created_at: "2024-01-01",
  updated_at: "2024-02-01",
  teams: ["team1", "team2"],
  template: "template123",
  detachedFromTemplate: {
    description: true,
    inventoryParts: true,
    image: true,
  },
};

describe("Asset Resolvers", () => {
  const ctx = createMockContext();

  it("resolves documentFolders", async () => {
    const { documentFolders } = typeResolvers.Asset;
    const result = await documentFolders(mockMachine, {}, ctx);

    expect(result).toEqual({
      internalId: "internal456",
      externalId: "external789",
      _id: "oem123__machine123__FOLDERS",
    });
  });

  it("resolves uuid", async () => {
    const { uuid } = typeResolvers.Asset;
    const result = await uuid(mockMachine, {}, ctx);

    expect(result).toBe("uuid-123");
  });

  it("resolves generalAccessUrl", async () => {
    const { generalAccessUrl } = typeResolvers.Asset;
    const result = await generalAccessUrl(mockMachine, {}, ctx);

    expect(result).toBe("http://localhost:8008/m/uuid-123");
  });

  it("resolves isQRCodeEnabled", async () => {
    ctx.dataSources.User.countDocuments.mockResolvedValueOnce(1);
    const { isQRCodeEnabled } = typeResolvers.Asset;
    const result = await isQRCodeEnabled(mockMachine, {}, ctx);

    expect(result).toBe(true);
  });

  it("resolves isBoxFoldersDisabled", async () => {
    ctx.dataSources.Oem.loadOne.mockResolvedValueOnce({ isBoxFoldersDisabled: true });
    const { isBoxFoldersDisabled } = typeResolvers.Asset;
    const result = await isBoxFoldersDisabled(mockMachine, {}, ctx);

    expect(result).toBe(true);
  });

  it("resolves customer", async () => {
    ctx.dataSources.Customer.loadOne.mockResolvedValueOnce({ name: "Customer A" });
    const { customer } = typeResolvers.Asset;
    const result = await customer(mockMachine, {}, ctx);

    expect(result).toEqual({ name: "Customer A" });
  });

  it("resolves oem", async () => {
    ctx.dataSources.Oem.loadOne.mockResolvedValueOnce({ name: "OEM A" });
    const { oem } = typeResolvers.Asset;
    const result = await oem(mockMachine, {}, ctx);

    expect(result).toEqual({ name: "OEM A" });
  });

  it("resolves totalOpenTickets", async () => {
    ctx.dataSources.Oem.loadOne.mockResolvedValueOnce({ statuses: [{ _id: "closedStatus" }] });
    ctx.dataSources.Ticket.countDocuments.mockResolvedValueOnce(5);

    const { totalOpenTickets } = typeResolvers.Asset;
    const result = await totalOpenTickets(mockMachine, {}, ctx);

    expect(result).toBe(5);
  });

  it("resolves createdAt", async () => {
    const { createdAt } = typeResolvers.Asset;
    const result = await createdAt(mockMachine);

    expect(result).toBe("2024-01-01");
  });

  it("resolves updatedAt", async () => {
    const { updatedAt } = typeResolvers.Asset;
    const result = await updatedAt(mockMachine);

    expect(result).toBe("2024-02-01");
  });

  it("resolves teams", async () => {
    ctx.dataSources.Team.loadManyByQuery.mockResolvedValueOnce([
      { name: "Team 1" },
      { name: "Team 2" },
    ]);

    const { teams } = typeResolvers.Asset;
    const result = await teams(mockMachine, {}, ctx);

    expect(result).toEqual([{ name: "Team 1" }, { name: "Team 2" }]);
  });

  it("resolves numberOfTeams", async () => {
    const { numberOfTeams } = typeResolvers.Asset;
    const result = await numberOfTeams(mockMachine);

    expect(result).toBe(2);
  });

  it("resolves description", async () => {
    const { description } = typeResolvers.Asset;
    const result = await description(mockMachine, {}, ctx);

    expect(result).toBe("description");
  });

  it("resolves template description", async () => {
    ctx.dataSources.MachineTemplate.getById.mockResolvedValueOnce({
      description: "Template Description",
    });

    const { description } = typeResolvers.Asset;
    const result = await description(
      {
        ...mockMachine,
        detachedFromTemplate: {
          ...mockMachine.detachedFromTemplate,
          description: false,
        },
      },
      {},
      ctx,
    );

    expect(result).toBe("Template Description");
  });

  it("resolves image", async () => {
    const { image } = typeResolvers.Asset;
    const result = await image(mockMachine, {}, ctx);

    expect(result).toBe("image_url_override");
  });

  it("resolves template image", async () => {
    ctx.dataSources.MachineTemplate.getById.mockResolvedValueOnce({
      image: "image_url",
    });

    const { image } = typeResolvers.Asset;
    const result = await image(
      {
        ...mockMachine,
        detachedFromTemplate: {
          ...mockMachine.detachedFromTemplate,
          image: false,
        },
      },
      {},
      ctx,
    );

    expect(result).toBe("image_url");
  });
});

describe("AssetPart Resolvers", () => {
  const ctx = createMockContext();

  const mockAssetPart = {
    part: "part123",
    addedBy: "user456",
  };

  it("resolves inventoryPart", async () => {
    ctx.dataSources.InventoryPart.loadOne.mockResolvedValueOnce({ name: "Inventory Part A" });
    const { part } = typeResolvers.AssetPart;
    const result = await part(mockAssetPart, {}, ctx);

    expect(result).toEqual({ name: "Inventory Part A" });
    expect(ctx.dataSources.InventoryPart.loadOne).toHaveBeenCalledWith("part123");
  });

  it("resolves addedBy", async () => {
    ctx.dataSources.User.loadOne.mockResolvedValueOnce({ name: "User A" });
    const { addedBy } = typeResolvers.AssetPart;
    const result = await addedBy(mockAssetPart, {}, ctx);

    expect(result).toEqual({ name: "User A" });
  });
});

describe("AssetDetachedFromTemplate Resolvers", () => {
  const mockAsset = {
    _id: "asset123",
    description: "description",
    image: "image",
    inventoryParts: null,
  };

  it("resolves detachedFromTemplate", () => {
    const { detachedFromTemplate } = typeResolvers.Asset;
    const result = detachedFromTemplate({ detachedFromTemplate: mockAsset, template: "" });

    expect(result.description).toBe(true);
    expect(result.documentation).toBe("detached");
    expect(result.image).toBe(true);
    expect(result.inventoryParts).toBe(false);
  });
});
