import type { Request, Response } from "express";
import {
  getMarketPlaceOrganization,
  getMarketPlaceProduct,
  getMarketPlaceProducts,
} from "~/services/marketplace/fetch";
import { controllerErrorHandler } from "~/utils/controllerErrorHandler";
import { serviceFnInputGenerator } from "~/utils/inputGenerator";

export const getMarketPlaceOrganizationController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    const marketPlaceOrg = await getMarketPlaceOrganization(params);
    if (!marketPlaceOrg)
      return res
        .status(404)
        .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
    return res.status(200).json(marketPlaceOrg);
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const getMarketPlaceProductsController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    const marketPlaceOrg = await getMarketPlaceProducts(params);
    if (!marketPlaceOrg)
      return res
        .status(404)
        .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
    return res.status(200).json(marketPlaceOrg);
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};

export const getMarketPlaceProductController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    const marketPlaceOrg = await getMarketPlaceProduct(params);
    if (!marketPlaceOrg)
      return res
        .status(404)
        .json({ message: params.translate("error_messages.invalid_id_utf_encode") });
    return res.status(200).json(marketPlaceOrg);
  } catch (exception) {
    controllerErrorHandler(res, exception);
  }
};
