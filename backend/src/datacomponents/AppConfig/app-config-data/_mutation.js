import to from "await-to-js";
import { roles } from "~/directives";
import { throwIfError } from "../../../utils";
import { ERROR } from "~/environment";

export const mutationTypes = `
  type Mutation {
    saveAppConfig(input: InputAppConfig): AppConfig @${roles.is.admin},
  }
`;

export const mutationResolvers = {
  Mutation: {
    saveAppConfig: async (_, args, { dataSources }) => {
      const { input } = args;
      if (!input) return null;

      const { _id, features, plans } = input;
      const appConfig = await dataSources.AppConfig.getOne({ _id });
      const payload = { _id };
      if (features) payload.features = features;
      if (plans && plans.length === 1) {
        const existingPlans = appConfig.plans;
        const payloadPlan = plans[0];
        const planToUpdate = existingPlans.findIndex(plan => plan.type === payloadPlan.type);
        if (planToUpdate >= 0) {
          payload.plans = existingPlans;
          const plan = plans[0];
          payload.plans[planToUpdate] = plan;
        }
      }
      const [err, updatedAppConfig] = await to(dataSources.AppConfig.save(payload));

      throwIfError(err, ERROR.USER.BAD_USER_INPUT);

      return updatedAppConfig;
    },
  },
};
