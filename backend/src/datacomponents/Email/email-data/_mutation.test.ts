// eslint-disable-next-line import/no-extraneous-dependencies
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { Types } from "mongoose";
import { mutationResolvers } from "./_mutation";
import { removeThreadsFromTickets, sendEmailWithAttachmentsAndNotify } from "./_helper";
import { IUser } from "~/datamodels/User/interface";
import { config } from "~/config/_nylas";

const { Mutation } = mutationResolvers;

const OEM_ID = new Types.ObjectId("6499a52b5dfe76648ff67a81");

const user: IUser = {
  username: "admin",
  organization: OEM_ID,
};
const emailAddressId = "65129da26d06b548a7f092j8";

const oem = {
  _id: OEM_ID,
  email: {
    accounts: [
      {
        grantId: "ldwiqhwiduyfvujydvsa",
        accessToken: "abcdefghi",
        accountId: "abc",
        emailAddress: "<EMAIL>",
        _id: emailAddressId,
      },
    ],
  },
};

const to = ["<EMAIL>"];
const body = "The description";
const subject = "ABC";
const threadId = "1aufx8aq5f93n4thicystgznv";
const messageId = "1aufx8aq5f93n4thicystgiop";
const ticketId = "65129da26d06b548a7f571c4";

const dataSources = {
  Ticket: { getOne: vi.fn(), save: vi.fn(), updateOne: vi.fn() },
  Oem: {
    getById: vi.fn(),
    save: vi.fn(),
    countDocuments: vi.fn(),
    loadOne: vi.fn(),
    Oem: {
      findOneAndUpdate: vi.fn(),
    },
    getOne: vi.fn(),
  },
  EmailThread: {
    EmailThread: { findOneAndUpdate: vi.fn() },
    getOne: vi.fn(),
  },
  EmailMessage: {
    getMany: vi.fn(),
    updateManyByQuery: vi.fn(),
    EmailMessage: { findOneAndUpdate: vi.fn() },
  },
  NylasApi: {
    sendEmail: vi.fn(),
    exchangeCodeForToken: vi.fn(),
    revokeToken: vi.fn(),
    deleteAccount: vi.fn(),
    getThread: vi.fn(),
    markThreadReadOrUnread: vi.fn(),
    isGrantValid: vi.fn(),
    moveEmailToFolder: vi.fn(),
    getMessage: vi.fn(),
  },
};

vi.mock("./_helper", async () => ({
  getFileUrl: vi.fn(),
  removeThreadsFromTickets: vi.fn(),
  sendEmailWithAttachmentsAndNotify: vi.fn(),
  processMessagesInBatches: vi.fn(),
}));

vi.mock("~/agenda/run", async () => ({
  default: vi.fn(),
}));

vi.mock("../../../services/email/sync", async () => ({
  syncEmailAccount: vi.fn(),
  syncEmailAccountJob: vi.fn(),
  bulkDeleteAccount: vi.fn(),
  syncEmailFolderUnreadCount: vi.fn(),
}));

describe("Email", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("linkEmailAccount", async () => {
    test("Link email account", async () => {
      const code = "abcdefghi";
      const grantId = "hsduqyfvudsubkcjbn";
      const { emailAddress } = oem.email.accounts[0];
      const accessToken = "askugjfydqbkalsknc";
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.NylasApi.exchangeCodeForToken.mockResolvedValueOnce({
        emailAddress,
        accessToken,
        grantId,
      });
      // @ts-ignore
      await Mutation.linkEmailAccount(
        null,
        { code },
        { dataSources, user, req: { t: (_: string) => "" } },
      );
      expect(dataSources.NylasApi.exchangeCodeForToken).toHaveBeenCalledOnce();
      expect(dataSources.Oem.Oem.findOneAndUpdate).toHaveBeenCalledOnce();
      expect(dataSources.NylasApi.exchangeCodeForToken).toHaveBeenCalledWith(
        code,
        config.emailRedirectUri,
      );
      expect(dataSources.Oem.Oem.findOneAndUpdate).toHaveBeenCalledWith(
        {
          "email.accounts.emailAddress": emailAddress,
          _id: user.organization,
        },
        {
          $set: {
            "email.accounts.$": {
              accessToken,
              emailAddress,
              accountStatus: "connected",
              grantId,
              isSynced: false,
              statusUpdateTime: expect.any(String),
            },
          },
        },
      );
      expect(removeThreadsFromTickets).toHaveBeenCalledTimes(0);
    });

    test("Remove all email references from ticket when emails do not match", async () => {
      const code = "abcdefghi";
      const grantId = "hsduqyfvudsubkcjbn";
      const emailAddress = "<EMAIL>";
      const accessToken = "askugjfydqbkalsknc";
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.NylasApi.exchangeCodeForToken.mockResolvedValueOnce({
        emailAddress,
        accessToken,
        grantId,
      });
      // @ts-ignore
      await Mutation.linkEmailAccount(
        null,
        { code },
        { dataSources, user, req: { t: (_: string) => "" } },
      );
      expect(dataSources.NylasApi.exchangeCodeForToken).toHaveBeenCalledOnce();
      expect(dataSources.Oem.Oem.findOneAndUpdate).toHaveBeenCalledOnce();
      expect(dataSources.NylasApi.exchangeCodeForToken).toHaveBeenCalledWith(
        code,
        config.emailRedirectUri,
      );
      expect(dataSources.Oem.Oem.findOneAndUpdate).toHaveBeenCalledWith(
        {
          _id: user.organization,
        },
        {
          $push: {
            "email.accounts": {
              accessToken,
              emailAddress,
              grantId,
              accountStatus: "connected",
              isSynced: false,
              statusUpdateTime: expect.any(String),
            },
          },
        },
      );
    });
  });

  describe("unlinkEmailAccount", async () => {
    const notThisOemAndSameEmailQuery = {
      _id: { $ne: oem._id },
      "email.accounts.emailAddress": oem.email.accounts[0].emailAddress,
    };

    test("Unlink & delete account if no other OEM is using the same email account", async () => {
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.Oem.countDocuments.mockResolvedValueOnce(0);
      dataSources.Oem.countDocuments.mockResolvedValueOnce(0);
      // @ts-ignore
      await Mutation.unlinkEmailAccount(
        null,
        { emailAddressId },
        {
          dataSources,
          user,
          req: { t: (_: string) => "" },
        },
      );
      expect(dataSources.Oem.countDocuments).toHaveBeenCalledWith({
        ...notThisOemAndSameEmailQuery,
        "email.accounts.grantId": oem.email.accounts[0].grantId,
      });
      expect(dataSources.Oem.countDocuments).toHaveBeenCalledWith(notThisOemAndSameEmailQuery);
      expect(dataSources.NylasApi.revokeToken).toHaveBeenCalledTimes(0);
      expect(dataSources.Oem.Oem.findOneAndUpdate).toHaveBeenCalledOnce();
      expect(dataSources.Oem.Oem.findOneAndUpdate).toHaveBeenCalledWith(
        {
          "email.accounts.grantId": oem.email.accounts[0].grantId,
          _id: oem._id,
        },
        {
          $set: {
            "email.accounts.$": {
              emailAddress: oem.email.accounts[0].emailAddress,
            },
          },
        },
      );
      expect(dataSources.NylasApi.deleteAccount).toHaveBeenCalledOnce();
      expect(dataSources.NylasApi.deleteAccount).toHaveBeenCalledWith(
        oem.email.accounts[0].grantId,
      );
    });
  });

  describe("updateEmailThread", async () => {
    test("Save nylas thread read/unread status", async () => {
      const oemTicketId = "65129da26d06b548a7f571c7";
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.Oem.loadOne.mockResolvedValueOnce(oem);
      dataSources.Ticket.getOne.mockResolvedValueOnce({
        _id: oemTicketId,
      });
      dataSources.NylasApi.getThread.mockResolvedValueOnce({
        id: "abc",
        messages: [],
      });

      await Mutation.updateEmailThread(
        null,
        {
          input: {
            threadId,
            ticketId,
            unread: false,
            emailAddressId,
          },
        },
        {
          // @ts-ignore
          dataSources,
          user,
          req: { t: (_: string) => "" },
        },
      );

      expect(dataSources.EmailThread.getOne).toHaveBeenCalledOnce();
      expect(dataSources.NylasApi.markThreadReadOrUnread).toHaveBeenCalledOnce();
      expect(dataSources.NylasApi.markThreadReadOrUnread).toHaveBeenCalledWith({
        grantId: oem.email.accounts[0].grantId,
        threadId,
        unread: false,
      });
    });

    test("Remove thread ref from the ticket", async () => {
      const oemTicketId = "65129da26d06b548a7f571c7";
      const newTicketId = "65129da26d06b548a7f571c8";
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.Oem.loadOne.mockResolvedValueOnce(oem);
      dataSources.Ticket.getOne.mockResolvedValueOnce({
        _id: oemTicketId,
      });
      dataSources.Ticket.getOne.mockResolvedValueOnce({
        _id: newTicketId,
      });
      dataSources.NylasApi.getThread.mockResolvedValueOnce({
        id: "abc",
        messages: [],
      });

      await Mutation.updateEmailThread(
        null,
        {
          input: {
            threadId,
            ticketId,
            unread: false,
            emailAddressId,
          },
        },
        {
          // @ts-ignore
          dataSources,
          user,
          req: { t: (_: string) => "" },
        },
      );

      expect(dataSources.EmailThread.getOne).toHaveBeenCalledOnce();
      expect(dataSources.NylasApi.markThreadReadOrUnread).toHaveBeenCalledOnce();
      expect(dataSources.NylasApi.markThreadReadOrUnread).toHaveBeenCalledWith({
        grantId: oem.email.accounts[0].grantId,
        threadId,
        unread: false,
      });
      expect(dataSources.Ticket.updateOne).toHaveBeenCalledWith(
        { _id: newTicketId },
        { $pull: { linkedEmailThreads: { accountId: emailAddressId, threadId } } },
      );
    });

    test("Add thread ref to the ticket", async () => {
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.Oem.loadOne.mockResolvedValueOnce(oem);
      dataSources.Ticket.getOne.mockResolvedValueOnce({ _id: ticketId });
      dataSources.Ticket.getOne.mockResolvedValueOnce(null);
      dataSources.NylasApi.getThread.mockResolvedValueOnce({
        id: "abc",
        messages: [],
      });

      await Mutation.updateEmailThread(
        null,
        {
          input: {
            threadId,
            ticketId,
            unread: false,
            emailAddressId,
          },
        },
        {
          // @ts-ignore
          dataSources,
          user,
          req: { t: (_: string) => "" },
        },
      );

      expect(dataSources.EmailThread.getOne).toHaveBeenCalledOnce();

      expect(dataSources.Ticket.save).toHaveBeenCalledWith({
        _id: ticketId,
        $push: { linkedEmailThreads: { accountId: emailAddressId, threadId } },
      });
      expect(dataSources.Ticket.getOne).toHaveBeenCalledWith({
        _id: ticketId,
        oem: oem._id,
      });
      expect(dataSources.Ticket.getOne).toHaveBeenCalledWith({
        linkedEmailThreads: {
          $elemMatch: {
            accountId: emailAddressId,
            threadId,
          },
        },
        oem: oem._id,
      });
    });
  });

  describe("sendEmail", async () => {
    const uuid = "60d643a4-c8f0-4c01-b709-000b225ee7ed";
    test("Send email", async () => {
      const messageObj = {
        threadId,
        id: messageId,
        files: [],
      };
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.NylasApi.sendEmail.mockResolvedValueOnce(messageObj);

      await Mutation.sendEmail(
        null,
        {
          input: {
            subject,
            body,
            to,
            uuid,
            emailAddressId,
          },
        },
        {
          // @ts-ignore
          dataSources,
          user,
          req: { t: (_: string) => "" },
        },
      );

      expect(sendEmailWithAttachmentsAndNotify).toHaveBeenCalledWith({
        dataSources,
        user,
        to,
        subject,
        body,
        uuid,
        ticket: null,
        replyToMessageId: undefined,
        inlineAttachments: undefined,
        emailAddressId,
        fileUrls: undefined,
        forwardedAttachments: undefined,
        bcc: undefined,
        cc: undefined,
        grantId: oem.email.accounts[0].grantId,
      });
    });

    test("Send email and attach to ticket id", async () => {
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.NylasApi.sendEmail.mockResolvedValueOnce({
        threadId,
        id: messageId,
        files: [],
      });
      dataSources.Ticket.getOne.mockResolvedValueOnce({ _id: ticketId });

      await Mutation.sendEmail(
        null,
        {
          input: {
            subject,
            body,
            to,
            ticketId,
            uuid,
            emailAddressId,
          },
        },
        {
          // @ts-ignore
          dataSources,
          user,
          req: { t: (_: string) => "" },
        },
      );

      expect(sendEmailWithAttachmentsAndNotify).toHaveBeenCalledWith({
        dataSources,
        user,
        to,
        subject,
        body,
        ticket: { _id: ticketId },
        uuid,
        replyToMessageId: undefined,
        inlineAttachments: undefined,
        emailAddressId,
        fileUrls: undefined,
        forwardedAttachments: undefined,
        bcc: undefined,
        cc: undefined,
        grantId: oem.email.accounts[0].grantId,
      });
      expect(dataSources.Ticket.getOne).toHaveBeenCalledWith({
        _id: ticketId,
        oem: OEM_ID,
      });
    });

    test("Error when ticket is not found", async () => {
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.NylasApi.sendEmail.mockResolvedValueOnce({
        threadId,
        messageId,
      });
      dataSources.Ticket.getOne.mockResolvedValueOnce(null);

      await expect(
        Mutation.sendEmail(
          null,
          {
            input: {
              subject,
              body,
              to,
              ticketId,
              emailAddressId,
            },
          },
          {
            // @ts-ignore
            dataSources,
            user,
            req: { t: (_: string) => "" },
          },
        ),
      ).rejects.toThrowError("Ticket not found");

      expect(dataSources.Ticket.getOne).toHaveBeenCalledWith({
        _id: ticketId,
        oem: OEM_ID,
      });
    });

    test("Error when `draftId` and `body` missing", async () => {
      await expect(
        Mutation.sendEmail(
          null,
          {
            input: {
              subject,
              // body,
              to,
              emailAddressId,
            },
          },
          {
            // @ts-ignore
            dataSources,
            user,
            req: { t: (_: string) => "" },
          },
        ),
      ).rejects.toThrowError("Body is required");
    });

    test("Error when `draftId` and `to` missing", async () => {
      await expect(
        Mutation.sendEmail(
          null,
          {
            input: {
              subject,
              body,
              // to,
              emailAddressId,
            },
          },
          {
            // @ts-ignore
            dataSources,
            user,
            req: { t: (_: string) => "" },
          },
        ),
      ).rejects.toThrowError("To email(s) required");
    });
  });

  describe("moveEmailToFolder", () => {
    afterEach(() => {
      vi.restoreAllMocks();
    });

    const emailAddressId = "65129da26d06b548a7f092j8";
    const messageIds = ["message1", "message2"];
    const folderId = "folder123";

    test("Successfully moves emails to the folder", async () => {
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.NylasApi.isGrantValid.mockResolvedValueOnce(true);
      dataSources.NylasApi.moveEmailToFolder.mockResolvedValueOnce("Success");

      const response = await Mutation.moveEmailToFolder(
        null,
        { input: { emailAddressId, messageIds, folderId } },
        { dataSources, user, req: { t: (_: string) => "" } },
      );

      expect(dataSources.Oem.getById).toHaveBeenCalledWith(user.organization);
      expect(dataSources.NylasApi.isGrantValid).toHaveBeenCalledWith(oem.email.accounts[0].grantId);
      expect(dataSources.NylasApi.moveEmailToFolder).toHaveBeenCalledWith(
        oem.email.accounts[0].grantId,
        messageIds,
        folderId,
      );

      expect(response).toBe("Moving Emails...");
    });
  });
});
