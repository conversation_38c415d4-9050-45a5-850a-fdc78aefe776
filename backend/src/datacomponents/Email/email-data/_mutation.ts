import moment from "moment";
import { features, roles } from "~/directives";
import { getEmailConfigFromOem, throwIfError } from "~/utils";
import { processMessagesInBatches, sendEmailWithAttachmentsAndNotify } from "./_helper";
import type {
  InputEmailInlineAttachments,
  InputSendEmail,
  InputUpdateEmailThread,
  InputForwardedEmailAttachments,
} from "~/graphql/types";
import { IContext } from "~/types/common";
import { config } from "~/config/_nylas";
import InvalidInputError from "~/errors/InvalidInputError";
import { IOem } from "~/datamodels/Oem/interface";
import { bulkDeleteAccount, syncEmailFolderUnreadCount } from "~/services/email/sync";
import { getEmailThread } from "~/services/emailThread/fetch";
import { SYNC_EMAIL_ACCOUNT_JOB } from "~/agenda/constants";
import logger from "~/utils/logger";
import runJob from "~/agenda/run";

const { technician, oem: oemRole } = roles.is;
const { emails } = features.oemOwns;

export const mutationTypes = `#graphql
  type Mutation {
    linkEmailAccount(code: String!): Oem @${emails} @${oemRole}
    unlinkEmailAccount(emailAddressId: ID!): Oem @${emails} @${oemRole}
    updateEmailThread(input: InputUpdateEmailThread): EmailThreadWithTicket @${emails} @${technician}
    sendEmail(input: InputSendEmail): String @${emails} @${technician}
    moveEmailToFolder(input: InputMoveEmailToFolder): String @${emails} @${technician}
  }
`;

export const mutationResolvers = {
  Mutation: {
    linkEmailAccount: async (
      _: any,
      args: { code: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) => {
      const { code } = args || {};
      const oem: IOem = await dataSources.Oem.getById(user.organization);
      if (!oem) return null;

      const linkedAccounts = oem.email?.accounts?.filter(
        account => account.accountStatus === "connected",
      );

      if ((linkedAccounts?.length || 0) + 1 > oem.email?.allowedAccounts!)
        throw new InvalidInputError(translate("error_messages.email_linking_limit"));

      const { grantId, accessToken, emailAddress } =
        await dataSources.NylasApi.exchangeCodeForToken(code, config.emailRedirectUri);

      const matchingEmailConfiguration = oem.email?.accounts?.find?.(
        account => account.emailAddress === emailAddress,
      );

      if (matchingEmailConfiguration && matchingEmailConfiguration.accountStatus === "connected") {
        throw new InvalidInputError(translate("error_messages.email_already_linked"));
      }

      const emailAccountToAdd = {
        accessToken,
        grantId,
        emailAddress,
        accountStatus: "connected",
        statusUpdateTime: moment().utc().format(),
        isSynced: false,
      };

      if (matchingEmailConfiguration) {
        await dataSources.Oem.Oem.findOneAndUpdate(
          {
            _id: user.organization,
            "email.accounts.emailAddress": emailAddress,
          },
          {
            $set: {
              "email.accounts.$": emailAccountToAdd,
            },
          },
        );
      } else
        await dataSources.Oem.Oem.findOneAndUpdate(
          {
            _id: user.organization,
          },
          {
            // @ts-ignore
            $push: {
              "email.accounts": emailAccountToAdd,
            },
          },
        );

      await runJob({
        grantId,
        oem: user.organization?.toString()!,
        user,
        jobName: SYNC_EMAIL_ACCOUNT_JOB,
      });

      return await dataSources.Oem.getOne({ _id: user.organization });
    },
    unlinkEmailAccount: async (
      _: any,
      { emailAddressId }: { emailAddressId: string },
      { dataSources, user }: IContext,
    ) => {
      const oem: IOem = await dataSources.Oem.getById(user.organization);
      if (!oem || !oem?.email?.accounts?.length) return null;

      const { grantId, emailAddress } = getEmailConfigFromOem({ emailAddressId, oem });

      const notThisOemAndSameEmailQuery = {
        _id: { $ne: oem._id },
        "email.accounts.emailAddress": emailAddress,
      };
      const [anotherOemUsingSameEmailAndGrantId, anotherOemUsingSameEmailCount] = await Promise.all(
        [
          dataSources.Oem.countDocuments({
            ...notThisOemAndSameEmailQuery,
            "email.accounts.grantId": grantId,
          }),
          dataSources.Oem.countDocuments(notThisOemAndSameEmailQuery),
        ],
      );

      try {
        if (anotherOemUsingSameEmailCount === 0 && anotherOemUsingSameEmailAndGrantId === 0) {
          await dataSources.NylasApi.deleteAccount(grantId!);
        }
      } catch (e: any) {
        // 401: Could not verify credentials
        if (e.statusCode !== 401) {
          return throwIfError(e);
        }
      }
      await dataSources.Oem.Oem.findOneAndUpdate(
        { "email.accounts.grantId": grantId, _id: user.organization },
        {
          $set: {
            "email.accounts.$": {
              emailAddress,
            },
          },
        },
      );

      bulkDeleteAccount(grantId, dataSources);
      return await dataSources.Oem.getOne({ _id: user.organization });
    },
    updateEmailThread: async (
      _: any,
      { input }: { input: InputUpdateEmailThread },
      { dataSources, user, req: { t: translate } }: IContext,
    ) => {
      const { threadId, unread, ticketId, emailAddressId } = input || {};
      const oem: IOem = await dataSources.Oem.getById(user.organization);

      if (!oem) return throwIfError("Oem not found");

      const { grantId, _id } = getEmailConfigFromOem({ emailAddressId, oem });

      if (!grantId) {
        throw new InvalidInputError(translate("error_messages.invalid_email_address"));
      }

      if (ticketId) {
        const isOemTicket = await dataSources.Ticket.getOne({
          _id: ticketId,
          oem: oem._id,
        });
        if (!isOemTicket) return throwIfError("Ticket not found");
      }

      if (unread !== undefined) {
        await Promise.all([
          input.isDraft
            ? dataSources.NylasApi.markDraftReadOrUnread({
                grantId,
                draftId: threadId,
                unread: unread as boolean,
              })
            : dataSources.NylasApi.markThreadReadOrUnread({
                grantId,
                threadId,
                unread: unread as boolean,
              }),
          dataSources.EmailThread.EmailThread.findOneAndUpdate(
            { grantId, id: threadId },
            { unread: !!unread },
          ),
        ]);
        if (!input.isDraft) {
          const nylasThread = await dataSources.NylasApi.getThread(grantId, threadId);
          await Promise.all(
            nylasThread.messages.map(
              async nylasMessage =>
                await dataSources.EmailMessage.updateOne(
                  { id: nylasMessage.id },
                  { unread: nylasMessage.unread },
                ),
            ),
          );
        }
      }
      let { thread, ticket } = await getEmailThread({
        // @ts-ignore
        args: { input: { threadId, emailAddressId } },
        dataSources,
        user,
      });

      // @ts-ignore
      syncEmailFolderUnreadCount(dataSources, grantId, user.organization, emailAddressId);

      if (ticketId !== undefined && ticket && ticketId !== ticket._id.toString()) {
        await dataSources.Ticket.updateOne(
          { _id: ticket._id },
          { $pull: { linkedEmailThreads: { threadId, accountId: _id } } },
        );
        ticket = null;
      }

      if (ticketId && (!ticket || ticketId !== ticket._id.toString())) {
        ticket = await dataSources.Ticket.save({
          _id: ticketId,
          $push: { linkedEmailThreads: { threadId, accountId: _id } },
        });
      }

      return {
        ticket,
        thread,
      };
    },
    sendEmail: async (
      _: any,
      { input }: { input: InputSendEmail },
      { dataSources, user, req: { t: translate } }: IContext,
    ) => {
      const {
        subject,
        body,
        to,
        cc,
        bcc,
        fileUrls,
        ticketId,
        replyToMessageId,
        uuid,
        forwardedAttachments,
        inlineAttachments,
        emailAddressId,
        draftId,
      } = input || {};

      if (!body) return throwIfError("Body is required");
      if (!to || !to.length) return throwIfError("To email(s) required");

      const oem: IOem = await dataSources.Oem.getById(user?.organization);

      if (!oem) return throwIfError("Oem not found");

      const { grantId, _id } = getEmailConfigFromOem({ emailAddressId, oem });

      if (!grantId) {
        throw new InvalidInputError(translate("error_messages.invalid_email_address"));
      }

      let ticket = null;

      if (ticketId) {
        ticket = await dataSources.Ticket.getOne({ _id: ticketId, oem: oem?._id });
        if (!ticket) return throwIfError("Ticket not found");
      }

      sendEmailWithAttachmentsAndNotify({
        dataSources,
        user,
        ticket,
        to: to as string[],
        cc: cc as string[],
        bcc: bcc as string[],
        subject: subject as string,
        body: body as string,
        fileUrls: fileUrls as string[],
        replyToMessageId: replyToMessageId as string,
        uuid: uuid as string,
        draftId: draftId as string,
        forwardedAttachments: forwardedAttachments as InputForwardedEmailAttachments[],
        inlineAttachments: inlineAttachments as InputEmailInlineAttachments[],
        grantId,
        emailAddressId: _id,
      });

      return "Sending Email...";
    },
    moveEmailToFolder: async (
      _: any,
      { input }: { input: { emailAddressId: string; messageIds: string[]; folderId: string } },
      { dataSources, user, req: { t: translate } }: IContext,
    ) => {
      const { emailAddressId, messageIds, folderId } = input || {};

      const oem: IOem = await dataSources.Oem.getById(user.organization);

      if (!oem) return throwIfError("Oem not found");

      const { grantId } = getEmailConfigFromOem({ emailAddressId, oem });

      const isValidGrant = await dataSources.NylasApi.isGrantValid(grantId);

      if (!grantId) {
        throw new InvalidInputError(translate("error_messages.invalid_email_address"));
      }

      if (!isValidGrant) {
        logger.info("Invalid Grant Id");
        return;
      }

      await dataSources.NylasApi.moveEmailToFolder(grantId, messageIds, folderId);
      await processMessagesInBatches(messageIds, grantId, dataSources);

      syncEmailFolderUnreadCount(dataSources, grantId, oem._id.toString(), emailAddressId);
      return "Moving Emails...";
    },
  },
};
