import to from "await-to-js";
import { Types } from "mongoose";
import { isSelfCreated, throwIfError } from "~/utils";
import InvalidInputError from "~/errors/InvalidInputError";
import type { ContactIdForm } from "~/types/contact";
import type { ICustomerPortal } from "~/datamodels/CustomerPortal/interface";
import { ICustomer } from "~/datamodels/Customer/interface";
import { FORBIDDEN } from "~/environment";

export const deleteCustomerPortal = async ({ args, dataSources, user }: ContactIdForm) => {
  const { input } = args;
  if (!input || !input.id) throw new InvalidInputError("Invalid Input");
  const [err, portal]: [any, ICustomerPortal | undefined] = await to(
    dataSources.CustomerPortal.getOne({
      _id: new Types.ObjectId(input.id),
      oem: user.organization,
    }),
  );
  if (err) {
    throw err;
  }
  if (!portal) {
    throw new InvalidInputError("Invalid Id");
  }
  if (portal.createdBy && !isSelfCreated(user, { createdBy: portal.createdBy?.toString() })) {
    throw Error(FORBIDDEN);
  }

  const connection: ICustomer = await dataSources.Customer.loadOne(portal.connection);

  try {
    await Promise.all([
      dataSources.CustomerPortal.softDeleteById(input.id, user.id),
      dataSources.Contact.softDeleteMany({ connection: connection._id }, user.id),
      dataSources.Customer.updateOne({ _id: connection._id }, { $unset: { linkedOrg: "" } }),
    ]);
  } catch (err) {
    return throwIfError(err);
  }
  return input.id;
};
