name: Deploy Triggers
on:
  push:
    branches:
      - develop
      - main
      - dev
    paths:
      - "triggers/**"
jobs:
  deploy:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: triggers
    environment:
      name: ${{ (github.ref == 'refs/heads/main' && 'Release') || (github.ref == 'refs/heads/develop' && 'Alpha') }}
    env:
      TRIGGERS_PROJECT_ID: ${{ vars.TRIGGERS_PROJECT_ID }}
      TRIGGERS_APP_ID: ${{ vars.TRIGGERS_APP_ID }}
      TRIGGERS_USERNAME: ${{ vars.TRIGGERS_USERNAME }}
      TRIGGERS_API_KEY: ${{ secrets.TRIGGERS_API_KEY }}
      TRIGGERS_ENVIRONMENT: ${{ vars.TRIGGERS_ENVIRONMENT }}
      TRIGGERS_CLUSTER_NAME: ${{ vars.TRIGGERS_CLUSTER_NAME }}
      TRIGGERS_DATABASE: ${{ vars.TRIGGERS_DATABASE }}
      TRIGGERS_SERVICE_ID: ${{ vars.TRIGGERS_SERVICE_ID }}
      TRIGGERS_AWS_ACCOUNT_ID: ${{ vars.TRIGGERS_AWS_ACCOUNT_ID }}
    strategy:
      matrix:
        node-version: [16.x]
    steps:
      - name: Get Code
        uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Clean install dependencies
        run: |
          npm ci
          npm test

      - name: Delete old triggers
        run: npm run deploy:delete

      - name: Add new triggers
        run: npm run deploy:add

      - name: Update changed triggers
        run: npm run deploy:update
