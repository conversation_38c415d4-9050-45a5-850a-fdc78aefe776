import { Apollo<PERSON>rror, UserInputError } from "apollo-server-express";
import { GraphQLResolveInfo } from "graphql";
import { ExecutionContext } from "graphql/execution/execute";
import { Types } from "mongoose";

import { MACHINE_DOCUMENTATION_STATUSES } from "~/constants/machine";
import {
  CREATE_BOX_WEBHOOK_FOR_MACHINE_JOB,
  CREATE_BOX_WEBHOOK_FOR_TEMPLATE_JOB,
  DE_INDEX_AI_ASSISTANT_FILE_JOB,
  DELETE_BOX_WEBHOOK_FOR_MACHINE_JOB,
  DELETE_BOX_WEBHOOK_FOR_TEMPLATE_JOB,
  INDEX_AI_ASSISTANT_FILE_JOB,
} from "~/agenda/constants";
import runJob from "~/agenda/run";
import {
  createIndexingOperationProgressMessage,
  getAiAssistantWithValidations,
  getAllFolderIDs,
  getFoldersFiles,
  getMultipleF<PERSON>,
  validateAccess,
  validateAccessToMachine,
  validateAccessToMachineTemplate,
} from "~/datacomponents/AiAssistant/ai-assistant-data/helpers";
import { DocumentStatus as AiAssistantDocumentStatus } from "~/datamodels/AiAssistant/constants";
import { IAiAssistant, IAiAssistantDocument } from "~/datamodels/AiAssistant/interface";
import { roles } from "~/directives";
import { FORBIDDEN } from "~/environment";
import { IContext } from "~/types/common";
import { isSelfCreated, logger } from "~/utils";
import ForbiddenError from "~/errors/ForbiddenError";
import {
  InputAddAiAssistantDocuments,
  InputCreateAiAssistantV2,
  InputRemoveAiAssistantDocuments,
  InputRenameAiAssistant,
  InputUpdateAiAssistant,
} from "~/graphql/types";
import { submitIdeaSuggestion } from "~/services/aiAssistant/utils";
import { IMachineTemplate } from "~/datamodels/MachineTemplate/interface";

const { staff } = roles.is;

export const mutationTypes = `#graphql
  type Mutation {
    addAiAssistantDocuments(input: InputAddAiAssistantDocuments): ID @${staff}
    createAiAssistant(machineID: ID): AiAssistant @${staff}
    createAiAssistantV2(input: InputCreateAiAssistantV2): AiAssistant @${staff}
    removeAiAssistant(aiAssistantID: ID): ID @${staff}
    removeAiAssistantDocuments(input: InputRemoveAiAssistantDocuments): ID @${staff}
    renameAiAssistant(input: InputRenameAiAssistant): ID @${staff}
    submitIdeaSuggestion(suggestion: String): String @${staff}
    updateAiAssistant(input: InputUpdateAiAssistant): ID @${staff}
  }
`;

export const mutationResolvers = {
  Mutation: {
    addAiAssistantDocuments: async (
      _: ExecutionContext["contextValue"],
      {
        input: { _id, files },
      }: {
        input: InputAddAiAssistantDocuments;
      },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) => {
      const { aiAssistant, machine, template } = await getAiAssistantWithValidations(
        _id,
        dataSources,
        user,
        false,
        true,
        false,
      );

      const machineIds = {
        externalFolderId: "",
        internalFolderId: "",
      };

      const boxFiles = await getMultipleFiles(dataSources, files, user.organization!);

      if (machine) {
        if (
          machine.template &&
          (machine.detachedFromTemplate?.documentation ===
            MACHINE_DOCUMENTATION_STATUSES.DETACHING ||
            machine.detachedFromTemplate?.documentation ===
              MACHINE_DOCUMENTATION_STATUSES.ATTACHING)
        ) {
          return null;
        }

        machineIds.externalFolderId = machine.documentFolders?.externalId!;
        machineIds.internalFolderId = machine.documentFolders?.internalId!;

        const isTemplateAttached =
          machine.template &&
          machine.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHED;
        if (isTemplateAttached) {
          const machineTemplate = await dataSources.MachineTemplate.loadOne(machine.template);
          machineIds.externalFolderId = machineTemplate.documentFolders?.externalId;
          machineIds.internalFolderId = machineTemplate.documentFolders?.internalId;
        }
      } else if (template) {
        machineIds.externalFolderId = template.documentFolders?.externalId!;
        machineIds.internalFolderId = template.documentFolders?.internalId!;
      }

      const [externalFolderIDs, internalFolderIds] = await Promise.all([
        getAllFolderIDs(dataSources, machineIds.externalFolderId!, user.organization!),
        getAllFolderIDs(dataSources, machineIds.internalFolderId!, user.organization!),
      ]);

      boxFiles.forEach(boxFile => {
        if (!(boxFile && [...externalFolderIDs, ...internalFolderIds].includes(boxFile.parent.id)))
          throw new UserInputError(`Invalid folder "${boxFile.parent.id}"`);
      });

      const externalDocumentsSet = new Set(
        boxFiles
          .filter(({ parent: { id } }) => id === machineIds.externalFolderId)
          .map(({ id }) => id),
      );

      const existingState = (aiAssistant.documents?.externalDocuments || [])
        .concat(aiAssistant.documents?.internalDocuments || [])
        .reduce<{ [key: string]: { id: string; status: number } }>(
          (map, { _id: id, externalStorageServiceDocumentID: documentID, status }) => ({
            ...map,
            [documentID?.toString()!]: { id: id?.toString()!, status },
          }),
          {},
        );

      const filteredDocuments = files.filter(fileID => {
        if (
          !(
            !(fileID in existingState) ||
            existingState[fileID].status === AiAssistantDocumentStatus.INDEXING_FAILED
          )
        )
          return !logger.warn(`Ignoring invalid file "${fileID}"`);

        return true;
      });

      const pushQuery: { [key: string]: Array<IAiAssistantDocument> } = {};
      const updatedFiles: {
        [key: string]: {
          externalStorageServiceDocumentID: IAiAssistantDocument["externalStorageServiceDocumentID"];
          key: string;
        };
      } = {};

      filteredDocuments.forEach(fileID => {
        const key = externalDocumentsSet.has(fileID) ? "externalDocuments" : "internalDocuments";
        if (!(key in pushQuery)) pushQuery[key] = [];
        pushQuery[key].push({
          consumedStorage: 0,
          externalStorageServiceDocumentID: fileID,
          status: AiAssistantDocumentStatus.INITIATED_INDEXING,
        });
      });

      await Promise.all(
        Object.entries(pushQuery)
          .map(async ([key, values]) =>
            Promise.all(
              values.map(async value => {
                // first try to push a new element
                let maybeUpdatedAiAssistant =
                  await dataSources.AiAssistant.AiAssistant.findOneAndUpdate<IAiAssistant>(
                    {
                      _id: aiAssistant._id,
                      [`documents.${key}`]: {
                        $not: {
                          $elemMatch: {
                            externalStorageServiceDocumentID:
                              value.externalStorageServiceDocumentID,
                          },
                        },
                      },
                    },
                    {
                      // @ts-ignore
                      $push: { [`documents.${key}`]: value },
                    },
                    {
                      new: true,
                    },
                  );

                if (!maybeUpdatedAiAssistant) {
                  logger.info(
                    `Last indexing operation for file "${value.externalStorageServiceDocumentID}" failed`,
                  );

                  // @ts-ignore
                  maybeUpdatedAiAssistant =
                    await dataSources.AiAssistant.AiAssistant.findOneAndUpdate<IAiAssistant>(
                      {
                        _id: aiAssistant._id,
                        [`documents.${key}`]: {
                          $elemMatch: {
                            externalStorageServiceDocumentID:
                              value.externalStorageServiceDocumentID,
                            status: AiAssistantDocumentStatus.INDEXING_FAILED,
                          },
                        },
                      },
                      {
                        $set: {
                          [`documents.${key}.$[element].status`]:
                            AiAssistantDocumentStatus.INITIATED_INDEXING,
                        },
                      },
                      {
                        arrayFilters: [
                          {
                            [`element.externalStorageServiceDocumentID`]:
                              value.externalStorageServiceDocumentID,
                          },
                        ],
                        new: true,
                      },
                    );
                }

                if (!maybeUpdatedAiAssistant)
                  logger.warn(`Ignoring invalid file "${value.externalStorageServiceDocumentID}"`);
                else
                  updatedFiles[
                    // @ts-ignore
                    maybeUpdatedAiAssistant.documents[key]
                      .find(
                        // @ts-ignore
                        ({ externalStorageServiceDocumentID }) =>
                          value.externalStorageServiceDocumentID ===
                          externalStorageServiceDocumentID,
                      )
                      ?._id.toString()
                  ] = {
                    key,
                    externalStorageServiceDocumentID: value.externalStorageServiceDocumentID,
                  };
              }),
            ),
          )
          .flat(),
      );

      // run jobs for the documents
      await Promise.all(
        Object.entries(updatedFiles).map(
          ([documentID, { key, externalStorageServiceDocumentID }]) =>
            Promise.all([
              dataSources.PubnubApi.publishMessage(
                createIndexingOperationProgressMessage({
                  aiAssistantID: aiAssistant._id?.toString()!,
                  documentID,
                  key,
                  externalStorageServiceDocumentID,
                  oemID: user.organization?.toString()!,
                  status: AiAssistantDocumentStatus.INITIATED_INDEXING,
                }),
              ),
              runJob({
                aiAssistantID: aiAssistant._id!.toString(),
                externalStorageServiceDocumentID,
                fileID: documentID,
                jobName: INDEX_AI_ASSISTANT_FILE_JOB,
                throwPubNubErrorOnStorageExceeded: true,
                key,
                machineID: aiAssistant.machineID,
                oemID: user.organization,
              }),
            ]),
        ),
      );
    },

    createAiAssistant: async (
      _: ExecutionContext["contextValue"],
      { machineID }: { machineID: string },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) => {
      const [machine] = await Promise.all([
        dataSources.Machine.loadOne(new Types.ObjectId(machineID)),
        validateAccess(
          dataSources,
          user.organization?.toString()!,
          user.id?.toString()!,
          false,
          true,
        ),
        validateAccessToMachine(dataSources, machineID, user),
      ]);

      if (!(machine && machine.oem.toString() === user.organization?.toString()))
        throw new UserInputError(`No asset found against ID "${machineID}"`);

      if (
        machine.template &&
        (machine.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.DETACHING ||
          machine.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHING)
      ) {
        return null;
      }

      const isTemplateAttached =
        machine.template &&
        machine.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHED;
      const machineIds = {
        externalFolderId: machine.documentFolders?.externalId,
        internalFolderId: machine.documentFolders?.internalId,
      };

      if (isTemplateAttached) {
        const template = await dataSources.MachineTemplate.loadOne(machine.template);
        machineIds.externalFolderId = template?.documentFolders?.externalId;
        machineIds.internalFolderId = template?.documentFolders?.internalId;
      }

      /**
       * Fetch files from box for internal and external documents
       * and mark those files as to be indexed for polling on frontend
       */
      const { externalDocuments, internalDocuments } = await getFoldersFiles(
        dataSources,
        machineIds.externalFolderId,
        machineIds.internalFolderId,
        machine.oem,
      );

      const data: IAiAssistant = {
        assistantName: machine.name,
        documents: {
          externalDocuments: externalDocuments.map(({ id }) => ({
            consumedStorage: 0,
            externalStorageServiceDocumentID: id,
            status: AiAssistantDocumentStatus.INITIATED_INDEXING,
          })),
          internalDocuments: internalDocuments.map(({ id }) => ({
            consumedStorage: 0,
            externalStorageServiceDocumentID: id,
            status: AiAssistantDocumentStatus.INITIATED_INDEXING,
          })),
        },
        machineID: machine._id!,
        oem: user.organization!,
        // @ts-ignore
        createdBy: user.id,
      };

      const aiAssistant: IAiAssistant = await dataSources.AiAssistant.save(data);

      try {
        await dataSources.VectaraApi.createAssistant(aiAssistant._id);
      } catch (err) {
        // In case we recieved failure from the API
        // we need to delete this so that it doesn't
        // show up in the active assistants list
        await dataSources.AiAssistant.softDeleteById(aiAssistant._id, user.id);
        logger.error(err);
        throw new ApolloError(
          `Failed to create AI Assistant "${aiAssistant._id}" for asset "${machine._id}"`,
        );
      }

      aiAssistant.documents!.externalDocuments.forEach(externalDocument =>
        runJob({
          aiAssistantID: aiAssistant._id!.toString(),
          externalStorageServiceDocumentID: externalDocument.externalStorageServiceDocumentID,
          fileID: externalDocument._id!.toString(),
          jobName: INDEX_AI_ASSISTANT_FILE_JOB,
          key: "externalDocuments",
          machineID,
          oemID: user.organization,
        }),
      );
      aiAssistant.documents!.internalDocuments.forEach(internalDocument =>
        runJob({
          aiAssistantID: aiAssistant._id!.toString(),
          externalStorageServiceDocumentID: internalDocument.externalStorageServiceDocumentID,
          fileID: internalDocument._id!.toString(),
          jobName: INDEX_AI_ASSISTANT_FILE_JOB,
          key: "internalDocuments",
          machineID,
          oemID: user.organization,
        }),
      );

      if (
        !(
          machine.documentFolders?.externalFolderBoxWebhookID &&
          machine.documentFolders?.internalFolderBoxWebhookID
        )
      )
        runJob({
          machineID,
          jobName: CREATE_BOX_WEBHOOK_FOR_MACHINE_JOB,
        });
      else logger.info(`Webhooks for machine "${machineID}" already registered`);

      return aiAssistant;
    },

    createAiAssistantV2: async (
      _: ExecutionContext["contextValue"],
      {
        input: { assetId, assetTemplateId, description, documentIds = [], name, assistantType },
      }: { input: InputCreateAiAssistantV2 },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) => {
      if (!assetId && !assetTemplateId)
        throw new UserInputError("Either assetId or assetTemplateId must be provided");

      const isAsset = !!assetId;

      const [resource] = await Promise.all([
        assetId
          ? dataSources.Machine.loadOne(new Types.ObjectId(assetId))
          : dataSources.MachineTemplate.loadOne(new Types.ObjectId(assetTemplateId!)),
        validateAccess(
          dataSources,
          user.organization?.toString()!,
          user.id?.toString()!,
          false,
          true,
        ),
      ]);

      if (assetId) await validateAccessToMachine(dataSources, assetId, user);
      else await validateAccessToMachineTemplate(dataSources, assetTemplateId!, user);

      if (!(resource && resource.oem.toString() === user.organization?.toString()))
        throw new UserInputError(
          `No asset${isAsset ? "" : " template"} found against ID "${assetId ?? assetTemplateId}"`,
        );

      const isTemplateDocumentationAttached =
        !!resource.template &&
        resource.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHED;

      if (assetTemplateId || isTemplateDocumentationAttached) {
        const assistant = await dataSources.AiAssistant.getOne({
          assistantType,
          templateId: new Types.ObjectId(assetTemplateId || resource.template),
        });

        if (assistant) {
          let assetTemplate: IMachineTemplate = resource;
          if (!assetTemplateId) {
            assetTemplate = await dataSources.MachineTemplate.loadOne(
              new Types.ObjectId(resource.template!),
            );
          }

          throw new UserInputError(
            `AI Assistant for template "${assetTemplate.title}" already exists`,
          );
        }
      } else if (assetId) {
        const assistant = await dataSources.AiAssistant.getOne({
          assistantType,
          machineID: new Types.ObjectId(assetId),
        });

        if (assistant)
          throw new UserInputError(`AI Assistant for asset "${resource.name}" already exists`);
      }

      if (
        resource.template &&
        (resource.detachedFromTemplate?.documentation ===
          MACHINE_DOCUMENTATION_STATUSES.DETACHING ||
          resource.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHING)
      ) {
        return null;
      }

      /**
       * Fetch files from box for internal and external documents
       * and mark those files as to be indexed for polling on frontend
       */
      const documentationIds = {
        externalFolderId: resource.documentFolders?.externalId,
        internalFolderId: resource.documentFolders?.internalId,
      };

      if (isTemplateDocumentationAttached) {
        const template = await dataSources.MachineTemplate.loadOne(resource.template);
        documentationIds.externalFolderId = template?.documentFolders?.externalId;
        documentationIds.internalFolderId = template?.documentFolders?.internalId;
      }

      const boxFiles = await getMultipleFiles(dataSources, documentIds, user.organization!);

      const [externalFolderIDs, internalFolderIds] = await Promise.all([
        getAllFolderIDs(dataSources, documentationIds.externalFolderId!, user.organization!),
        getAllFolderIDs(dataSources, documentationIds.internalFolderId!, user.organization!),
      ]);

      boxFiles.forEach(boxFile => {
        if (!(boxFile && [...externalFolderIDs, ...internalFolderIds].includes(boxFile.parent.id)))
          throw new UserInputError(`Invalid folder "${boxFile.parent.id}"`);
      });

      const externalDocumentsSet = new Set(
        boxFiles
          .filter(({ parent: { id } }) => id === documentationIds.externalFolderId)
          .map(({ id }) => id),
      );
      const internalDocumentsSet = new Set(
        boxFiles
          .filter(({ parent: { id } }) => id === documentationIds.internalFolderId)
          .map(({ id }) => id),
      );

      const data: IAiAssistant = {
        assistantName: name,
        assistantType,
        createdBy: new Types.ObjectId(user.id),
        description,
        documents: {
          externalDocuments: [...externalDocumentsSet].map(id => ({
            consumedStorage: 0,
            externalStorageServiceDocumentID: id,
            status: AiAssistantDocumentStatus.INITIATED_INDEXING,
          })),
          internalDocuments: [...internalDocumentsSet].map(id => ({
            consumedStorage: 0,
            externalStorageServiceDocumentID: id,
            status: AiAssistantDocumentStatus.INITIATED_INDEXING,
          })),
        },
        ...(isTemplateDocumentationAttached || !isAsset
          ? { templateId: isTemplateDocumentationAttached ? resource.template : resource._id! }
          : { machineID: resource._id! }),
        oem: user.organization!,
      };

      const aiAssistant: IAiAssistant = await dataSources.AiAssistant.save(data);

      try {
        await dataSources.VectaraApi.createAssistant(aiAssistant._id);
      } catch (err) {
        // In case we recieved failure from the API
        // we need to delete this so that it doesn't
        // show up in the active assistants list
        await dataSources.AiAssistant.softDeleteById(aiAssistant._id, user.id);
        logger.error(err);
        throw new ApolloError(
          `Failed to create AI Assistant "${aiAssistant._id}" for asset${
            isAsset ? "" : " template"
          } "${assetId ?? assetTemplateId}"`,
        );
      }

      aiAssistant.documents!.externalDocuments.forEach(externalDocument =>
        runJob({
          aiAssistantID: aiAssistant._id!.toString(),
          externalStorageServiceDocumentID: externalDocument.externalStorageServiceDocumentID,
          fileID: externalDocument._id!.toString(),
          jobName: INDEX_AI_ASSISTANT_FILE_JOB,
          key: "externalDocuments",
          ...(isTemplateDocumentationAttached || !isAsset
            ? { templateId: assetTemplateId }
            : { machineID: assetId }),
          oemID: user.organization,
        }),
      );
      aiAssistant.documents!.internalDocuments.forEach(internalDocument =>
        runJob({
          aiAssistantID: aiAssistant._id!.toString(),
          externalStorageServiceDocumentID: internalDocument.externalStorageServiceDocumentID,
          fileID: internalDocument._id!.toString(),
          jobName: INDEX_AI_ASSISTANT_FILE_JOB,
          key: "internalDocuments",
          ...(isTemplateDocumentationAttached || !isAsset
            ? { templateId: assetTemplateId }
            : { machineID: assetId }),
          oemID: user.organization,
        }),
      );

      if (
        !(
          resource.documentFolders?.externalFolderBoxWebhookID &&
          resource.documentFolders?.internalFolderBoxWebhookID
        )
      )
        runJob({
          ...(isTemplateDocumentationAttached || !isAsset
            ? { templateId: assetTemplateId }
            : { machineID: assetId }),
          jobName: isTemplateDocumentationAttached
            ? CREATE_BOX_WEBHOOK_FOR_TEMPLATE_JOB
            : CREATE_BOX_WEBHOOK_FOR_MACHINE_JOB,
        });
      else
        logger.info(
          `Webhooks for asset${isAsset ? "" : " template"} "${
            assetId ?? assetTemplateId
          }" already registered`,
        );

      return aiAssistant;
    },

    removeAiAssistant: async (
      _: ExecutionContext["contextValue"],
      { aiAssistantID }: { aiAssistantID: string },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) => {
      const { aiAssistant } = await getAiAssistantWithValidations(
        aiAssistantID,
        dataSources,
        user,
        false,
        false,
        false,
      );
      if (!aiAssistant) return null;

      if (!isSelfCreated(user, { createdBy: aiAssistant?.createdBy?._id?.toString() }))
        throw Error(FORBIDDEN);

      try {
        await dataSources.VectaraApi.deleteAssistant(aiAssistant._id);
      } catch (err) {
        // @ts-ignore
        if (err?.extensions?.response?.status !== 404)
          // In case we recieved failure from the API
          // we won't delete the assistant so that the
          // users are actually able to see on the UI
          // that their assistant is still present or
          // it will become a ghost assistant which is
          // actually consuming quota but is not visible
          // on the UI for the users
          throw err;
      }

      const creatorId = new Types.ObjectId(aiAssistant?.createdBy?._id?.toString());
      const creator = await dataSources.User.User.findOne({
        _id: creatorId,
        oem: user.organization,
      });

      if (!creator) throw new ForbiddenError("Creator not found.");

      const totalConsumedStorage =
        (aiAssistant.documents?.externalDocuments || [])
          .filter(doc => doc.status === AiAssistantDocumentStatus.INDEXED)
          .reduce((acc, { consumedStorage }) => acc + consumedStorage, 0) +
        (aiAssistant.documents?.internalDocuments || [])
          .filter(doc => doc.status === AiAssistantDocumentStatus.INDEXED)
          .reduce((acc, { consumedStorage }) => acc + consumedStorage, 0);

      const currentConsumedStorage = creator.aiAssistantConfiguration?.consumedStorage ?? 0;

      const storageToDeduct = Math.min(totalConsumedStorage, currentConsumedStorage);

      await Promise.all([
        dataSources.AiAssistant.softDeleteById(aiAssistantID, user.id) as Promise<IAiAssistant>,
        dataSources.User.save(
          {
            _id: creatorId,
            $inc: { "aiAssistantConfiguration.consumedStorage": -storageToDeduct },
          },
          dataSources,
        ),
        dataSources.AiAssistantChat.softDeleteMany(
          {
            aiAssistant: aiAssistant._id,
          },
          user.id,
        ),
      ]);
      aiAssistant.deleted = true;

      runJob(
        aiAssistant.machineID
          ? {
              machineID: aiAssistant.machineID,
              jobName: DELETE_BOX_WEBHOOK_FOR_MACHINE_JOB,
            }
          : {
              templateId: aiAssistant.templateId,
              jobName: DELETE_BOX_WEBHOOK_FOR_TEMPLATE_JOB,
            },
      );

      return aiAssistantID;
    },

    removeAiAssistantDocuments: async (
      _: ExecutionContext["contextValue"],
      {
        input: { _id, documentIds },
      }: {
        input: InputRemoveAiAssistantDocuments;
      },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) => {
      const { aiAssistant } = await getAiAssistantWithValidations(
        _id,
        dataSources,
        user,
        false,
        false,
        false,
      );

      const existingState = (aiAssistant.documents?.internalDocuments || []).reduce(
        (map, { _id: id, externalStorageServiceDocumentID, status }) => ({
          ...map,
          [id?.toString()!]: { externalStorageServiceDocumentID, key: "internalDocuments", status },
        }),
        (aiAssistant.documents?.externalDocuments || []).reduce<{
          [key: string]: {
            externalStorageServiceDocumentID: IAiAssistantDocument["externalStorageServiceDocumentID"];
            key: string;
            status: number;
          };
        }>(
          (map, { _id: id, externalStorageServiceDocumentID, status }) => ({
            ...map,
            [id?.toString()!]: {
              externalStorageServiceDocumentID,
              key: "externalDocuments",
              status,
            },
          }),
          {},
        ),
      );

      const filteredDocuments = documentIds.filter(documentID => {
        if (
          !(
            documentID in existingState &&
            [
              AiAssistantDocumentStatus.DE_INDEXING_FAILED,
              AiAssistantDocumentStatus.INDEXED,
            ].includes(existingState[documentID].status)
          )
        )
          return !logger.warn(`Ignoring file "${documentID}" as it isn't indexed`);

        return true;
      });

      if (!filteredDocuments.length) return;

      const queryOptions: Array<{ [key: string]: Types.ObjectId }> = [];
      const updateQuery: { [key: string]: number } = {};

      filteredDocuments.forEach(documentID => {
        const { key } = existingState[documentID];

        updateQuery[`documents.${key}.$[file${documentID}].status`] =
          AiAssistantDocumentStatus.INITIATED_DEINDEXING;
        queryOptions.push({
          [`file${documentID}._id`]: new Types.ObjectId(documentID),
        });
      });

      const maybeUpdatedAiAssistant =
        await dataSources.AiAssistant.AiAssistant.findOneAndUpdate<IAiAssistant>(
          {
            _id: aiAssistant._id,
            $and: filteredDocuments.map(documentID => ({
              [`documents.${existingState[documentID].key}`]: {
                $elemMatch: {
                  _id: documentID,
                  status: {
                    $in: [
                      AiAssistantDocumentStatus.DE_INDEXING_FAILED,
                      AiAssistantDocumentStatus.INDEXED,
                    ],
                  },
                },
              },
            })),
          },
          {
            $set: updateQuery,
          },
          {
            arrayFilters: queryOptions,
            new: true,
          },
        );

      if (!maybeUpdatedAiAssistant)
        throw new UserInputError("Not all the provided documents are de indexeable");

      // run jobs for the documents
      await Promise.all(
        filteredDocuments.map(documentID =>
          Promise.all([
            dataSources.PubnubApi.publishMessage(
              createIndexingOperationProgressMessage({
                aiAssistantID: aiAssistant._id?.toString()!,
                documentID,
                externalStorageServiceDocumentID:
                  existingState[documentID].externalStorageServiceDocumentID,
                key: existingState[documentID].key,
                oemID: user.organization?.toString()!,
                status: AiAssistantDocumentStatus.INITIATED_DEINDEXING,
              }),
            ),
            runJob({
              aiAssistantID: aiAssistant._id!.toString(),
              externalStorageServiceDocumentID:
                existingState[documentID].externalStorageServiceDocumentID,
              fileID: documentID,
              jobName: DE_INDEX_AI_ASSISTANT_FILE_JOB,
              key: existingState[documentID].key,
              oemID: user.organization,
            }),
          ]),
        ),
      );
    },

    renameAiAssistant: async (
      _: ExecutionContext["contextValue"],
      { input: { _id, assistantName } }: { input: InputRenameAiAssistant },
      { dataSources, user }: IContext,
      __: GraphQLResolveInfo,
    ) => {
      await getAiAssistantWithValidations(_id?.toString()!, dataSources, user, false, false, false);

      await dataSources.AiAssistant.save({
        _id,
        oem: user.organization,
        $set: {
          assistantName,
        },
      });

      return _id;
    },

    submitIdeaSuggestion: async (
      _: ExecutionContext["contextValue"],
      { suggestion }: { suggestion: string },
      { dataSources, user, req: { t: translate } }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      await submitIdeaSuggestion({
        args: {
          input: {
            suggestion,
          },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),

    updateAiAssistant: async (
      _: ExecutionContext["contextValue"],
      { input: { _id, assistantName, description } }: { input: InputUpdateAiAssistant },
      { dataSources, user }: IContext,
      ___: GraphQLResolveInfo,
    ) => {
      const { aiAssistant } = await getAiAssistantWithValidations(
        _id,
        dataSources,
        user,
        false,
        false,
        false,
      );

      if (!aiAssistant) return null;

      await dataSources.AiAssistant.updateOne(
        {
          _id: aiAssistant._id,
        },
        {
          $set: {
            assistantName,
            description,
          },
        },
      );

      return _id;
    },
  },
};
