export default `#graphql
  input InputUpdateEmailThread {
    threadId: String!
    unread: Boolean
    ticketId: ID
    emailAddressId: ID!
    isDraft: Boolean
  }

  input InputForwardedEmailAttachments {
    contentDisposition: String!
    contentType: String!
    filename: String!
    grantId: String
    id: String!
    contentId: String
    isInline: Boolean!
    size: Int!
  }

  input InputEmailInlineAttachments {
    content: String!
    content_disposition: String!
    content_id: String!
    content_type: String!
    filename: String!
    size: Int!
  }

  input InputSendEmail {
    to: [String]
    cc: [String]
    bcc: [String]
    draftId: String
    fileUrls: [String]
    subject: String
    body: String
    ticketId: String
    replyToMessageId: String    
    uuid: String
    forwardedAttachments: [InputForwardedEmailAttachments]
    inlineAttachments: [InputEmailInlineAttachments]
    emailAddressId: ID!
  }

  input InputMoveEmailToFolder {
    messageIds: [String!]!
    emailAddressId: ID!
    folderId: String!
  }
`;
