import inputTypes from "~/datacomponents/CustomerPortal/customer-portal-data/input";
import {
  mutationTypes,
  createCustomerPortal,
  updateCustomerPortal,
  deleteCustomerPortal,
} from "~/datacomponents/CustomerPortal/customer-portal-data/mutation";
import {
  queryTypes,
  listCustomerPortals,
  getCustomerPortal,
} from "~/datacomponents/CustomerPortal/customer-portal-data/query";
import types, { typeResolvers } from "~/datacomponents/CustomerPortal/customer-portal-data/type";

export default {
  resolvers: {
    Mutation: {
      createCustomerPortal,
      updateCustomerPortal,
      deleteCustomerPortal,
    },
    Query: {
      listCustomerPortals,
      getCustomerPortal,
    },
    ...typeResolvers,
  },
  types: `
      ${inputTypes}
      ${mutationTypes}
      ${queryTypes}
      ${types}
    `,
};
