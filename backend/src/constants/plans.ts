import PAID_FEATURES from "$/settings/paid-features.json";
import { getEnums } from "~/utils/_get-enums";

const paidFeatures = getEnums(PAID_FEATURES, "reference");
export const PRODUCT_TYPES = {
  ASSET_HUB: "asset-hub",
  INDUSTRIAL_AI: "industrial-ai",
  THREE_D: "3d",
  CMMS: "cmms",
  FIELD_SERVICE: "field-service",
};

export const PRODUCT_NAMES = {
  [PRODUCT_TYPES.ASSET_HUB]: "Asset Hub",
  [PRODUCT_TYPES.INDUSTRIAL_AI]: "Industrial AI",
  [PRODUCT_TYPES.THREE_D]: "3D Streams",
  [PRODUCT_TYPES.CMMS]: "CMMS",
  [PRODUCT_TYPES.FIELD_SERVICE]: "Field Service",
};

export const ALLOWED_TIER_TYPES = ["free", "pro", "enterprise"];

export const TIER_TYPES = {
  FREE: "free",
  PRO: "pro",
  ENTERPRISE: "enterprise",
};

export const LIMIT_TYPES = {
  ASSET: "asset",
  MAX_AI_USERS: "max-ai-users",
  AI_STORAGE: "ai-storage",
  AI_QUERIES: "ai-queries",
  AI_NOTETAKER_RECORDING: "ai-notetaker-recording",
  AI_SHARED_QUERIES: "ai-shared-queries",
};

export const DEFAULT_TIER_CONFIG = {
  [PRODUCT_TYPES.ASSET_HUB]: [
    {
      type: "free",
      name: "Free",
      price: 0,
      allowedFeatures: [],
      limits: [
        {
          type: LIMIT_TYPES.ASSET,
          value: "5",
        },
      ],
    },
    {
      type: "pro",
      name: "Pro",
      price: 15,
      allowedFeatures: [],
      limits: [
        {
          type: LIMIT_TYPES.ASSET,
          value: "100",
        },
      ],
    },
    {
      type: "enterprise",
      name: "Enterprise",
      allowedFeatures: [
        paidFeatures.hierarchy,
        paidFeatures.productionLines,
        paidFeatures.components,
        paidFeatures.customerPortal,
        paidFeatures.branding,
      ],
      price: -1,
      limits: [
        {
          type: LIMIT_TYPES.ASSET,
          value: -1,
        },
      ],
    },
  ],
  [PRODUCT_TYPES.INDUSTRIAL_AI]: [
    {
      type: "free",
      name: "Free",
      price: 0,
      allowedFeatures: [paidFeatures.aiAssistants, paidFeatures.aiNotetaker],
      limits: [
        {
          type: LIMIT_TYPES.MAX_AI_USERS,
          value: 100,
        },
        {
          type: LIMIT_TYPES.AI_STORAGE,
          value: 50 * 1024 * 1024,
        },
        {
          type: LIMIT_TYPES.AI_QUERIES,
          value: 10,
        },
        {
          type: LIMIT_TYPES.AI_SHARED_QUERIES,
          value: 5,
        },
        {
          type: LIMIT_TYPES.AI_NOTETAKER_RECORDING,
          value: 300 * 60,
        },
      ],
    },
    {
      type: "pro",
      name: "Pro",
      price: 39,
      allowedFeatures: [paidFeatures.aiAssistants, paidFeatures.aiNotetaker],
      limits: [
        {
          type: LIMIT_TYPES.MAX_AI_USERS,
          value: 100,
        },
        {
          type: LIMIT_TYPES.AI_STORAGE,
          value: 500 * 1024 * 1024,
        },
        {
          type: LIMIT_TYPES.AI_QUERIES,
          value: 100,
        },
        {
          type: LIMIT_TYPES.AI_SHARED_QUERIES,
          value: 50,
        },
        {
          type: LIMIT_TYPES.AI_NOTETAKER_RECORDING,
          value: 44640 * 60,
        },
      ],
    },
    {
      type: "enterprise",
      name: "Enterprise",
      price: -1,
      allowedFeatures: [paidFeatures.aiAssistants, paidFeatures.aiNotetaker],
      limits: [
        {
          type: LIMIT_TYPES.MAX_AI_USERS,
          value: "-1",
        },
        {
          type: LIMIT_TYPES.AI_STORAGE,
          value: "-1",
        },
        {
          type: LIMIT_TYPES.AI_QUERIES,
          value: "-1",
        },
        {
          type: LIMIT_TYPES.AI_SHARED_QUERIES,
          value: "-1",
        },
        { type: LIMIT_TYPES.AI_NOTETAKER_RECORDING, value: "-1" },
      ],
    },
  ],
  [PRODUCT_TYPES.THREE_D]: [
    {
      type: "pro",
      name: "Pro",
      price: 49,
      allowedFeatures: [paidFeatures._3DModels, paidFeatures._3DGuides],
      limits: [],
    },
    {
      type: "enterprise",
      name: "Enterprise",
      price: -1,
      allowedFeatures: [paidFeatures._3DModels, paidFeatures._3DGuides],
      limits: [],
    },
  ],
  [PRODUCT_TYPES.CMMS]: [
    {
      type: "enterprise",
      name: "Enterprise",
      price: -1,
      allowedFeatures: [
        paidFeatures.procedures,
        paidFeatures.preventiveMaintenance,
        paidFeatures.emails,
        paidFeatures.scheduler,
        paidFeatures.calendarSync,
        paidFeatures.analytics,
        paidFeatures.activityLogging,
        paidFeatures.teams,
      ],
      limits: [],
      conflictingPurchases: [PRODUCT_TYPES.FIELD_SERVICE],
    },
  ],
  [PRODUCT_TYPES.FIELD_SERVICE]: [
    {
      type: "enterprise",
      name: "Enterprise",
      price: -1,
      allowedFeatures: [
        paidFeatures.procedures,
        paidFeatures.preventiveMaintenance,
        paidFeatures.emails,
        paidFeatures.scheduler,
        paidFeatures.calendarSync,
        paidFeatures.analytics,
        paidFeatures.activityLogging,
        paidFeatures.teams,
      ],
      limits: [],
      conflictingPurchases: [PRODUCT_TYPES.CMMS],
    },
  ],
};

export const CONFLICTING_PURCHASES = {
  [PRODUCT_TYPES.CMMS]: [PRODUCT_TYPES.FIELD_SERVICE],
  [PRODUCT_TYPES.FIELD_SERVICE]: [PRODUCT_TYPES.CMMS],
};
