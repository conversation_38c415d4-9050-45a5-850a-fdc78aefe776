name: Deploy Pubnub Sync Membership
on:
  push:
    branches:
      - develop
      - main
      - dev
    paths:
      - "functions/pubnub-sync-membership/**"
jobs:
  deploy:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: functions/pubnub-sync-membership
    environment:
      name: ${{ (github.ref == 'refs/heads/main' && 'Release') || (github.ref == 'refs/heads/staging' && 'Staging') || (github.ref == 'refs/heads/develop' && 'Alpha') }}
    strategy:
      matrix:
        node-version: [16.x]
    steps:
      - name: Get Code
        uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Clean install dependencies and test
        run: |
          npm ci
          npm run test

      - name: build
        run: npm run build

      - name: deploy
        run: aws lambda update-function-code --function-name="${{ vars['LAMBDA_PUBNUB_SYNC_MEMBERSHIP']}}" --region="${{ vars.AWS_REGION }}" --zip-file=fileb://dist.zip
