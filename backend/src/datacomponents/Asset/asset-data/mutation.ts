import { ExecutionContext } from "graphql/execution/execute";
import { features, roles } from "~/directives";

import {
  InputCreateOwnOemAsset,
  InputUpdateOwnOemAsset,
  InputHandleOwnOemAssetQrAccess,
  InputAssignOwnOemMultipleAssetsToOwnOemCustomer,
  InputAssignMultipleAssetsToTeam,
  InputUnassignAssetFromTeam,
  InputRemoveOwnOemAssetFromOwnOemCustomer,
  InputAssignAssetInventoryParts,
  InputRemoveAssetInventoryPart,
  InputAssignAssetsToParent,
  InputUnassignAssetFromParent,
  InputResetAssetToTemplate,
} from "~/graphql/types";
import { createAsset } from "~/services/asset/create";
import { deleteAsset, deleteAsset3DModel, deleteAssetImage } from "~/services/asset/delete";
import {
  addAsset3DModel,
  assignAssetsToCustomer,
  assignAssetsToParent,
  assignMultipleAssetsToTeam,
  assignPartsToAsset,
  detachAssetDocumentation,
  handleOwnOemAssetQRAccess,
  removeAssetFromCustomer,
  removePartsFromAsset,
  resetOwnOemAssetToTemplate,
  unassignAssetFromParent,
  unassignAssetFromTeam,
  updateAsset,
} from "~/services/asset/update";
import { IContext } from "~/types/common";

const { hierarchy } = features.oemOwns;

export const mutationTypes = `#graphql
  type Mutation {
    addOwnOemAsset3DModel(_id: ID!, _3dModelUrl: String!, _3dModelPartUrls: [String!]): Asset @${roles.is.technician}
    assignAssetsToParent(input: InputAssignAssetsToParent!): Asset @${hierarchy} @${roles.is.staff}
    assignMultipleAssetsToTeam(input: InputAssignMultipleAssetsToTeam!): [Asset] @${roles.is.staff}
    assignOwnOemInventoryPartsToAsset(input: InputAssignAssetInventoryParts): Asset @${roles.is.staff}
    assignOwnOemMultipleAssetsToOwnOemCustomer(input: InputAssignOwnOemMultipleAssetsToOwnOemCustomer!): [Asset] @${roles.is.staff}
    createOwnOemAsset(input: InputCreateOwnOemAsset): Asset @${roles.is.staff}
    deleteOwnOemAsset(assetId: ID!): String @${roles.is.staff}
    deleteOwnOemAsset3DModel(assetId: ID!): Asset @${roles.is.technician}
    deleteOwnOemAssetImage(assetId: ID!): Asset @${roles.is.staff}
    handleOwnOemAssetQRAccess(input: InputHandleOwnOemAssetQRAccess): Asset @${roles.is.staff}
    removeOwnOemAssetFromOwnOemCustomer(input: InputRemoveOwnOemAssetFromOwnOemCustomer!): Asset @${roles.is.staff}
    removeOwnOemInventoryPartFromAsset(input: InputRemoveAssetInventoryPart): Asset @${roles.is.staff}
    unassignAssetFromParent(input: InputUnassignAssetFromParent): Asset @${hierarchy} @${roles.is.staff}
    unassignAssetFromTeam(input: InputUnassignAssetFromTeam): Asset @${roles.is.staff}
    updateOwnOemAsset(input: InputUpdateOwnOemAsset): Asset @${roles.is.staff}
    detachOwnOemAssetDocumentation(_id: ID!): Asset @${roles.is.staff}
    resetOwnOemAssetToTemplate(input: InputResetAssetToTemplate): Asset @${roles.is.staff}
  }
`;

export const mutationResolvers = {
  Mutation: {
    addOwnOemAsset3DModel: async (
      _: ExecutionContext["contextValue"],
      args: { _id: string; _3dModelUrl: string; _3dModelPartUrls: string[] },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await addAsset3DModel({
        args: {
          input: {
            _id: args._id,
            _3dModelUrl: args._3dModelUrl,
            _3dModelPartUrls: args._3dModelPartUrls,
          },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),

    assignAssetsToParent: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputAssignAssetsToParent },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await assignAssetsToParent({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    assignMultipleAssetsToTeam: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputAssignMultipleAssetsToTeam },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await assignMultipleAssetsToTeam({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    assignOwnOemInventoryPartsToAsset: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputAssignAssetInventoryParts },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await assignPartsToAsset({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        user,
        dataSources,
        translate,
      }),

    assignOwnOemMultipleAssetsToOwnOemCustomer: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputAssignOwnOemMultipleAssetsToOwnOemCustomer },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await assignAssetsToCustomer({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    createOwnOemAsset: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputCreateOwnOemAsset },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await createAsset({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    deleteOwnOemAsset: async (
      _: ExecutionContext["contextValue"],
      args: { assetId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await deleteAsset({
        args: {
          input: {
            assetId: args.assetId,
          },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),

    deleteOwnOemAsset3DModel: async (
      _: ExecutionContext["contextValue"],
      args: { assetId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await deleteAsset3DModel({
        args: {
          input: {
            assetId: args.assetId,
          },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),

    deleteOwnOemAssetImage: async (
      _: ExecutionContext["contextValue"],
      args: { assetId: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await deleteAssetImage({
        args: {
          input: {
            assetId: args.assetId,
          },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),

    detachOwnOemAssetDocumentation: async (
      _: ExecutionContext["contextValue"],
      args: { _id: string },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      detachAssetDocumentation({
        // @ts-ignore
        args: { input: { _id: args._id } },
        dataSources,
        user,
        translate,
      }),

    handleOwnOemAssetQRAccess: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputHandleOwnOemAssetQrAccess },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await handleOwnOemAssetQRAccess({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    removeOwnOemAssetFromOwnOemCustomer: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputRemoveOwnOemAssetFromOwnOemCustomer },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await removeAssetFromCustomer({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    removeOwnOemInventoryPartFromAsset: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputRemoveAssetInventoryPart },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await removePartsFromAsset({
        args: {
          input: { assetId: args?.input?.assetId, parts: [args?.input?.partId] },
          files: null,
          headers: {},
          query: {},
          params: {},
        },
        dataSources,
        user,
        translate,
      }),

    resetOwnOemAssetToTemplate: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputResetAssetToTemplate },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      resetOwnOemAssetToTemplate({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    unassignAssetFromParent: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputUnassignAssetFromParent },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await unassignAssetFromParent({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    unassignAssetFromTeam: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputUnassignAssetFromTeam },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await unassignAssetFromTeam({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),

    updateOwnOemAsset: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputUpdateOwnOemAsset },
      { dataSources, user, req: { t: translate } }: IContext,
    ) =>
      await updateAsset({
        args: { input: args.input, files: null, headers: {}, query: {}, params: {} },
        dataSources,
        user,
        translate,
      }),
  },
};
