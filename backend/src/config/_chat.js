import PubNub from "pubnub";
import createUuidFromSeed from "uuid-by-string";
import APP from "~/config/_app";

const uuid = createUuidFromSeed(APP.NAMESPACE);
const pubnub = new PubNub({
  subscribeKey: process.env.CHAT_SUBSCRIBE_KEY,
  publishKey: process.env.CHAT_PUBLISH_KEY,
  uuid,
  secretKey: process.env.CHAT_SECRET_KEY,
});

export default pubnub;

// console.log(`[CHAT][INFO][UUID]: ${uuid}`);
