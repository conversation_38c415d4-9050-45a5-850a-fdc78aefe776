import { describe, afterEach, expect, test, vi } from "vitest";
import { Types } from "mongoose";
import { queryResolvers } from "./_query";
import { IUser } from "~/datamodels/User/interface";

const { Query } = queryResolvers;

const OEM_ID = new Types.ObjectId("6499a52b5dfe76648ff67a81");
const ORGANIZATION_ID = OEM_ID;

const user: IUser = {
  organization: ORGANIZATION_ID,
  username: "User",
};

const dataSources = {
  ActivityLog: {
    getMany: vi.fn(),
    totalCount: vi.fn(),
  },
};

describe("Query ActivityLog ", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("listOwnOemActivityLogs", async () => {
    test("get activityLogs", async () => {
      const totalCount = 1000;
      const logs = [
        {
          _id: "64d1db10aa1d4f0613a9d7d7",
        },
        {
          _id: "64d1db10aa1d4f0613a9d7d8",
        },
      ];
      dataSources.ActivityLog.getMany.mockResolvedValueOnce(logs);
      dataSources.ActivityLog.totalCount.mockResolvedValueOnce(totalCount);
      const result = await Query.listOwnOemActivityLogs(
        null,
        // @ts-ignore
        {
          params: {
            limit: 10,
            skip: 1,
          },
        },
        {
          dataSources,
          user,
        },
      );
      expect(dataSources.ActivityLog.getMany).toHaveBeenCalledOnce();
      expect(dataSources.ActivityLog.getMany).toHaveBeenCalledWith(
        expect.objectContaining({
          oem: user.organization,
          limit: 10,
          skip: 1,
          sort: ["createdAt:desc", "_id:asc"],
        }),
      );

      expect(dataSources.ActivityLog.totalCount).toHaveBeenCalledOnce();
      expect(dataSources.ActivityLog.totalCount).toHaveBeenCalledWith(
        expect.objectContaining({
          oem: user.organization,
          limit: -1,
          skip: 0,
          sort: ["createdAt:desc", "_id:asc"],
        }),
      );

      expect(result).toStrictEqual(
        expect.objectContaining({
          logs,
          limit: 10,
          skip: 1,
          totalCount,
        }),
      );
    });
  });
});
