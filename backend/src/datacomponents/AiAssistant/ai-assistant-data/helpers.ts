import { Job } from "agenda";
import { ForbiddenError } from "apollo-server-express";
import { unlinkSync as deleteLocalFile } from "fs";
import { Types } from "mongoose";
import PAID_FEATURES from "$/settings/paid-features.json";
import { DE_INDEX_AI_ASSISTANT_FILE_JOB, INDEX_AI_ASSISTANT_FILE_JOB } from "~/agenda/constants";
import { NOTIFICATION_IDENTIFIERS } from "~/constants/notification-identifiers";
import { IBoxFile, IBoxBasic } from "~/datasources/api/box/interface";
import {
  DocumentStatus as AiAssistantDocumentStatus,
  ErrorReasons,
  SUPPORTED_FILE_EXTENSIONS,
} from "~/datamodels/AiAssistant/constants";
import { IAiAssistant, IAiAssistantDocument } from "~/datamodels/AiAssistant/interface";
import { IMachine } from "~/datamodels/Machine/interface";
import { AiAssistantDefaultConfigurationValues } from "~/datamodels/Oem/constants";
import { IOem } from "~/datamodels/Oem/interface";
import { IAiAssistantConfiguration, IUser } from "~/datamodels/User/interface";
import serverEnvironmentTag from "~/environment/_server-tag";
import { IContext } from "~/types/common";
import { getEnums } from "~/utils/_get-enums";
import { getOemChannelId } from "~/utils/chat";
import logger from "~/utils/logger";
import { MACHINE_DOCUMENTATION_STATUSES } from "~/constants/machine";
import isPaidFeatureAvailable, {
  getOemAIQuota,
  userHasAIAccess,
} from "~/utils/isPaidFeatureAvailable";
import type { IMachineTemplate } from "~/datamodels/MachineTemplate/interface";
import type { IContact } from "~/datamodels/Contact/interface";
import { CONTACT_ACCESS_STATUS } from "~/constants/contacts";

const paidFeaturesEnum = getEnums(PAID_FEATURES, "reference");

export const createIndexingOperationProgressMessage = ({
  aiAssistantID,
  consumedStorage,
  documentID,
  externalStorageServiceDocumentID,
  key,
  oemID,
  status,
  reason,
}: {
  aiAssistantID: string;
  consumedStorage?: IAiAssistantDocument["consumedStorage"];
  documentID: string;
  externalStorageServiceDocumentID?: IAiAssistantDocument["externalStorageServiceDocumentID"];
  key: string;
  oemID: string;
  reason?: string;
  status: IAiAssistantDocument["status"];
}) => ({
  message: {
    aiAssistantID,
    consumedStorage,
    documentID,
    externalStorageServiceDocumentID,
    key,
    status,
    reason,
    text: NOTIFICATION_IDENTIFIERS.AI_ASSISTANT_INDEXING_PROGRESS,
  },
  channel: getOemChannelId(oemID),
});

export const removeDocument = async (
  dataSources: IContext["dataSources"],
  {
    attrs: { data },
  }: Job<{
    aiAssistantID: string;
    externalStorageServiceDocumentID: IAiAssistantDocument["externalStorageServiceDocumentID"];
    fileID: string;
    key: string;
    oemID: string;
    triggeredFromWebhook?: boolean;
  }>,
) => {
  if (!data?.aiAssistantID || !data?.fileID || !data?.key || !data?.oemID)
    throw new Error(
      `[AGENDA][${INDEX_AI_ASSISTANT_FILE_JOB}] Not enough data for this operation. Required keys aiAssistantID, fileID, key, oemID. Received data: ${JSON.stringify(
        data,
      )}`,
    );

  const aiAssistantDocumentID = new Types.ObjectId(data.fileID);
  const aiAssistantID = new Types.ObjectId(data.aiAssistantID);
  const oemID = new Types.ObjectId(data.oemID);

  const [aiAssistant, oem] = await Promise.all([
    dataSources.AiAssistant.loadOne(aiAssistantID) as Promise<IAiAssistant>,
    dataSources.Oem.loadOne(oemID) as Promise<IOem>,
  ]);

  if (!(aiAssistant && oem))
    throw new Error(
      `[AGENDA][${INDEX_AI_ASSISTANT_FILE_JOB}] Couldn't find either Ai Assistant or Oem against data "${data.aiAssistantID}", "${data.oemID}"`,
    );

  const aiAssistantDocument =
    aiAssistant.documents?.internalDocuments.find(
      internalDocument => internalDocument._id?.toString() === data.fileID,
    ) ||
    aiAssistant.documents?.externalDocuments.find(
      externalDocument => externalDocument._id?.toString() === data.fileID,
    );

  if (!aiAssistantDocument)
    throw new Error(`Couldn't find file ${data.fileID} for assistant ${data.aiAssistantID}`);

  const maybeUpdatedAiAssistant =
    await dataSources.AiAssistant.AiAssistant.findOneAndUpdate<IAiAssistant>(
      {
        _id: aiAssistantID,
        [`documents.${data.key}`]: {
          $elemMatch: {
            _id: aiAssistantDocumentID,
            status: AiAssistantDocumentStatus.INITIATED_DEINDEXING,
          },
        },
      },
      {
        // @ts-ignore
        $pull: {
          [`documents.${data.key}`]: { _id: aiAssistantDocumentID },
        },
      },
      { new: true },
    );

  if (!maybeUpdatedAiAssistant)
    throw new Error(
      `Document ${data.fileID} for Ai Assistant ${data.aiAssistantID} was not scheduled for de-indexing`,
    );

  try {
    await dataSources.VectaraApi.deleteDocument(aiAssistant._id, aiAssistantDocument._id);
  } catch (err) {
    // @ts-ignore
    if (err?.extensions?.response?.status !== 404) {
      await dataSources.AiAssistant.AiAssistant.findOneAndUpdate<IAiAssistant>(
        {
          _id: aiAssistantID,
          [`documents.${data.key}`]: { $not: { $elemMatch: { _id: aiAssistantDocumentID } } },
        },
        {
          // @ts-ignore
          $push: {
            [`documents.${data.key}`]: {
              ...aiAssistantDocument,
              status: AiAssistantDocumentStatus.DE_INDEXING_FAILED,
            },
          },
        },
      );
      throw err;
    }
  }

  // If triggered from webhook, find users linked to the machine and update storage
  const userToUpdate: Types.ObjectId = aiAssistant.createdBy;
  const creator = await dataSources.User.User.findOne({ _id: userToUpdate, oem: oemID });

  if (!creator) throw new ForbiddenError("Creator not found.");

  const currentConsumedStorage = creator.aiAssistantConfiguration?.consumedStorage ?? 0;
  const storageToDeduct = aiAssistantDocument.consumedStorage;

  const safeDeduction = Math.min(currentConsumedStorage, storageToDeduct);

  await Promise.all([
    dataSources.User.updateOne(
      { _id: userToUpdate },
      {
        $inc: {
          "aiAssistantConfiguration.consumedStorage": -safeDeduction,
        },
      },
    ),
    !data.triggeredFromWebhook
      ? dataSources.PubnubApi.publishMessage(
          createIndexingOperationProgressMessage({
            aiAssistantID: data.aiAssistantID,
            consumedStorage: 0,
            documentID: data.fileID,
            externalStorageServiceDocumentID: data.externalStorageServiceDocumentID,
            key: data.key,
            oemID: data.oemID,
            status: AiAssistantDocumentStatus.DE_INDEXED,
          }),
        )
      : Promise.resolve(), // No-op if webhook triggered
  ]);
};

export const getAllFolderIDs = async (
  dataSources: IContext["dataSources"],
  parentFolderID: string,
  oemID: Types.ObjectId,
) => {
  const boxOemUserName = `${oemID.toString()}${serverEnvironmentTag}`;
  const folderIDs: string[] = [];

  const fetchFolderIDs = async (folderID: string) => {
    const folder = await dataSources.BoxApi.getFolderById({
      boxOemUserName,
      id: folderID,
    });

    folderIDs.push(folderID);

    for (const entry of folder?.item_collection.entries || []) {
      if (entry.type === "folder") {
        await fetchFolderIDs(entry.id);
      }
    }
  };

  await fetchFolderIDs(parentFolderID);

  return folderIDs;
};

export const getFoldersFiles = async (
  dataSources: IContext["dataSources"],
  externalFolderID: string,
  internalFolderID: string,
  oemID: Types.ObjectId,
) => {
  const boxOemUserName = `${oemID.toString()}${serverEnvironmentTag}`;

  const fetchAllFilesFromFolder = async (folderID: string): Promise<Array<IBoxFile>> => {
    const folder = await dataSources.BoxApi.getFolderById({
      boxOemUserName,
      id: folderID,
    });

    const files: Array<IBoxFile> = [];
    const subFolders: Array<IBoxBasic> = [];

    folder?.item_collection.entries.forEach((entry: IBoxBasic) => {
      if (
        entry.type === "file" &&
        SUPPORTED_FILE_EXTENSIONS[String(entry.name.split(".").at(-1)).toLowerCase()]
      ) {
        files.push(entry as IBoxFile);
      } else if (entry.type === "folder") {
        subFolders.push(entry);
      }
    });

    for (const subFolder of subFolders) {
      const subFolderFiles = await fetchAllFilesFromFolder(subFolder.id);
      files.push(...subFolderFiles);
    }

    return files;
  };

  const [externalDocuments, internalDocuments] = await Promise.all([
    fetchAllFilesFromFolder(externalFolderID),
    fetchAllFilesFromFolder(internalFolderID),
  ]);

  return { externalDocuments, internalDocuments };
};

export const getMultipleFiles = (
  dataSources: IContext["dataSources"],
  fileIds: Array<string>,
  oemID: Types.ObjectId,
) =>
  Promise.all<IBoxFile>(
    fileIds.map(fileID =>
      dataSources.BoxApi.getFileById({
        boxOemUserName: `${oemID.toString()}${serverEnvironmentTag}`,
        fileID,
        options: { fields: "parent,size" },
      }),
    ),
  );

export const isSelfCreated = (
  user: IUser | string,
  document: { createdBy: string },
  checkById = false,
): boolean => {
  if (!document || !user) return false;

  return checkById
    ? user === document.createdBy.toString()
    : (user as IUser).id?.toString() === document?.createdBy?.toString();
};

export const initiateDocumentOperations = async (
  dataSources: IContext["dataSources"],
  {
    attrs: { data },
  }: Job<{
    aiAssistantID: string;
    externalStorageServiceDocumentID: IAiAssistantDocument["externalStorageServiceDocumentID"];
    fileID: string;
    key: string;
    oemID: string;
    throwPubNubErrorOnStorageExceeded: boolean;
  }>,
) => {
  if (!(data?.aiAssistantID && data?.fileID && data?.key && data?.oemID))
    throw new Error(
      `[AGENDA][${INDEX_AI_ASSISTANT_FILE_JOB}] Not enough data for this operation. Required keys aiAssistantID, fileID, key, oemID:, Recieved data ${JSON.stringify(
        data,
      )}`,
    );

  const aiAssistantID = new Types.ObjectId(data.aiAssistantID);
  const oemID = new Types.ObjectId(data.oemID);
  const aiAssistant: IAiAssistant = await dataSources.AiAssistant.loadOne(aiAssistantID);

  const resourceId = aiAssistant.machineID || aiAssistant.templateId;
  const isAsset = !!aiAssistant.machineID;
  const resourceName = isAsset ? "asset" : "template";

  const [resource, oem, creator]: [IMachine | IMachineTemplate, IOem, IUser] = await Promise.all([
    isAsset
      ? dataSources.Machine.loadOne(resourceId)
      : dataSources.MachineTemplate.loadOne(resourceId),
    dataSources.Oem.loadOne(oemID),
    dataSources.User.loadOne(aiAssistant.createdBy),
  ]);

  if (!(aiAssistant && resource && creator && oem))
    throw new Error(
      `[AGENDA][${INDEX_AI_ASSISTANT_FILE_JOB}] Couldn't find either Ai Assistant, ${resourceName}, oem or creator against data "${data.aiAssistantID}", "${resourceId}", "${data.oemID}"`,
    );

  if (
    isAsset &&
    "template" in resource &&
    resource.template &&
    (resource.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.DETACHING ||
      resource.detachedFromTemplate?.documentation === MACHINE_DOCUMENTATION_STATUSES.ATTACHING)
  ) {
    return null;
  }

  const aiAssistantDocument =
    aiAssistant.documents?.internalDocuments.find(
      internalDocument => internalDocument._id?.toString() === data.fileID,
    ) ||
    aiAssistant.documents?.externalDocuments.find(
      internalDocument => internalDocument._id?.toString() === data.fileID,
    );
  const boxOemUserName = `${resource.oem!.toString()}${serverEnvironmentTag}`;

  if (!aiAssistantDocument)
    throw new Error(
      `Couldn't find file ${data.fileID} for assistant ${data.aiAssistantID} of ${resourceName} ${resourceId}`,
    );

  let callbackPromise: Promise<any> = new Promise(() => {});

  const fileSize: number = (
    await dataSources.BoxApi.getFileById({
      boxOemUserName,
      fileID: aiAssistantDocument.externalStorageServiceDocumentID,
      options: { fields: "size" },
    })
  ).size;
  const targetPath = `${__dirname}/${aiAssistantDocument.externalStorageServiceDocumentID}`;

  const oemAllowedAiQuota = await getOemAIQuota(dataSources, oem);

  const allowedStorage =
    oemAllowedAiQuota.allowedStorage || AiAssistantDefaultConfigurationValues.allowedStorage;
  const consumedStorage = creator?.aiAssistantConfiguration?.consumedStorage || 0;

  if (consumedStorage + fileSize > allowedStorage) {
    if (data.throwPubNubErrorOnStorageExceeded) {
      await dataSources.PubnubApi.publishMessage(
        createIndexingOperationProgressMessage({
          aiAssistantID: data.aiAssistantID,
          consumedStorage,
          documentID: data.fileID,
          externalStorageServiceDocumentID: data.externalStorageServiceDocumentID,
          key: data.key,
          oemID: data.oemID,
          reason: ErrorReasons.DATA_LIMIT_EXCEEDED,
          status: AiAssistantDocumentStatus.INDEXING_FAILED,
        }),
      );
      return;
    }
    throw new Error(
      `Can't index file ${
        data.fileID
      } of size ${fileSize} as it will exceed the remaining quota of ${
        allowedStorage - consumedStorage
      }`,
    );
  }

  await dataSources.BoxApi.downloadFileById({
    callback: () =>
      (callbackPromise = dataSources.VectaraApi.uploadFileToAiAssistant(
        data.fileID,
        aiAssistantID,
        targetPath,
      )
        .then(async response => {
          logger.info(
            `Uploaded file ${aiAssistantDocument.externalStorageServiceDocumentID} on corpora ${data.aiAssistantID}`,
          );
          const maybeUpdatedAiAssistant =
            await dataSources.AiAssistant.AiAssistant.findOneAndUpdate<IAiAssistant>(
              {
                _id: aiAssistantID,
                [`documents.${data.key}`]: {
                  $elemMatch: {
                    _id: data.fileID,
                    status: AiAssistantDocumentStatus.INITIATED_INDEXING,
                  },
                },
              },
              {
                $set: {
                  [`documents.${data.key}.$[element].status`]: AiAssistantDocumentStatus.INDEXED,
                  [`documents.${data.key}.$[element].consumedStorage`]:
                    response.storage_usage.bytes_used,
                },
              },
              {
                arrayFilters: [
                  {
                    "element._id": data.fileID,
                  },
                ],
                new: true,
              },
            );

          if (!maybeUpdatedAiAssistant)
            throw new Error(`Document ${aiAssistantID} wasn't scheduled for indexing`);

          return await Promise.all([
            dataSources.User.save({
              _id: creator._id,
              $inc: {
                "aiAssistantConfiguration.consumedStorage": response.storage_usage.bytes_used,
              },
            }),
            dataSources.PubnubApi.publishMessage(
              createIndexingOperationProgressMessage({
                aiAssistantID: data.aiAssistantID,
                consumedStorage: response.storage_usage.bytes_used,
                documentID: data.fileID,
                externalStorageServiceDocumentID: data.externalStorageServiceDocumentID,
                key: data.key,
                oemID: data.oemID,
                status: AiAssistantDocumentStatus.INDEXED,
              }),
            ),
          ]);
        })
        .catch((err: Error) => {
          logger.error(err);
          if (err?.message === `Document ${aiAssistantID} wasn't scheduled for indexing`)
            try {
              dataSources.VectaraApi.deleteDocument(aiAssistantID, new Types.ObjectId(data.fileID));
            } catch (e) {
              logger.error(e);
            }
          else throw err;
        })
        .finally(() => deleteLocalFile(targetPath))),
    boxOemUserName,
    fileID: aiAssistantDocument.externalStorageServiceDocumentID,
    targetPath,
  });
  await callbackPromise;
};

export const onDocumentOperationFailure = async (
  dataSources: IContext["dataSources"],
  _: any,
  {
    attrs: { data, name },
  }: Job<{
    aiAssistantID: string;
    externalStorageServiceDocumentID: IAiAssistantDocument["externalStorageServiceDocumentID"];
    fileID: string;
    key: string;
    oemID: string;
    triggeredFromWebhook?: boolean;
  }>,
) => {
  if (!data) return;

  const expectedStatus =
    name === DE_INDEX_AI_ASSISTANT_FILE_JOB
      ? AiAssistantDocumentStatus.INITIATED_DEINDEXING
      : AiAssistantDocumentStatus.INITIATED_INDEXING;
  const status =
    name === DE_INDEX_AI_ASSISTANT_FILE_JOB
      ? AiAssistantDocumentStatus.DE_INDEXING_FAILED
      : AiAssistantDocumentStatus.INDEXING_FAILED;

  return await Promise.all([
    dataSources.AiAssistant.AiAssistant.findOneAndUpdate<IAiAssistant>(
      {
        _id: data.aiAssistantID,
        [`documents.${data.key}`]: {
          $elemMatch: {
            _id: data.fileID,
            status: expectedStatus,
          },
        },
      },
      {
        $set: {
          [`documents.${data.key}.$[element].status`]: status,
        },
      },
      {
        arrayFilters: [
          {
            "element._id": data.fileID,
          },
        ],
        new: true,
      },
    ),
    dataSources.PubnubApi.publishMessage(
      createIndexingOperationProgressMessage({
        aiAssistantID: data.aiAssistantID,
        documentID: data.fileID,
        externalStorageServiceDocumentID: data.externalStorageServiceDocumentID,
        key: data.key,
        oemID: data.oemID,
        status,
      }),
    ),
  ]);
};

export const validateAccess = async (
  dataSources: IContext["dataSources"],
  oemID: string,
  userID: string,
  checkQueryQuotaValidity = true,
  checkStorageQuotaValidity = true,
) => {
  const [oem, user] = await Promise.all([
    dataSources.Oem.loadOne(new Types.ObjectId(oemID)) as Promise<IOem>,
    dataSources.User.loadOne(new Types.ObjectId(userID)) as Promise<IUser>,
  ]);

  const [isAIPaid, isAIUser] = await Promise.all([
    // @ts-ignore
    isPaidFeatureAvailable(dataSources, paidFeaturesEnum.aiAssistants, oem),
    // @ts-ignore
    await userHasAIAccess(user),
  ]);

  // Validate basic access to AI Assistant feature
  if (
    !(
      user &&
      oem &&
      // @ts-ignore
      isAIPaid &&
      // @ts-ignore
      isAIUser
    )
  ) {
    throw new ForbiddenError(
      `User ${userID} doesn't have access to AI Assistant feature inside organization "${oemID}"`,
    );
  }

  // Validate user-specific query and storage limits
  if (checkQueryQuotaValidity || checkStorageQuotaValidity) {
    const { aiAssistantConfiguration = {} } = user;
    const { consumedQueries = 0, consumedStorage = 0 } =
      aiAssistantConfiguration as IAiAssistantConfiguration;

    const oemAllowedAiQuota = await getOemAIQuota(dataSources, oem);

    const { allowedQueries, allowedStorage } = oemAllowedAiQuota;

    // @ts-ignore
    if (
      checkQueryQuotaValidity &&
      (!aiAssistantConfiguration || consumedQueries >= allowedQueries!)
    ) {
      throw new ForbiddenError(
        `User ${userID} has exceeded their query quota for AI Assistants. Allowed: ${allowedQueries}, Used: ${consumedQueries}`,
      );
    }

    // @ts-ignore
    if (
      checkStorageQuotaValidity &&
      (!aiAssistantConfiguration || consumedStorage >= allowedStorage!)
    ) {
      throw new ForbiddenError("Data storage quota for AI Assistants exceeded");
    }
  }

  return { oem, user };
};

export const validateAccessToMachine = async (
  dataSources: IContext["dataSources"],
  machineID: string,
  user: IContext["user"],
) => {
  const [machine, oemUser] = await Promise.all([
    dataSources.Machine.loadOne(new Types.ObjectId(machineID)) as Promise<IMachine>,
    dataSources.User.loadOne(new Types.ObjectId(user.id)) as Promise<IUser>,
  ]);

  let isSharedAsset = false;

  if (machine.oem?.toString() !== user.organization?.toString()) {
    const correspondingContact: IContact = await dataSources.Contact.getOne({
      user: user.id,
      connection: machine.customer,
      accessStatus: CONTACT_ACCESS_STATUS.ACTIVE,
    });
    if (!correspondingContact)
      throw new ForbiddenError("User does not have access to this organization's assets");
    isSharedAsset = true;
  }

  const machineTeams = machine.teams?.map(t => t.toString());
  if (
    !isSharedAsset &&
    user.teams &&
    user.teams?.length > 0 &&
    !user.teams.some(team => machineTeams!.includes(team.toString()))
  )
    throw new ForbiddenError(`User "${user.id}" doesn't have access to Asset "${machineID}"`);

  return { machine, oemUser, isSharedAsset };
};

export const validateAccessToMachineTemplate = async (
  dataSources: IContext["dataSources"],
  templateID: string,
  user: IContext["user"],
) => {
  const [template, oemUser] = await Promise.all([
    dataSources.MachineTemplate.loadOne(
      new Types.ObjectId(templateID),
    ) as Promise<IMachineTemplate>,
    dataSources.User.loadOne(new Types.ObjectId(user.id)) as Promise<IUser>,
  ]);

  if (template && template.oem.toString() !== user.organization?.toString()!) {
    const assetsCreatedFromTemplate: Pick<IMachine, "customer">[] =
      await dataSources.Machine.getManyByQuery(
        {
          template: new Types.ObjectId(templateID),
          oem: new Types.ObjectId(template.oem),
          $and: [{ customer: { $exists: true } }, { customer: { $ne: null } }],
          "detachedFromTemplate.documentation": MACHINE_DOCUMENTATION_STATUSES.ATTACHED,
          deleted: false,
        },
        { customer: 1 },
      );
    const correspondingContact: IContact = await dataSources.Contact.getOne({
      user: user.id,
      connection: { $in: assetsCreatedFromTemplate?.map(asset => asset.customer) },
      accessStatus: CONTACT_ACCESS_STATUS.ACTIVE,
    });
    if (!correspondingContact)
      throw new ForbiddenError(`User "${user.id}" doesn't have access to Asset "${templateID}"`);
  }

  if (!template)
    throw new ForbiddenError(`User "${user.id}" doesn't have access to Asset "${templateID}"`);

  return { template, oemUser, isSharedAsset: false };
};

export const getAiAssistantWithValidations = async (
  aiAssistantID: string,
  dataSources: IContext["dataSources"],
  user: IContext["user"],
  checkQueryQuotaValidity = true,
  checkStorageQuotaValidity = true,
  allowedSharedAssetAccess = false,
) => {
  const _aiAssistantID = new Types.ObjectId(aiAssistantID);

  const [aiAssistant] = await Promise.all([
    dataSources.AiAssistant.loadOne(_aiAssistantID) as Promise<IAiAssistant>,
    validateAccess(
      dataSources,
      user.organization?.toString()!,
      user.id?.toString()!,
      checkQueryQuotaValidity,
      checkStorageQuotaValidity,
    ),
  ]);

  let machine = null;
  let isSharedAsset = false;
  let template = null;

  if (aiAssistant.machineID) {
    const machineCheck = await validateAccessToMachine(
      dataSources,
      aiAssistant.machineID!.toString(),
      user,
    );
    machine = machineCheck.machine;
    isSharedAsset = machineCheck.isSharedAsset;
  }

  if (aiAssistant.templateId) {
    const templateCheck = await validateAccessToMachineTemplate(
      dataSources,
      aiAssistant.templateId!.toString(),
      user,
    );
    template = templateCheck.template;
    isSharedAsset = templateCheck.isSharedAsset;
  }

  if (machine) {
    const accessThroughSharedAsset = isSharedAsset && allowedSharedAssetAccess;

    if (
      !(aiAssistant && aiAssistant.oem.toString() === user.organization!.toString()) &&
      !accessThroughSharedAsset
    )
      throw new ForbiddenError(`User ${user.id} cannot view Assistant ${aiAssistantID}`);
  }
  if (template && template.oem.toString() !== user.organization?.toString()!) {
    isSharedAsset = true;
  }

  return { aiAssistant, machine, template, isSharedAsset };
};
