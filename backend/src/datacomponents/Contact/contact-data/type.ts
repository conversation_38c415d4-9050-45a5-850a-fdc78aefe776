import { Types } from "mongoose";
import type { IContact } from "~/datamodels/Contact/interface";
import type { IContext } from "~/types/common";
import { canResendInvite } from "~/utils/contact";

export default `#graphql
type Contact {
    _id: ID
    name: String
    email: String
    jobTitle: String
    landline: String
    phoneNumber: String
    oem: ID
    user: ID
    connection: ID
    createdBy: User
    deleted: Boolean
    updatedBy: User
    accessStatus: String
    canResendInvite: Boolean
}

type PaginatedContacts {
    totalCount: Int
    limit: Int
    skip: Int
    currentPage: Int
    contacts: [Contact]
}
`;

export const typeResolvers = {
  Contact: {
    createdBy: (
      { createdBy }: IContact,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.User.loadOne(new Types.ObjectId(createdBy)),
    updatedBy: (
      { updatedBy }: IContact,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.User.loadOne(new Types.ObjectId(updatedBy)),
    canResendInvite: async (
      { accessStatus, connection, email }: IContact,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => await canResendInvite(dataSources, accessStatus, connection, email),
  },
};
