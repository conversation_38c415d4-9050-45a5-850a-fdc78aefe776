import { Types } from "mongoose";
import type { IConnectionRequest } from "~/datamodels/ConnectionRequest/interface";
import { IUser } from "~/datamodels/User/interface";
import type { IContext } from "~/types/common";

export default `#graphql

type ConnectionRequest {
  _id: ID
  sentBy: User
  senderOem: Oem
  sentTo: Oem
  sentToContact: Contact
  sentToConnection: Customer
  sentToUser: User
  sentDate: DateTime
  status: String
  resolvedBy: User
  resolveDate: DateTime
  existingContactConnection: Customer
}
`;

export const typeResolvers = {
  ConnectionRequest: {
    resolvedBy: (
      { resolvedBy }: IConnectionRequest,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.User.loadOne(new Types.ObjectId(resolvedBy)),
    sentBy: (
      { sentBy }: IConnectionRequest,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.User.loadOne(new Types.ObjectId(sentBy)),
    sentTo: (
      { sentTo }: IConnectionRequest,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.Oem.loadOne(new Types.ObjectId(sentTo)),
    sentToUser: (
      { sentTo }: IConnectionRequest,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => dataSources.User.loadOne(new Types.ObjectId(sentTo)),
    senderOem: async (
      { sentBy }: IConnectionRequest,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      const user: IUser = await dataSources.User.loadOne(new Types.ObjectId(sentBy));
      if (user) return dataSources.Oem.loadOne(new Types.ObjectId(user?.oem));
    },
    sentToConnection: async (
      { sentToContact }: IConnectionRequest,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      if (!sentToContact) return null;
      if (sentToContact?.connection)
        return dataSources.Customer.loadOne(new Types.ObjectId(sentToContact?.connection));
    },
    existingContactConnection: async (
      { sentTo, sentBy }: IConnectionRequest,
      _: unknown,
      { dataSources }: { dataSources: IContext["dataSources"] },
    ) => {
      if (!sentBy || !sentTo) return false;
      const user = await dataSources.User.loadOne(new Types.ObjectId(sentBy));
      const contact = await dataSources.Contact.Contact.findOne({
        email: user?.email,
        oem: sentTo,
      });
      if (contact)
        return await dataSources.Customer.loadOne(new Types.ObjectId(contact?.connection));
    },
    resolveDate: ({ resolveDate }: IConnectionRequest) =>
      resolveDate ? new Date(resolveDate)?.toISOString() : null,
    sentDate: ({ sentDate }: IConnectionRequest) =>
      sentDate ? new Date(sentDate)?.toISOString() : null,
  },
};
