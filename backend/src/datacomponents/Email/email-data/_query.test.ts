// eslint-disable-next-line import/no-extraneous-dependencies
import { afterAll, afterEach, beforeAll, describe, expect, test, vi } from "vitest";
import { Types } from "mongoose";
import { queryResolvers } from "./_query";
import { IUser } from "~/datamodels/User/interface";
import { getFileUrl, sendEmailFileUrl } from "./_helper";

const { Query } = queryResolvers;

const OEM_ID = new Types.ObjectId("6499a52b5dfe76648ff67a81");

const originalEnv = process.env;
const EMAIL_FILE_PASSWORD = "*********";
const SERVER_URI = "https://www.example.com";

const user: IUser = {
  username: "admin",
  organization: OEM_ID,
};
const emailAddressId = "65129da26d06b548a7f092j8";

const oem = {
  _id: OEM_ID,
  email: {
    accounts: [
      { grantId: "ldwiqhwiduyfvujydvsa", _id: emailAddressId, emailAddress: "<EMAIL>" },
    ],
  },
  urlOem: "https://www.example.com",
};

const threadId = "1aufx8aq5f93n4thicystgznv";
const messageId = "1aufx8aq5f93n4thicystgiop";
const ticketId = "65129da26d06b548a7f571c4";

const dataSources = {
  Ticket: { getById: vi.fn(), getOne: vi.fn(), save: vi.fn(), updateOne: vi.fn() },
  Oem: {
    getById: vi.fn(),
    save: vi.fn(),
  },
  NylasApi: {
    sendEmail: vi.fn(),
    exchangeCodeForToken: vi.fn(),
    revokeToken: vi.fn(),
    generateAuthUrl: vi.fn(),
    getFolders: vi.fn(),
    getThreads: vi.fn(),
    getThread: vi.fn(),
    getMessage: vi.fn(),
    getFile: vi.fn(),
    getContacts: vi.fn(),
    getDraft: vi.fn(),
    getDrafts: vi.fn(),
  },
};

vi.mock("./_helper", async () => ({
  getFileUrl: vi.fn(),
  sendEmailFileUrl: vi.fn(),
}));

describe("Email Queries", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  beforeAll(() => {
    process.env = {
      ...originalEnv,
      EMAIL_FILE_PASSWORD,
      SERVER_URI,
    };
  });
  afterAll(() => {
    process.env = originalEnv;
  });

  describe("getEmailAuthUrl", async () => {
    test("Get email auth url", async () => {
      // @ts-ignore
      await Query.getEmailAuthUrl(null, null, { dataSources, user });
      expect(dataSources.NylasApi.generateAuthUrl).toHaveBeenCalledOnce();
    });
  });

  describe("getEmailFileUploadUrl", async () => {
    test("Get email auth url", async () => {
      const redirectUrl = "https://www.example.com/";
      const result = await Query.getEmailFileUploadUrl(
        null,
        { redirectUrl },
        // @ts-ignore
        { dataSources, user },
      );

      const [host, encryptedCode] = result ? result.split("email/file/") : [];

      expect(host).toEqual(redirectUrl);
      expect(encryptedCode).toBeTruthy();
    });
  });

  describe("downloadEmailFile", async () => {
    test("Download email file", async () => {
      const fileId = "my-file";
      const attachment = {
        file: fileId,
      };
      const uuid = "e437d6ec-07b5-4824-888f-2e5fdc097c43";
      // @ts-ignore
      sendEmailFileUrl.mockImplementation(() => ({ file: true }));
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.NylasApi.getFile.mockResolvedValueOnce(attachment);
      const result = await Query.downloadEmailFile(
        null,
        { uuid, fileId, messageId, emailAddressId },
        // @ts-ignore
        { dataSources, user },
      );
      expect(dataSources.NylasApi.getFile).toHaveBeenCalledOnce();
      expect(dataSources.NylasApi.getFile).toHaveBeenCalledWith(
        oem.email.accounts[0].grantId,
        messageId,
        fileId,
      );
      expect(sendEmailFileUrl).toHaveBeenCalledOnce();
      expect(sendEmailFileUrl).toHaveBeenCalledWith({
        attachment,
        userId: user.id as string,
        dataSources,
        oemSlug: oem.urlOem,
        uuid,
        grantId: oem.email.accounts[0].grantId,
        messageId,
      });
      expect(result).toBe("downloading file...");
    });
  });

  describe("listEmailContacts", async () => {
    test("List email contacts", async () => {
      const pageToken = "abc";
      dataSources.Oem.getById.mockResolvedValueOnce(oem);
      dataSources.NylasApi.getContacts.mockResolvedValueOnce([
        {
          contacts: [],
        },
      ]);

      // @ts-ignore
      await Query.listEmailContacts(null, { pageToken, emailAddressId }, { dataSources, user });
      expect(dataSources.NylasApi.getContacts).toHaveBeenCalledOnce();
      expect(dataSources.NylasApi.getContacts).toHaveBeenCalledWith(
        oem.email.accounts[0].grantId,
        pageToken,
        "",
      );
    });
  });
});
