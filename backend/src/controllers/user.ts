import type { Request, Response } from "express";
import { createSchedule } from "~/services/userSchedule/create";
import { controllerErrorHandler } from "~/utils/controllerErrorHandler";
import { serviceFnInputGenerator } from "~/utils/inputGenerator";

export const createScheduleController = async (req: Request, res: Response) => {
  try {
    const params = serviceFnInputGenerator(req, res);
    const id = await createSchedule(params);
    return res.status(200).json({
      message: params.translate("schedule.created"),
      data: {
        scheduleId: id,
      },
    });
  } catch (exception: any) {
    controllerErrorHandler(res, exception);
  }
};
