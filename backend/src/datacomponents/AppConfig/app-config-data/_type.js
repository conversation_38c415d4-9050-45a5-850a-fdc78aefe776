import APP_FEATURES from "$/settings/app-features.json";

const appFeatures = Object.keys(APP_FEATURES);

export const types = `
  enum AppFeaturesEnum {
    ${appFeatures.join(" ")}
  }

  type TierLimit {
    type: String
    value: Float
  }

  type PlanTier {
    name: String
    type: String
    price: Float
    allowedFeatures: [String]
    limits: [TierLimit]
  }

  type ProductPlan {
    type: String
    name: String
    tiers: [PlanTier]
    conflictingPurchases: [String]
  }

  type AppConfig {
    _id: ID
    features: [AppFeaturesEnum]
    version: String
    maintenanceOn: Boolean
    plans: [ProductPlan]
  }
`;

export const typeResolvers = {
  AppConfig: {
    features: ({ features }) => features || [],
    version: (_, __, { appName }) => process.env[`${appName}_APP_VERSION`],
  },
};
