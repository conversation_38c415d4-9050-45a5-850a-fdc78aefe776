import { GraphQLResolveInfo } from "graphql";
import type { ExecutionContext } from "graphql/execution/execute";
import { features, roles } from "~/directives";
import {
  getAllCustomerPortals,
  getCustomerPortal as _getCustomerPortal,
} from "~/services/customerPortal/fetch";
import { IContext } from "~/types/common";

const { staff } = roles.is;
const { customerPortal } = features.oemOwns;

export const queryTypes = `#graphql
  type Query {
    listCustomerPortals(params: InputQueryParams): PaginatedCustomerPortals @${staff} @${customerPortal}
    getCustomerPortal(id: ID!): CustomerPortal @${staff} @${customerPortal}
  }
`;

export const listCustomerPortals = async (
  _: ExecutionContext["contextValue"],
  args: { params: { skip: number; limit: number } },
  { dataSources, user, req: { t: translate } }: IContext,
  ___: GraphQLResolveInfo,
) =>
  await getAllCustomerPortals({
    args: { input: {}, params: args.params, headers: {}, files: null, query: {} },
    dataSources,
    user,
    translate,
  });

export const getCustomerPortal = async (
  _: ExecutionContext["contextValue"],
  { id }: { id: string },
  { dataSources, user, req: { t: translate } }: IContext,
  ___: GraphQLResolveInfo,
) =>
  await _getCustomerPortal({
    args: { input: { id }, params: {}, headers: {}, files: null, query: {} },
    dataSources,
    user,
    translate,
  });
