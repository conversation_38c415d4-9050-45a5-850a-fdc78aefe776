/* eslint-disable */
// @ts-nocheck

import { Types } from "mongoose";
import Random from "random-seed-generator";
import { expect, test, vi } from "vitest";
import PAID_FEATURES from "$/settings/paid-features.json";
import { INDEX_AI_ASSISTANT_FILE_JOB } from "~/agenda/definitions";
import { NOTIFICATION_IDENTIFIERS } from "~/constants/notification-identifiers";
import {
  getAllFolderIDs,
  getFoldersFiles,
  onDocumentOperationFailure,
  removeDocument,
  validateAccess,
  validateAccessToMachine,
} from "~/datacomponents/AiAssistant/ai-assistant-data/helpers";
import { DocumentStatus as AiAssistantDocumentStatus } from "~/datamodels/AiAssistant/constants";
import { getEnums } from "~/utils";
import { mockAppConfig } from "#/tests/mocks/AppConfig";
import { PRODUCT_TYPES } from "~/constants/plans";
import { mockOemPaidFeatures } from "#/tests/mocks/Oem";

const { serverEnvironmentTag } = vi.hoisted(() => ({
  serverEnvironmentTag: "serverEnvironmentTag",
}));

vi.mock("~/environment/_server-tag", () => ({ default: serverEnvironmentTag }));

const aiAssistantID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const externalFolderID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const fileID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const fileSize =
  Number(
    Random.string({
      length: 3,
      pool: "0123456789",
    }),
  ) || 1;
const internalFileID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const internalFolderID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const key: string = Random.string({
  length: 6,
  pool: "ABCDEF",
});
const machineID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const oemID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const teamID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});
const userID: string = Random.string({
  length: 24,
  pool: "ABCDEF0123456789",
});

const paidFeaturesEnum = getEnums(PAID_FEATURES, "reference");

test("Box Folders IDs", async () => {
  const BoxGetFolder = vi.fn();
  const dataSources = {
    BoxApi: {
      getFolderById: BoxGetFolder,
    },
  };

  await Promise.all([
    getAllFolderIDs(dataSources, externalFolderID, oemID),
    getAllFolderIDs(dataSources, internalFolderID, oemID),
  ]);

  expect(BoxGetFolder).toHaveBeenCalledWith({
    boxOemUserName: `${oemID}${serverEnvironmentTag}`,
    id: externalFolderID,
  });
  expect(BoxGetFolder).toHaveBeenCalledWith({
    boxOemUserName: `${oemID}${serverEnvironmentTag}`,
    id: internalFolderID,
  });
});

test("Box Folders Files", async () => {
  const BoxGetFolder = vi.fn();
  const dataSources = {
    BoxApi: {
      getFolderById: BoxGetFolder,
    },
  };

  await getFoldersFiles(dataSources, externalFolderID, internalFolderID, oemID);

  expect(BoxGetFolder).toHaveBeenCalledWith({
    boxOemUserName: `${oemID}${serverEnvironmentTag}`,
    id: externalFolderID,
  });
  expect(BoxGetFolder).toHaveBeenCalledWith({
    boxOemUserName: `${oemID}${serverEnvironmentTag}`,
    id: internalFolderID,
  });
});

test("Ai Assistant feature access validation", async () => {
  const oemLoadOne = vi.fn();
  const userLoadOne = vi.fn();
  const appConfigLoadOne = vi.fn();
  const dataSources = {
    Oem: { loadOne: oemLoadOne },
    User: { loadOne: userLoadOne },
    AppConfig: { getOne: appConfigLoadOne },
  };

  oemLoadOne.mockReturnValue({
    paidFeatures: [],
    aiAssistantConfiguration: {
      allowedQueries: 100,
      allowedStorage: 100,
    },
  });
  appConfigLoadOne.mockReturnValue(mockAppConfig);
  userLoadOne.mockReturnValue({ productAccess: [PRODUCT_TYPES.INDUSTRIAL_AI] });

  expect(async () => await validateAccess(dataSources, oemID, userID)).rejects.toThrowError();

  expect(oemLoadOne).toHaveBeenCalledWith(new Types.ObjectId(oemID));
  expect(userLoadOne).toHaveBeenCalledWith(new Types.ObjectId(userID));

  oemLoadOne.mockReturnValue({
    paidFeatures: [paidFeaturesEnum.aiAssistants],
    aiAssistantConfiguration: {
      allowedQueries: 100,
      allowedStorage: 100,
    },
    ...mockOemPaidFeatures,
  });

  userLoadOne.mockReturnValue({ productAccess: [PRODUCT_TYPES.INDUSTRIAL_AI] });
  await validateAccess(dataSources, oemID, userID);
});

test("Machine access validation", async () => {
  const machineLoadOne = vi.fn();
  const userLoadOne = vi.fn();
  const dataSources = {
    Machine: { loadOne: machineLoadOne },
    User: { loadOne: userLoadOne },
  };

  machineLoadOne.mockReturnValue({ teams: [] });
  userLoadOne.mockReturnValue({ teams: [] });

  await validateAccessToMachine(dataSources, machineID, { id: userID });

  expect(machineLoadOne).toHaveBeenCalledWith(new Types.ObjectId(machineID));
  expect(userLoadOne).toHaveBeenCalledWith(new Types.ObjectId(userID));

  machineLoadOne.mockReturnValue({ teams: [teamID] });
  await validateAccessToMachine(dataSources, machineID, { id: userID });

  machineLoadOne.mockReturnValue({ teams: [] });
  userLoadOne.mockReturnValue({ teams: [teamID] });
  expect(
    async () =>
      await validateAccessToMachine(dataSources, machineID, { id: userID, teams: [teamID] }),
  ).rejects.toThrowError();
});

test("Document De Indexing", async () => {
  const aiAssistantFindOneAndUpdateResponse = {
    documents: { internalDocuments: [{ _id: fileID }] },
  };
  const aiAssistantLoadOneResponse = {
    createdBy: userID,
    documents: { internalDocuments: [{ _id: fileID, consumedStorage: fileSize }] },
    oem: oemID,
  };
  const oemLoadOneResponse = {};

  const aiAssistantFindOneAndUpdate = vi.fn();
  const aiAssistantLoadOne = vi.fn();
  const oemLoadOne = vi.fn();
  const userLoadOne = vi.fn();
  const userSave = vi.fn();
  const oemSave = vi.fn();
  const pubnubPublishMessage = vi.fn();
  const vectaraApiDeleteDocument = vi.fn();
  const userUpdateOne = vi.fn();

  const dataSources = {
    AiAssistant: {
      AiAssistant: { findOneAndUpdate: aiAssistantFindOneAndUpdate },
      loadOne: aiAssistantLoadOne,
    },
    Oem: { loadOne: oemLoadOne, save: oemSave },
    PubnubApi: { publishMessage: pubnubPublishMessage },
    VectaraApi: { deleteDocument: vectaraApiDeleteDocument },
    User: {
      loadOne: userLoadOne,
      save: userSave,
      updateOne: userUpdateOne,
      User: { findOne: userLoadOne },
    },
  };

  aiAssistantFindOneAndUpdate.mockReturnValue(aiAssistantFindOneAndUpdateResponse);
  aiAssistantLoadOne.mockReturnValue(aiAssistantLoadOneResponse);
  oemLoadOne.mockReturnValue(oemLoadOneResponse);
  userLoadOne.mockReturnValue({
    aiAssistantConfiguration: {
      consumedStorage: fileSize,
    },
  });
  userSave.mockReturnValue({});
  userUpdateOne.mockReturnValue({});

  await removeDocument(dataSources, {
    attrs: {
      data: { aiAssistantID, fileID, key, machineID, oemID, userId: userID },
    },
  });

  expect(userUpdateOne).toHaveBeenCalledWith(
    { _id: userID },
    {
      $inc: {
        "aiAssistantConfiguration.consumedStorage": -1 * fileSize,
      },
    },
  );
});

test("Document indexing operation failure", async () => {
  const aiAssistantFindOneAndUpdate = vi.fn();
  const pubnubPublishMessage = vi.fn();
  const dataSources = {
    AiAssistant: { AiAssistant: { findOneAndUpdate: aiAssistantFindOneAndUpdate } },
    PubnubApi: { publishMessage: pubnubPublishMessage },
  };

  vi.mock("~/utils/chat", () => ({
    getOemChannelId: vi.fn(() => "mocked-channel-id"),
  }));

  await onDocumentOperationFailure(dataSources, null, { attrs: {} });

  expect(aiAssistantFindOneAndUpdate).not.toHaveBeenCalled();
  expect(pubnubPublishMessage).not.toHaveBeenCalled();

  await onDocumentOperationFailure(dataSources, null, {
    attrs: {
      data: {
        aiAssistantID,
        externalStorageServiceDocumentID: internalFileID,
        fileID,
        key,
        machineID,
        oemID,
        userId: userID.toString(),
      },
      name: INDEX_AI_ASSISTANT_FILE_JOB,
    },
  });

  expect(aiAssistantFindOneAndUpdate).toHaveBeenCalledWith(
    {
      _id: aiAssistantID,
      [`documents.${key}`]: {
        $elemMatch: {
          _id: fileID,
          status: AiAssistantDocumentStatus.INITIATED_INDEXING,
        },
      },
    },
    {
      $set: {
        [`documents.${key}.$[element].status`]: AiAssistantDocumentStatus.INDEXING_FAILED,
      },
    },
    {
      arrayFilters: [
        {
          "element._id": fileID,
        },
      ],
      new: true,
    },
  );
  expect(pubnubPublishMessage).toHaveBeenCalledWith({
    message: {
      aiAssistantID,
      documentID: fileID,
      externalStorageServiceDocumentID: internalFileID,
      key,
      status: AiAssistantDocumentStatus.INDEXING_FAILED,
      text: NOTIFICATION_IDENTIFIERS.AI_ASSISTANT_INDEXING_PROGRESS,
      consumedStorage: undefined,
    },
    channel: "mocked-channel-id",
  });
});
