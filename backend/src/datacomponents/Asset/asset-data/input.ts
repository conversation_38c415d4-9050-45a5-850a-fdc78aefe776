export default `#graphql
  input InputAssignAssetsToParent {
    assetIds: [ID!]!
    parentId: ID!
  }

  input InputAssignMultipleAssetsToTeam {
    assets: [ID!]!
    team: ID!
  }

  input InputAssignAssetInventoryParts {
    assetId: ID!
    inventoryParts: [ID!]!
  }

  input InputAssignOwnOemMultipleAssetsToOwnOemCustomer {
    assets: [ID!]!
    customer: ID!
  }

  input InputCreateOwnOemAsset {
    assetType: String
    name: String!
    serialNumber: String!
    customer: ID
    description: String
    image: String
    thumbnail: String
    template: ID
    teams: [String]
  }

  input InputHandleOwnOemAssetQRAccess {
    assetId: ID!
    isQRCodeEnabled: Boolean!
  }

  input InputRemoveOwnOemAssetFromOwnOemCustomer {
    asset: ID!
    customer: ID!
  }

  input InputRemoveAssetInventoryPart {
    assetId: ID!
    partId: ID!
  }

  input InputUnassignAssetFromParent {
    assetId: ID!
    parentId: ID!
  }

  input InputUnassignAssetFromTeam {
    team: ID!
    asset: ID!
  }

  input InputResetAssetToTemplate {
    assetId: ID!
    fieldsToReset: [String!]!
  }

  input InputUpdateOwnOemAsset {
    _id: ID!
    name: String
    serialNumber: String
    description: String
    image: String
    thumbnail: String
    template: ID
    customFields: [InputCustomField]
    isQRCodeEnabled: Boolean
    assetType: ID
  }
`;
