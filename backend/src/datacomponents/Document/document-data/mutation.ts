import { GraphQLResolveInfo } from "graphql";
import { ExecutionContext } from "graphql/execution/execute";
import { roles } from "~/directives";
import { IContext } from "~/types/common";
import { createDocuments as _createDocuments } from "~/services/document/create";
import { deleteDocuments as _deleteDocuments } from "~/services/document/delete";
import {
  approveOcrDocuments as _approveOcrDocuments,
  scanDocuments as _scanDocuments,
  indexDocuments as _indexDocuments,
} from "~/services/document/utils";
import { InputCreateDocuments, InputUpdateDocument } from "~/graphql/types";
import {
  updateDocument as _updateDocument,
  uploadSearchFeedback as _uploadSearchFeedback,
} from "~/services/document/update";

const { technician } = roles.is;

export const mutationTypes = `#graphql
  type Mutation {
    uploadSearchFeedback(input: InputUploadSearchFeedback): String @${technician}
    approveOcrDocuments(documentsToApprove: [String]): String @${technician}
    createDocuments(input: InputCreateDocuments): String @${technician}
    updateDocument(input: InputUpdateDocument): Document @${technician}
    deleteDocuments(documentsToDelete: [String]): String @${technician}
    indexDocuments(documentsToIndex: [String]): String @${technician}
    scanDocuments(documentsToScan: [String]): String @${technician}
  }
`;

export const mutationResolvers = {
  Mutation: {
    approveOcrDocuments: async (
      _: ExecutionContext["contextValue"],
      {
        documentsToApprove,
      }: {
        documentsToApprove: string[];
      },
      { dataSources, user, req: { t: translate } }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      await _approveOcrDocuments({
        args: { input: { documentsToApprove }, files: null, headers: {}, query: {}, params: {} },
        user,
        dataSources,
        translate,
      }),

    createDocuments: async (
      _: ExecutionContext["contextValue"],
      {
        input,
      }: {
        input: InputCreateDocuments;
      },
      { dataSources, user, req: { t: translate } }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      await _createDocuments({
        args: { input, files: null, headers: {}, query: {}, params: {} },
        user,
        dataSources,
        translate,
      }),

    deleteDocuments: async (
      _: ExecutionContext["contextValue"],
      {
        documentsToDelete,
      }: {
        documentsToDelete: string[];
      },
      { dataSources, user, req: { t: translate } }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      await _deleteDocuments({
        args: { input: { documentsToDelete }, files: null, headers: {}, query: {}, params: {} },
        user,
        dataSources,
        translate,
      }),

    indexDocuments: async (
      _: ExecutionContext["contextValue"],
      {
        documentsToIndex,
      }: {
        documentsToIndex: string[];
      },
      { dataSources, user, req: { t: translate } }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      await _indexDocuments({
        args: { input: { documentsToIndex }, files: null, headers: {}, query: {}, params: {} },
        user,
        dataSources,
        translate,
      }),

    scanDocuments: async (
      _: ExecutionContext["contextValue"],
      {
        documentsToScan,
      }: {
        documentsToScan: string[];
      },
      { dataSources, user, req: { t: translate } }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      await _scanDocuments({
        args: { input: { documentsToScan }, files: null, headers: {}, query: {}, params: {} },
        user,
        dataSources,
        translate,
      }),

    updateDocument: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputUpdateDocument },
      { dataSources, user, req: { t: translate } }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      await _updateDocument({
        // @ts-ignore
        args,
        user,
        dataSources,
        translate,
      }),

    uploadSearchFeedback: async (
      _: ExecutionContext["contextValue"],
      args: { input: InputUpdateDocument },
      { dataSources, user, req: { t: translate } }: IContext,
      __: GraphQLResolveInfo,
    ) =>
      await _uploadSearchFeedback({
        // @ts-ignore
        args,
        user,
        dataSources,
        translate,
      }),
  },
};
