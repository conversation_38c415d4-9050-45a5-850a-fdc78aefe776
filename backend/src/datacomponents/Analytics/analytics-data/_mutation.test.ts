import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { Types } from "mongoose";
import { mutationResolvers } from "./_mutation";
import { IUser } from "~/datamodels/User/interface";
import { IContext } from "~/types/common";

const { Mutation } = mutationResolvers;

const REPORT_ID = "6499a52b5dfe76648ff67a97";
const OEM_ID = new Types.ObjectId("6499a52b5dfe76648ff67a81");
const OEM_ID_1 = "64d1db10aa1d4f0613a9d7d7";

const user: IUser = {
  organization: OEM_ID,
  username: "User",
  id: "1",
};

const dataSources = {
  Report: {
    getMany: vi.fn(),
    getOne: vi.fn(),
    getById: vi.fn(),
    softDeleteById: vi.fn(),
    save: vi.fn(),
  },
  Machine: {
    getById: vi.fn(),
    getMany: vi.fn(),
    getOne: vi.fn(),
    countDocuments: vi.fn(),
    aggregate: vi.fn(),
  },
  Ticket: {
    getMany: vi.fn(),
    aggregate: vi.fn(),
    countDocuments: vi.fn(),
  },
  Customer: {
    getMany: vi.fn(),
    aggregate: vi.fn(),
    countDocuments: vi.fn(),
  },
  User: {
    getById: vi.fn(),
    getMany: vi.fn(),
  },
  Team: {
    getMany: vi.fn(),
  },
  Oem: {
    getOne: vi.fn(),
  },
  CustomAdditionalField: {
    getMany: vi.fn(),
  },
  AppConfig: {
    getOne: vi.fn(),
  },
};

const mockContext: IContext = {
  user,
  dataSources,
};

describe("Mutation Analytics", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("createOwnOemReport", () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    test('should fetch custom fields and save a report for "Work Orders"', async () => {
      const input = {
        entity: "workOrder",
        chartType: "horizontalBar",
        segment: {
          label: "None",
          value: "none",
        },
      };

      dataSources.AppConfig.getOne.mockResolvedValue({ features: [] });
      dataSources.Report.save.mockResolvedValue({ id: "report-id" });

      const result = await Mutation.createOwnOemReport({}, { input }, mockContext);

      expect(dataSources.Report.save).toHaveBeenCalledWith({
        ...input,
        title: "New work order report",
        segment: { label: "None", value: "none" },
        xAxis: { value: "WorkOrderType", label: "Work order type" },
        oem: user.organization,
      });

      expect(result).toEqual({ id: "report-id" });
    });

    test('should set xAxis based on custom fields for "Machine"', async () => {
      const input = {
        entity: "machine",
        chartType: "horizontalBar",
      };

      const customField = { _id: "custom-id", label: "Custom Label" };
      dataSources.AppConfig.getOne.mockResolvedValue({ features: [] });
      dataSources.Oem.getOne.mockResolvedValue({ paidFeatures: [] });
      dataSources.CustomAdditionalField.getMany.mockResolvedValue([customField]);
      dataSources.Report.save.mockResolvedValue({ id: "report-id" });

      const result = await Mutation.createOwnOemReport({}, { input }, mockContext);

      expect(dataSources.Report.save).toHaveBeenCalledWith({
        ...input,
        title: "New asset report",
        segment: { label: "None", value: "none" },
        xAxis: { value: "custom-id", label: "Custom Label" },
        oem: user.organization,
      });

      expect(result).toEqual({ id: "report-id" });
    });

    test("should return null if no input or entity is provided", async () => {
      const mockInputEmpty = {
        chartType: "horizontalBar",
        entity: "",
        title: "test",
      };

      let result = await Mutation.createOwnOemReport(
        null,
        { input: mockInputEmpty },
        { dataSources, user },
      );
      expect(result).toBeNull();

      result = await Mutation.createOwnOemReport(
        null,
        { input: mockInputEmpty },
        { dataSources, user },
      );
      expect(result).toBeNull();
    });
  });

  describe("updateOwnOemReport", () => {
    test("should update the report if it belongs to the user organization", async () => {
      const input = {
        _id: REPORT_ID,
        title: "test title",
        chartType: "Facility",
      };

      const mockReport = {
        _id: input._id,
        oem: OEM_ID, // Assuming this is the organization ID of the mock report
      };

      dataSources.Report.getById.mockResolvedValue(mockReport);

      const updatedReport = {
        ...mockReport,
      };
      dataSources.Report.save.mockResolvedValue(updatedReport);

      const result = await Mutation.updateOwnOemReport(null, { input }, { dataSources, user });

      expect(dataSources.Report.getById).toHaveBeenCalledWith(mockReport);

      expect(dataSources.Report.save).toHaveBeenCalledWith({
        ...input,
      });

      expect(result).toEqual(updatedReport);
    });

    test("should return null if the report does not belong to the user organization", async () => {
      // Mock input data
      const input = {
        _id: REPORT_ID,
        oem: OEM_ID,
      };

      const mockReport = {
        _id: input._id,
        oem: OEM_ID_1,
      };

      dataSources.Report.getById.mockResolvedValue(mockReport);

      const result = await Mutation.updateOwnOemReport(null, { input }, { dataSources, user });

      expect(dataSources.Report.getById).toHaveBeenCalledWith(input);
      expect(dataSources.Report.save).toHaveBeenCalled();
      expect(result).toBeUndefined();
    });
  });

  describe("deleteOwnOemReport", () => {
    test("should delete the report if it belongs to the user organization", async () => {
      // Mock report ID
      const mockReport = {
        _id: REPORT_ID,
        oem: OEM_ID, // Assuming this is the organization ID of the mock report
      };

      dataSources.Report.getById.mockResolvedValue(mockReport);

      // Call the function
      await Mutation.deleteOwnOemReport(null, { id: REPORT_ID }, { dataSources, user });

      expect(dataSources.Report.getById).toHaveBeenCalledWith(mockReport);
      expect(dataSources.Report.softDeleteById).toHaveBeenCalledWith(mockReport._id, user.id);
    });
  });
});
