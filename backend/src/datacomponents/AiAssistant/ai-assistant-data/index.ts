import inputTypes from "~/datacomponents/AiAssistant/ai-assistant-data/input";
import {
  mutationResolvers,
  mutationTypes,
} from "~/datacomponents/AiAssistant/ai-assistant-data/mutation";
import { queryResolvers, queryTypes } from "~/datacomponents/AiAssistant/ai-assistant-data/query";
import { typeResolvers, types } from "~/datacomponents/AiAssistant/ai-assistant-data/type";

export default {
  resolvers: {
    ...mutationResolvers,
    ...queryResolvers,
    ...typeResolvers,
  },
  types: `
    ${inputTypes}
    ${mutationTypes}
    ${queryTypes}
    ${types}
  `,
};
