import { stringify } from "csv-stringify";
import moment from "moment";
import { serializeAsText } from "~/utils/slate/_serializeAsText";
import {
  COLLECTIONS,
  ROW_HEADERS,
  DATE_FORMAT_CREATED_AT,
  DATE_FORMAT_SCHEDULING,
  DETAILED_ROW_HEADERS,
  DETAIL_EXPORT,
  DETAIL_EXPORT_VALIDATORS,
  DETAIL_EXPORT_CONFIG,
} from "~/datacomponents/Exporter/file-exporter/_consts";
import { throwIfError } from "~/utils";
import { IOem, IOemStatus, IOemTicketType, IOemAssetType } from "~/datamodels/Oem/interface";
import {
  IAggregationData,
  ICsvRow,
  ICustomAdditionalFieldWithIndex,
  IEnabledFeatures,
  DetailedExportType,
  OptionalDetailedExportType,
  DetailedExportDataType,
  DetailedExportDataItemType,
} from "~/datacomponents/Exporter/file-exporter/_types";
import { IProcedure } from "~/datamodels/Procedure/interface";
import { ITimeTrackerLog } from "~/datamodels/Ticket/interface";
import { IInventoryPart } from "~/datamodels/InventoryPart/interface";
import { isValidObjectId } from "~/datasources/db/_utils/_helpers";
import { toTitleCase, removeUnderscore } from "~/utils/_util-methods";
import type { IContext } from "~/types/common";
import { getPaidFeaturesArray } from "~/utils/isPaidFeatureAvailable";
import type { IPreventiveMaintenance } from "~/datamodels/PreventiveMaintenance/interface";

const { TICKET, MACHINE, CUSTOMER, INVENTORY_PART, MACHINE_HISTORY, PROCEDURE } = COLLECTIONS;
const MACHINE_TEMPLATE = "MachineTemplate";

const VALID_RESOURCES = [
  TICKET,
  MACHINE,
  CUSTOMER,
  INVENTORY_PART,
  MACHINE_HISTORY,
  MACHINE_TEMPLATE,
];

const qrAppUrl = process.env.QR_APP_URI;

const machineUrlModifier = process.env.MACHINE_URL_MODIFIER;

const getStatusLabel = (status: string | IOemStatus) => {
  let res = "";
  if (isValidObjectId(status as string)) {
    res = status.toString();
  }

  if (typeof status === "object" && status !== null) {
    res = status.label;
  }

  return toTitleCase(res as string);
};

const getBillableStatus = (isBillable: boolean | null | undefined) => {
  if (typeof isBillable !== "boolean") return "";
  return isBillable ? "Billable" : "Non-Billable";
};

const getTimeElapsed = (timeElapsedInSeconds: number) => {
  if (!timeElapsedInSeconds) return "";
  const duration = moment.duration(timeElapsedInSeconds, "seconds");
  return Math.floor(duration.asHours()) + moment.utc(duration.asMilliseconds()).format(":mm:ss");
};

const addCustomFieldInRow = (
  data: IAggregationData,
  row: ICsvRow,
  customAdditionalFields: ICustomAdditionalFieldWithIndex[],
): void => {
  const rowLength = row.length;

  if (!data.customFields?.length) {
    customAdditionalFields.forEach((_, idx) => (row[rowLength + idx] = ""));
    return;
  }

  const customFieldMap = new Map(data.customFields.map(cf => [cf.fieldId.toString(), cf]));
  customAdditionalFields.forEach((cf, idx) => {
    const cfId = cf._id.toString();
    if (customFieldMap.has(cfId)) {
      const customField = customFieldMap.get(cfId);
      row[rowLength + idx] =
        typeof customField?.value === "object" && customField?.value?.address
          ? customField.value.address
          : customField?.value;
    } else {
      row[rowLength + idx] = "";
    }
  });
};

export const getRowHeaders = (enabledFeatures: IEnabledFeatures) => ({
  ...ROW_HEADERS,
  [TICKET]: [
    ...ROW_HEADERS[TICKET],
    ...(enabledFeatures.teams ? ["Teams"] : []),
    ...(enabledFeatures.procedures ? ["Number of Procedures Attached"] : []),
  ],
  [MACHINE]: [
    ...ROW_HEADERS[MACHINE],
    ...(enabledFeatures.teams ? ["Teams"] : []),
    ...["Parent Asset ID", "Parent Asset Name", "Number of Sub Assets"],
  ],
  [CUSTOMER]: [...ROW_HEADERS[CUSTOMER], ...(enabledFeatures.teams ? ["Teams"] : [])],
  [MACHINE_HISTORY]: [...ROW_HEADERS[MACHINE_HISTORY]],
  [MACHINE_TEMPLATE]: [...ROW_HEADERS[MACHINE_TEMPLATE]],
});

const calculateResolutionTime = (data: IAggregationData): string => {
  const createdAt = data.created_at;
  const ticketClosedOn = data.closedOn;
  const oemData = data.oem[0] || { statuses: [] };

  const closedStatusLabel =
    oemData?.statuses[(oemData?.statuses?.length || 1) - 1]?.label || "Closed";

  const ticketStatus = data.status;

  const createdTime = new Date(createdAt).getTime();
  const endTime = new Date(ticketClosedOn).getTime();

  if (closedStatusLabel !== ticketStatus || endTime < createdTime) {
    return "-";
  }

  // Calculate the difference in hours
  const totalMilliseconds = endTime - createdTime;
  const totalMinutes = Math.floor(totalMilliseconds / (1000 * 60));
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  return `${hours}h ${minutes}m`;
};

const getDetailedRow = () => ({
  [DETAIL_EXPORT.INVENTORY_PARTS]: (data: { quantity: string; details: IInventoryPart }) => [
    data.details.articleNumber,
    data.details.name,
    data.quantity,
  ],
  [DETAIL_EXPORT.TIME_TRACKER_LOGS]: (data: ITimeTrackerLog & { ticketTagLabel: string }) => [
    data.description,
    data.startDateTime ? moment.utc(data.startDateTime).format(DATE_FORMAT_SCHEDULING) : "",
    data.endDateTime ? moment.utc(data.endDateTime).format(DATE_FORMAT_SCHEDULING) : "",
    getTimeElapsed(data.timeElapsedInSeconds ?? 0),
    data.ticketTagLabel,
    getBillableStatus(data.isBillable),
    data?.createdBy?.deleted ? "Deleted User" : data?.createdBy?.name || "",
  ],
  [DETAIL_EXPORT.PROCEDURES]: (data: IProcedure) => [
    data.name,
    data.procedureId,
    removeUnderscore(data.state || ""),
  ],
  [DETAIL_EXPORT.PREVENTIVE_MAINTENANCE_EVENTS]: (data: IPreventiveMaintenance) => [
    data.title,
    data.startDate,
    data.frequency,
    data.repeatInNumber,
    data.eventDate,
  ],
  [DETAIL_EXPORT.ASSET_HISTORY]: (data: any): ICsvRow => {
    const workOrderStatus = data?.oemStatuses?.find(
      // @ts-ignore
      s => s._id.toString() === data.ticket?.status?.toString(),
    )?.label;
    return [
      moment.utc(data.createdAt).format(DATE_FORMAT_CREATED_AT),
      data.eventType === TICKET ? "Work Order" : data.eventType,
      // eslint-disable-next-line no-nested-ternary
      ...(data.eventType === TICKET
        ? [
            data.ticket.title,
            data.ticket.id,
            workOrderStatus || "",
            data.ticket?.facility?.deleted ? "Deleted Facility" : data.ticket?.facility?.name,
            "",
            "",
            "",
            "",
          ]
        : data.eventType === PROCEDURE
        ? [
            data.ticket.title,
            data.ticket.id,
            workOrderStatus || "",
            "",
            data.resource.name,
            data.resource.procedureId,
            data.resource.state,
            "",
          ]
        : ["", "", "", "", "", "", "", serializeAsText(data?.note?.message || "")]),
    ];
  },
});

const detailTicketRow = <T extends DetailedExportType>(
  detailExport: T,
  detailDataItem: DetailedExportDataItemType<T>,
) => {
  const resolver = DETAIL_EXPORT_VALIDATORS[detailExport] ?? [];
  if (resolver && resolver(detailDataItem)) {
    return getDetailedRow()[detailExport](detailDataItem);
  }
  return [];
};

const buildDetailedRow = <T extends keyof typeof DETAIL_EXPORT>(
  detailExport: DetailedExportType,
  detailData: DetailedExportDataType<T>,
  baseRow: ICsvRow,
): ICsvRow[] => {
  if (detailData.length > 0) {
    // @ts-ignore
    return detailData.map(detailDataItem => [
      ...baseRow,
      ...detailTicketRow(detailExport, detailDataItem),
    ]);
  }
  const placeHolderRow = DETAILED_ROW_HEADERS[detailExport].map(_ => "");
  return [[...baseRow, ...placeHolderRow]];
};

export const getRow: {
  [x: string]: (
    data: IAggregationData,
    index: number,
    enabledFeatures: IEnabledFeatures,
  ) => ICsvRow;
} = {
  [TICKET]: (data: IAggregationData, index: number, enabledFeatures: IEnabledFeatures): ICsvRow => [
    index + 1,
    data.id,
    data.title,
    data.ticketType,
    data.ticketVisibility,
    data.user,
    toTitleCase(data.status),
    data.schedule?.startTime
      ? moment.utc(data.schedule.startTime).format(DATE_FORMAT_SCHEDULING)
      : "",
    data.schedule?.endTime ? moment.utc(data.schedule.endTime).format(DATE_FORMAT_SCHEDULING) : "",
    data.created_at ? moment.utc(data.created_at).format(DATE_FORMAT_CREATED_AT) : "",
    data.assignees?.join(", "),
    data.facilityId,
    data.facility,
    data.serialNumber,
    data.machine,
    data.created_at ? moment.utc(data.created_at).format(DATE_FORMAT_SCHEDULING) : "",
    calculateResolutionTime(data),
    ...(enabledFeatures.teams ? [data.teams?.join(", ")] : []),
    ...(enabledFeatures.procedures ? [data.countProceduresAttached] : []),
  ],
  [MACHINE]: (
    data: IAggregationData,
    index: number,
    enabledFeatures: IEnabledFeatures,
  ): ICsvRow => [
    index + 1,
    data.serialNumber,
    data.name,
    data.assetType || "-",
    data.facilityId || "-",
    data.facility || "-",
    data.countPartsAttached || 0,
    data.countPreventiveMaintenanceEvents || 0,
    `${qrAppUrl}/${machineUrlModifier}/${data.uuid}`,
    data.templateId,
    ...(enabledFeatures.teams ? [data.teams?.join(", ") || "-"] : []),
    data.hierarchy?.[0]?.serialNumber || "-",
    data.hierarchy?.[0]?.name || "-",
    data.totalChildrenCount || 0,
  ],
  [CUSTOMER]: (
    data: IAggregationData,
    index: number,
    enabledFeatures: IEnabledFeatures,
  ): ICsvRow => [
    index + 1,
    data.facilityIdentifier,
    data.name,
    data.countMachinesAttached,
    ...(enabledFeatures.teams ? [data.teams?.join(", ")] : []),
  ],
  [INVENTORY_PART]: (data: IAggregationData, index: number): ICsvRow => [
    index + 1,
    data.articleNumber,
    data.name,
  ],
  [MACHINE_HISTORY]: (data: IAggregationData, index: number): ICsvRow => [
    index + 1,
    moment.utc(data.createdAt).format(DATE_FORMAT_CREATED_AT),
    data.type === TICKET ? "Work Order" : data.type,
    // eslint-disable-next-line no-nested-ternary
    ...(data.type === TICKET
      ? [
          data.resource.title,
          data.resource.id,
          getStatusLabel(data.resource.status),
          data.resource.facility?.deleted ? "Deleted Facility" : data.resource.facility?.name,
          "",
          "",
          "",
          "",
        ]
      : data.type === PROCEDURE
      ? [
          data.ticket.title,
          data.ticket.id,
          "",
          "",
          data.resource.name,
          data.resource.procedureId,
          data.resource.state,
          "",
        ]
      : ["", "", "", "", "", "", "", serializeAsText(data.note.message)]),
  ],
  [MACHINE_TEMPLATE]: (data: IAggregationData, index: number): ICsvRow => [
    index + 1,
    data.templateId,
    data.title,
  ],
};

export const generateCsv = async (rows: ICsvRow[]) =>
  new Promise((resolve, reject) => {
    stringify(rows, function (err, output) {
      if (err) {
        reject(err);
      } else {
        resolve(output);
      }
    });
  });

export const validateGetDataCsvParams = ({
  resource,
  uuid,
  detailExport,
}: {
  resource: string;
  uuid: string;
  detailExport: OptionalDetailedExportType;
}) => {
  if (!resource) throwIfError("`resource` is required");
  else if (!uuid) throwIfError("`uuid` is required");
  else if (!VALID_RESOURCES.includes(resource)) throwIfError(`${resource} is not a valid resource`);
  else if (!!detailExport && !Object.values(DETAIL_EXPORT).includes(detailExport))
    throwIfError(`${detailExport} detail is not supported`);
};

export const getRows = async ({
  oem,
  data,
  resource,
  customAdditionalFields,
  detailExport,
  dataSources,
}: {
  oem: IOem;
  data: IAggregationData[];
  resource: string;
  customAdditionalFields: ICustomAdditionalFieldWithIndex[];
  detailExport: OptionalDetailedExportType;
  dataSources: IContext["dataSources"];
}) => {
  const ticketStatusDictionary: { [x: string]: IOemStatus } = {};
  const customFieldDictionary: { [x: string]: ICustomAdditionalFieldWithIndex } = {};
  const ticketTypeDictionary: { [x: string]: IOemTicketType } = {};
  const assetTypeDictionary: { [x: string]: IOemAssetType } = {};

  const appConfig = await dataSources.AppConfig.getOne();

  const paidFeatures = getPaidFeaturesArray(oem, appConfig);

  const enabledFeatures = {
    teams: paidFeatures!.includes("teams"),
    procedures: paidFeatures!.includes("procedures"),
  };
  const hasDetailExport =
    !!detailExport && Object.prototype.hasOwnProperty.call(DETAIL_EXPORT_CONFIG, resource);

  customAdditionalFields.forEach((cf, index: number) => {
    cf.index = index;
    if (cf?._id) {
      customFieldDictionary[cf._id.toString()] = cf;
    }
  });

  oem?.statuses?.forEach((status: IOemStatus) => {
    ticketStatusDictionary[status?._id?.toString()] = status;
  });

  oem?.ticketTypes?.forEach((type: IOemTicketType) => {
    ticketTypeDictionary[type?._id?.toString()] = type;
  });

  oem?.assetTypes?.forEach((type: IOemAssetType) => {
    assetTypeDictionary[type?._id?.toString()] = type;
  });

  data.forEach(record => {
    if (record?.customFields?.length) {
      record.customFields = record.customFields.filter(
        cf => customFieldDictionary[cf.fieldId?.toString()],
      );
      record.customFields.forEach(cf => {
        const fieldId = cf.fieldId?.toString();
        if (customFieldDictionary[fieldId]) {
          cf.label = customFieldDictionary[fieldId].label;
          cf.index = customFieldDictionary[fieldId].index;
        }
      });
    }
    if (record?.status) {
      record.status = ticketStatusDictionary[record.status]?.label;
    }

    if (isValidObjectId(record?.resource?.status as string)) {
      record.resource.status = ticketStatusDictionary[record.resource.status.toString()];
    }

    if (record?.ticketType) {
      record.ticketVisibility = ticketTypeDictionary[record.ticketType]?.isInternal
        ? "Internal"
        : "External";
      record.ticketType = ticketTypeDictionary[record.ticketType]?.name;
    }

    if (record?.assetType) {
      record.assetType = assetTypeDictionary[record.assetType]?.name;
    }
  });

  const rows: ICsvRow[] = [];

  const rowHeaders = [
    ...getRowHeaders(enabledFeatures)[resource],
    ...([TICKET, MACHINE, MACHINE_TEMPLATE].includes(resource) && detailExport
      ? DETAILED_ROW_HEADERS[detailExport]
      : []),
    ...customAdditionalFields.map(cf => cf.label),
  ];

  rows.push(rowHeaders);

  data.forEach((d: IAggregationData, index: number) => {
    const baseRow = getRow[resource](d, index, enabledFeatures);

    const detailedRows = hasDetailExport
      ? buildDetailedRow(detailExport, d[detailExport] || [], baseRow)
      : [baseRow];

    detailedRows.forEach(row => {
      addCustomFieldInRow(d, row, customAdditionalFields);
      const rowWithPlaceholders = row.map(val => val || (typeof val === "number" ? 0 : "-"));
      rows.push(rowWithPlaceholders);
    });
  });

  return rows;
};
