export default `
  input InputReportField {
    value: Mixed
    label: String
  }
  
  input InputReportFilter {
    filterType: String
    field: String
    condition: String
    value: [InputReportField]
  }
  
  input InputCreateOwnOemReport {
    entity: String!
  }

  input InputUpdateOwnOemReport {
    _id: ID!
    title: String
    filters: [InputReportFilter]
    xAxis: InputReportField
    table: [InputReportField]
    segment: InputReportField
    chartType: String
  }

  input InputDuplicateOwnOemReport {
    _id: ID!
    title: String
  }

  input InputGetAnalytics {
    _id: ID
    filters: [InputReportFilter]
    xAxis: InputReportField
    table : [InputReportField]
    segment: InputReportField
    entity: String
    chartType: String
    withInputTableOptions: Boolean
  }
`;
