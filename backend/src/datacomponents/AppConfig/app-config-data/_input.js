export default `
input InputTierLimit {
  type: String
  value: Float
}

input InputPlanTier {
  name: String
  type: String
  price: Float
  allowedFeatures: [String]
  limits: [InputTierLimit]
}


input InputProductPlan {
  type: String
  name: String
  tiers: [InputPlanTier]
  conflictingPurchases: [String]
}

input InputAppConfig {
  _id: ID
  features: [AppFeaturesEnum]
  plans: [InputProductPlan]
}
`;
