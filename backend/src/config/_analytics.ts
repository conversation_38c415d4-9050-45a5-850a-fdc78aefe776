export const ANALYTICS_AXIS_OPTIONS = {
  WorkOrderType: "Work order type",
  Assignee: "Assignee",
  Reporter: "Reporter",
  WorkOrderStatus: "Work order status",
  Facility: "Connection",
  AssetType: "Asset type",
  Machine: "Asset",
  Team: "Team",
};

export const FILTERS_CONDITIONS = {
  is: "Is",
  isAnyOf: "Is any of",
  isNot: "Is not",
  isEmpty: "Is empty",
  isNotEmpty: "Is not empty",
};

export const getFiltersConditions = (count: number) => {
  if (count > Object.keys(FILTERS_CONDITIONS).length) {
    count = Object.keys(FILTERS_CONDITIONS).length;
    const conditionsArray = Object.values(FILTERS_CONDITIONS);
    return conditionsArray.slice(0, count);
  }
  return Object.values(FILTERS_CONDITIONS).slice(0, count);
};

export const ANALYTICS_FILTERS = {
  workOrder: [
    {
      filterType: "Date",
      field: "Work Order Created",
      condition: getFiltersConditions(2),
      value: [
        {
          label: "Today",
          value: "Today",
        },
        {
          label: "Yesterday",
          value: "Yesterday",
        },
        {
          label: "This week",
          value: "This week",
        },
        {
          label: "Last week",
          value: "Last week",
        },
        {
          label: "This month",
          value: "This month",
        },
        {
          label: "Last month",
          value: "Last month",
        },
        {
          label: "This year",
          value: "This year",
        },
        {
          label: "Last year",
          value: "Last year",
        },
        {
          label: "Month to date",
          value: "Month to date",
        },
        {
          label: "Year to date",
          value: "Year to date",
        },
        {
          label: "Custom",
          value: "Custom",
        },
      ],
    },
    {
      filterType: "ID",
      field: "Connection",
      condition: getFiltersConditions(3),
      value: [],
    },
    {
      filterType: "ID",
      field: "Assignee",
      condition: getFiltersConditions(5),
      value: [],
    },
    {
      filterType: "ID",
      field: "Status",
      condition: getFiltersConditions(3),
      value: [],
    },
    {
      filterType: "ID",
      field: "Work Order Type",
      condition: getFiltersConditions(3),
      value: [],
    },
    {
      filterType: "ID",
      field: "Reporter",
      condition: getFiltersConditions(5),
      value: [],
    },
    {
      filterType: "ID",
      field: "Team",
      condition: getFiltersConditions(5),
      value: [],
    },
  ],
  facility: [
    {
      filterType: "Date",
      field: "Connection Created",
      condition: getFiltersConditions(2),
      value: [
        {
          label: "Today",
          value: "Today",
        },
        {
          label: "Yesterday",
          value: "Yesterday",
        },
        {
          label: "This week",
          value: "This week",
        },
        {
          label: "Last week",
          value: "Last week",
        },
        {
          label: "This month",
          value: "This month",
        },
        {
          label: "Last month",
          value: "Last month",
        },
        {
          label: "This year",
          value: "This year",
        },
        {
          label: "Last year",
          value: "Last year",
        },
        {
          label: "Month to date",
          value: "Month to date",
        },
        {
          label: "Year to date",
          value: "Year to date",
        },
        {
          label: "Custom",
          value: "Custom",
        },
      ],
    },
    {
      filterType: "ID",
      field: "Team",
      condition: getFiltersConditions(5),
      value: [],
    },
  ],
  machine: [
    {
      filterType: "Date",
      field: "Asset Created",
      condition: getFiltersConditions(2),
      value: [
        {
          label: "Today",
          value: "Today",
        },
        {
          label: "Yesterday",
          value: "Yesterday",
        },
        {
          label: "This week",
          value: "This week",
        },
        {
          label: "Last week",
          value: "Last week",
        },
        {
          label: "This month",
          value: "This month",
        },
        {
          label: "Last month",
          value: "Last month",
        },
        {
          label: "This year",
          value: "This year",
        },
        {
          label: "Last year",
          value: "Last year",
        },
        {
          label: "Month to date",
          value: "Month to date",
        },
        {
          label: "Year to date",
          value: "Year to date",
        },
        {
          label: "Custom",
          value: "Custom",
        },
      ],
    },
    {
      filterType: "ID",
      field: "Asset Type",
      condition: getFiltersConditions(5),
      value: [],
    },
    {
      filterType: "ID",
      field: "Team",
      condition: getFiltersConditions(5),
      value: [],
    },
  ],
};

export const ANALYTICS_TABLE_OPTIONS = {
  workOrder: {
    id: "ID",
    title: "WO Title",
    workOrderType: "Work order type",
    status: "Status",
    facility: "Connection",
    asset: "Asset",
    reporter: "Reporter",
    team: "Team",
    assignee: "Assignee",
    procedures: "Procedures",
    workOrderCreated: "Work order created",
  },
  facility: {
    id: "ID",
    facilityName: "Facility name",
    team: "Team",
    facilityCreated: "Facility created at",
  },
  connection: {
    id: "ID",
    facilityName: "Connection name",
    team: "Team",
    facilityCreated: "Connection created at",
  },
  machine: {
    sn: "SN",
    assetName: "Asset name",
    facility: "Connection",
    team: "Team",
    assetCreated: "Asset created",
    assetType: "Asset type",
  },
};
