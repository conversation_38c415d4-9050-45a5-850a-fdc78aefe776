import { Job } from "agenda";
import { Sort } from "mongodb";
import { definitions } from "~/agenda/definitions";
import { agenda } from "~/config";
import { IContext } from "~/types/common";
import logger from "~/utils/logger";

const onError = async (dataSources: IContext["dataSources"], err: Error, job: Job) => {
  const { maxRetries, onFailure } = definitions[job.attrs.name];

  logger.info(`[AGENDA][ERROR] Job Fail Reason: ${job.attrs.failReason}`);

  if (maxRetries === job.attrs.failCount) {
    onFailure?.(dataSources, err, job);
    logger.info(
      `[AGENDA][DONE] Maximum ${maxRetries} retries reached while running job "${job.attrs.name}"`,
    );
  } else {
    job.attrs.nextRunAt = new Date(new Date().getTime() + 1000 * (2 * 2 ** job.attrs.failCount!));
    logger.info(
      `[AGENDA][RETRY] Scheduled next run for job "${job.attrs.name}" at "${job.attrs.nextRunAt}"`,
    );
    await job.save();
  }
};

export const defineJob = ({
  dataSources,
  name,
  processedDefinitions,
}: {
  dataSources: IContext["dataSources"];
  name: string;
  processedDefinitions: Set<string>;
}) => {
  if (!processedDefinitions.has(name)) {
    const { maxRetries, config = {}, func, interval } = definitions[name];

    agenda.define(name, config, (_job: Job) => func(dataSources, _job));
    processedDefinitions.add(name);

    if (interval) agenda.every(interval, name);
    if (maxRetries) agenda.on("fail", (err, job: Job) => onError(dataSources, err, job));
  }
};

export const getJobsByDataAttributeFilters = (
  filters: { [key: string]: string },
  sort: Sort = {},
  limit = 0,
  skip = 0,
) =>
  agenda.jobs(
    Object.entries(filters).reduce(
      (filter, [key, value]) => ({
        ...filter,
        [`data.${key}`]: value,
      }),
      {},
    ),
    sort,
    limit,
    skip,
  );
