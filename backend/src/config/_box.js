import BoxSDK from "box-node-sdk";

export const boxConfig = {
  boxAppSettings: {
    clientID: process.env.BOX_CLIENT_ID,
    clientSecret: process.env.BOX_CLIENT_SECRET,
    appAuth: {
      publicKeyID: process.env.BOX_PUBLIC_KEY_ID,
      privateKey: process.env.BOX_PRIVATE_KEY.replace(/\\n/gm, "\n"), // ref: https://github.com/auth0/node-jsonwebtoken/issues/642#issuecomment-585173594
      passphrase: process.env.BOX_PASSPHRASE,
    },
  },
  enterpriseID: process.env.BOX_ENTERPRISE_ID,
};

const sdk = new BoxSDK({
  clientID: process.env.BOX_CLIENT_ID,
  clientSecret: process.env.BOX_CLIENT_SECRET,
  appAuth: {
    keyID: process.env.BOX_PUBLIC_KEY_ID,
    privateKey: process.env.BOX_PRIVATE_KEY.replace(/\\n/gm, "\n"),
    passphrase: process.env.BOX_PASSPHRASE,
  },
});

export const getBoxClient = () => sdk.getAppAuthClient("enterprise", process.env.BOX_ENTERPRISE_ID);
