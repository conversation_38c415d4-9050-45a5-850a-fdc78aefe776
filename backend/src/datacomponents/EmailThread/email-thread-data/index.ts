import {
  queryTypes,
  listEmailThreadsByFolder,
  listEmailThreadsByTicket,
  getEmailThread,
} from "~/datacomponents/EmailThread/email-thread-data/query";
import types from "~/datacomponents/EmailThread/email-thread-data/type";

export default {
  resolvers: {
    Query: {
      listEmailThreadsByFolder,
      getEmailThread,
      listEmailThreadsByTicket,
    },
  },
  types: `
      ${queryTypes}
      ${types}
    `,
};
