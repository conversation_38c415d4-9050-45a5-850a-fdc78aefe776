name: Deploy Templates
on:
  push:
    branches:
      - develop
      - main
      - dev
    paths:
      - "templates/**"
jobs:
  deploy:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: templates
    environment:
      name: ${{ (github.ref == 'refs/heads/main' && 'Release') || (github.ref == 'refs/heads/develop' && 'Alpha') }}
    env:
      MAILGUN_API_KEY: ${{ secrets.MAILGUN_API_KEY }}
      MAILGUN_DOMAIN: ${{ vars.MAILGUN_DOMAIN }}
    strategy:
      matrix:
        node-version: [16.x]
    steps:
      - name: Get Code
        uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Clean install dependencies
        run: |
          npm ci
          npm test

      - name: Deploy templates
        run: npm run deploy
