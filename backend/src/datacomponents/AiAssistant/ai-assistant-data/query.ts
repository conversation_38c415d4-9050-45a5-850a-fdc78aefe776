import { UserInputError } from "apollo-server-express";
import { GraphQLResolveInfo } from "graphql";
import type { ExecutionContext } from "graphql/execution/execute";
import { FilterQuery, Types } from "mongoose";
import {
  getAiAssistantWithValidations,
  validateAccess,
} from "~/datacomponents/AiAssistant/ai-assistant-data/helpers";
import { IAiAssistant } from "~/datamodels/AiAssistant/interface";
import { AiAssistantDefaultConfigurationValues } from "~/datamodels/Oem/constants";
import { features, roles } from "~/directives";
import { QueryQueryAiAssistantArgs } from "~/graphql/types";
import { IMachine } from "~/datamodels/Machine/interface";
import { IContext } from "~/types/common";
import { belongsToUserTeams, logger } from "~/utils";
import { getOemAIQuota } from "~/utils/isPaidFeatureAvailable";
import { getSharedAssistants } from "~/services/aiAssistant/fetch";

const { technician } = roles.is;
const { aiAssistants: aiAssistantsFeature } = features.oemOwns;

export const queryTypes = `#graphql
  type Query {
    getAiAssistant(id: ID): AiAssistant @${technician} @${aiAssistantsFeature}
    listAiAssistants(limit: Int, searchQuery: String): AiAssistantWithCount @${technician} @${aiAssistantsFeature}
    listSharedAiAssistants(params: InputQueryParams): PaginatedAssistants @${technician}
    queryAiAssistant(input: InputQueryAiAssistant!): AiAssistantQueryResponse @${technician} @${aiAssistantsFeature}
  }
`;

export const queryResolvers = {
  Query: {
    getAiAssistant: async (
      _: ExecutionContext["contextValue"],
      { id }: { id: string },
      { dataSources, user }: IContext,
      ___: GraphQLResolveInfo,
    ) => {
      const { aiAssistant, isSharedAsset: isSharedAssistant } = await getAiAssistantWithValidations(
        id,
        dataSources,
        user,
        false,
        false,
        true,
      );

      if (!(aiAssistant && !aiAssistant.deleted))
        throw new UserInputError(
          `User ${user.id?.toString()} cannot view Ai Assistant ${id} because it's deleted`,
        );

      return { ...aiAssistant, isSharedAssistant };
    },

    listAiAssistants: async (
      _: ExecutionContext["contextValue"],
      { searchQuery }: { searchQuery: string },
      { dataSources, user: _user }: IContext,
      ___: GraphQLResolveInfo,
    ) => {
      const aiAssistantsFilter: FilterQuery<IAiAssistant> = {
        oem: new Types.ObjectId(_user.organization),
        deleted: false,
      };

      const { user } = await validateAccess(
        dataSources,
        _user.organization?.toString()!,
        _user.id?.toString()!,
        false,
        false,
      );

      if (user.teams?.length) {
        const machines: Array<IMachine> = await dataSources.Machine.getMany({
          oem: _user.organization,
          // @ts-ignore
          ...belongsToUserTeams(user),
        });
        aiAssistantsFilter.$or = [
          { machineID: { $in: machines.map(({ _id }) => _id) } },
          { $and: [{ templateId: { $exists: true } }, { templateId: { $ne: null } }] },
        ];
      }

      if (searchQuery) {
        aiAssistantsFilter.assistantName = { $regex: searchQuery, $options: "i" };
      }

      const [defaultAiAssistant, aiAssistants, totalCount]: [
        IAiAssistant,
        Array<IAiAssistant>,
        number,
      ] = await Promise.all([
        dataSources.AiAssistant.getOne({
          oem: null,
          ...(searchQuery ? { assistantName: aiAssistantsFilter.assistantName } : {}),
        }),
        dataSources.AiAssistant.getMany(aiAssistantsFilter),
        dataSources.AiAssistant.countDocuments(aiAssistantsFilter),
      ]);

      return {
        aiAssistants: defaultAiAssistant ? [defaultAiAssistant, ...aiAssistants] : aiAssistants,
        totalCount,
      };
    },

    listSharedAiAssistants: async (
      _: ExecutionContext["contextValue"],
      { params }: { params: { skip: number; limit: number } },
      { dataSources, user, req: { t: translate } }: IContext,
      ___: GraphQLResolveInfo,
    ) =>
      await getSharedAssistants({
        args: { params, headers: {}, query: {}, files: null, input: {} },
        user,
        dataSources,
        translate,
      }),

    queryAiAssistant: async (
      _: ExecutionContext["contextValue"],
      { input: { chatID, dbChatId, id, query } }: QueryQueryAiAssistantArgs,
      { dataSources, user }: IContext,
      ___: GraphQLResolveInfo,
    ) => {
      const { aiAssistant } = await getAiAssistantWithValidations(
        id,
        dataSources,
        user,
        false,
        false,
        true,
      );

      if (!aiAssistant || aiAssistant.deleted)
        throw new UserInputError(`User ${user.id?.toString()} cannot query Ai Assistant ${id}`);

      const oem = await dataSources.Oem.loadOne(aiAssistant.oem || user.organization);

      const oemAIQuota = await getOemAIQuota(dataSources, oem);

      const maybeUpdatedUser = await dataSources.AiAssistantUsage.AiAssistantUsage.findOneAndUpdate(
        {
          user: user.id,
          oem: aiAssistant.oem || user.organization,
          $or: [
            {
              consumedQueries: {
                $lt:
                  oemAIQuota?.allowedQueries ||
                  AiAssistantDefaultConfigurationValues.allowedQueries,
              },
            },
          ],
        },
        {
          $inc: {
            consumedQueries: 1,
          },
        },
        { new: true },
      );

      if (!maybeUpdatedUser)
        throw new UserInputError(`Quota exceeded for User ${user.id!.toString()}`);

      try {
        const response = await dataSources.VectaraApi.performQueryOnAssistant(
          aiAssistant._id,
          chatID,
          query,
        );

        if (dbChatId) {
          await dataSources.AiAssistantChat.updateOne(
            { _id: dbChatId },
            {
              $set: {
                chatId: response.chat_id,
              },
            },
          );
        }

        return response;
      } catch (err) {
        logger.error(err);
        await dataSources.AiAssistantUsage.save({
          user: user.id,
          oem: aiAssistant.oem || user.organization,
          $inc: {
            consumedQueries: -1,
          },
        });
        throw err;
      }
    },
  },
};
